"""
Setup script for Video Search Indexer

This script helps with installation and dependency management.
"""

import subprocess
import sys
from pathlib import Path


def install_dependencies():
    """Install Python dependencies from requirements.txt."""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ])
        print("✅ Python dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def check_ffmpeg():
    """Check if FFmpeg is installed."""
    print("🔍 Checking for FFmpeg...")

    try:
        subprocess.run(['ffmpeg', '-version'],
                      capture_output=True, check=True)
        print("✅ FFmpeg is installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  FFmpeg not found")
        print("Please install FFmpeg:")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  Windows: Download from https://ffmpeg.org/download.html")
        return False


def check_ai_dependencies():
    """Check if AI dependencies are working."""
    print("🤖 Checking AI dependencies...")

    try:
        import torch
        import open_clip
        print("✅ AI libraries (torch, open-clip) are installed")

        # Test CLIP model loading
        print("🔍 Testing CLIP model initialization...")
        model, _, preprocess = open_clip.create_model_and_transforms('ViT-B-32', pretrained='openai')
        print("✅ CLIP model loads successfully")

        # Check device availability
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"💻 Using device: {device}")

        return True

    except ImportError as e:
        print(f"❌ AI dependencies missing: {e}")
        print("AI features will be disabled")
        print("To enable AI features, ensure requirements.txt includes:")
        print("  torch>=2.1.0")
        print("  open-clip-torch>=2.20.0")
        print("  transformers>=4.35.0")
        return False
    except Exception as e:
        print(f"⚠️  AI setup issue: {e}")
        print("AI features may not work properly")
        return False


def test_basic_functionality():
    """Test basic functionality of the indexer."""
    print("🧪 Testing basic functionality...")

    try:
        # Check if core files exist
        src_path = Path(__file__).parent / "src"

        required_files = [
            "indexer.py",
            "storage.py",
            "ai_analyzer.py",
            "cli.py",
            "video_processor.py"
        ]

        for filename in required_files:
            file_path = src_path / filename
            if not file_path.exists():
                print(f"❌ Required file not found: {file_path}")
                return False

        print("✅ All core files present")

        # Check config file
        config_path = Path(__file__).parent / "config" / "settings.yaml"
        if config_path.exists():
            print("✅ Configuration file present")
        else:
            print("⚠️  Configuration file not found (will use defaults)")

        # Test CLI command availability
        try:
            result = subprocess.run([
                sys.executable, '-m', 'src.cli', '--help'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                print("✅ CLI interface is working")
            else:
                print("⚠️  CLI interface may have issues")
        except Exception as e:
            print(f"⚠️  CLI test failed: {e}")

        return True

    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = ['data', 'logs', 'config']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   Created: {directory}/")
    
    print("✅ Directories created")


def setup():
    """Run the complete setup process."""
    print("🚀 Setting up Video Search Indexer with AI Support...")
    print("=" * 60)

    success = True
    ai_available = True

    # Create directories
    create_directories()

    # Install dependencies
    if not install_dependencies():
        success = False

    # Check FFmpeg
    if not check_ffmpeg():
        success = False

    # Check AI dependencies
    if not check_ai_dependencies():
        ai_available = False
        # Don't mark as failure since AI is optional

    # Test basic functionality
    if not test_basic_functionality():
        success = False

    print("\n" + "=" * 60)

    if success:
        print("✅ Setup completed successfully!")

        if ai_available:
            print("🤖 AI features are enabled and ready!")
            print("\nAvailable commands:")
            print("  python -m src.cli add-url 'https://youtube.com/watch?v=example'")
            print("  python -m src.cli process")
            print("  python -m src.cli search 'query'")
            print("  python -m src.cli ai-search 'visual query'  # 🆕 AI-powered!")
            print("  python -m src.cli list")
            print("  python -m src.cli stats")

            print("\n🎯 AI Search Examples:")
            print("  python -m src.cli ai-search 'person dancing'")
            print("  python -m src.cli ai-search 'man singing'")
            print("  python -m src.cli ai-search 'microphone'")
            print("  python -m src.cli ai-search 'outdoor scene'")
        else:
            print("⚠️  AI features are disabled (dependencies not available)")
            print("\nBasic commands available:")
            print("  python -m src.cli add-url 'https://youtube.com/watch?v=example'")
            print("  python -m src.cli process")
            print("  python -m src.cli search 'query'")
            print("  python -m src.cli list")
            print("  python -m src.cli stats")

            print("\n💡 To enable AI features, install:")
            print("  pip install torch open-clip-torch transformers")
    else:
        print("❌ Setup failed!")
        print("Please resolve the issues above before using the indexer")


if __name__ == '__main__':
    setup()
