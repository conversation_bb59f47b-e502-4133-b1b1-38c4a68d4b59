#!/usr/bin/env python3
"""
Test script to verify adult content processing through the main video processor.
This tests that yt-dlp can properly download and process adult content videos.
"""

import sys
from pathlib import Path
sys.path.append('src')

from video_processor import VideoProcessor
from storage import VideoStorage
import tempfile
import shutil

def test_adult_content_processing():
    """Test processing adult content URL with yt-dlp."""
    
    print("🔞 Testing adult content processing with yt-dlp...")
    
    # Initialize video processor
    processor = VideoProcessor()
    
    # Test URL
    url = "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie"
    
    # Create temporary output directory
    temp_dir = Path(tempfile.mkdtemp(prefix='adult_test_'))
    print(f"📁 Using temporary directory: {temp_dir}")
    
    try:
        # Test screenshot extraction
        print("📸 Extracting screenshots...")
        screenshots = processor.extract_screenshots(url, temp_dir)
        
        print(f"✅ Successfully extracted {len(screenshots)} screenshots")
        
        # Display screenshot info
        for i, screenshot in enumerate(screenshots):
            print(f"  Screenshot {i+1}: {screenshot}")
            
        # Test metadata extraction
        print("📋 Extracting metadata...")
        metadata = processor.get_video_info(url)
        
        if metadata:
            print("✅ Successfully extracted metadata:")
            print(f"  Title: {metadata.get('title', 'N/A')}")
            print(f"  Duration: {metadata.get('duration', 'N/A')}")
            print(f"  Platform: {metadata.get('platform', 'N/A')}")
        else:
            print("❌ Failed to extract metadata")
            
        return len(screenshots) > 0 and metadata is not None
        
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")
        return False
        
    finally:
        # Clean up temporary directory
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"🧹 Cleaned up temporary directory: {temp_dir}")

def test_full_video_indexing():
    """Test full video indexing pipeline with adult content."""
    
    print("\n🔄 Testing full video indexing pipeline...")
    
    # Initialize storage and processor
    storage = VideoStorage()
    processor = VideoProcessor()
    
    url = "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie"
    
    try:
        # Check if video already exists
        existing_video = storage.get_video_by_url(url)
        if existing_video:
            print(f"📹 Video already exists in database: {existing_video['title']}")
            return True
            
        # Extract metadata first
        print("📋 Extracting metadata...")
        metadata = processor.get_video_info(url)
        
        if not metadata:
            print("❌ Failed to extract metadata")
            return False
            
        print(f"✅ Metadata extracted: {metadata['title']}")
        
        # Create video directory using URL hash as ID
        import hashlib
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        video_dir = Path('data') / 'xvideos' / url_hash
        video_dir.mkdir(parents=True, exist_ok=True)
        
        # Extract screenshots
        print("📸 Extracting screenshots...")
        screenshots = processor.extract_screenshots(url, video_dir / 'screenshots', metadata)
        
        if not screenshots:
            print("❌ Failed to extract screenshots")
            return False
            
        print(f"✅ Extracted {len(screenshots)} screenshots")
        
        # Store video in database
        print("💾 Storing video in database...")
        video_id = storage.store_video(
            url=url,
            title=metadata['title'],
            description=metadata.get('description', ''),
            duration=metadata.get('duration'),
            platform=metadata.get('platform', 'xvideos'),
            video_path=str(video_dir),
            thumbnail_path=str(screenshots[0]) if screenshots else None,
            metadata=metadata
        )
        
        print(f"✅ Video stored with ID: {video_id}")
        return True
        
    except Exception as e:
        print(f"❌ Error during full indexing: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting adult content processing tests...\n")
    
    # Test 1: Basic screenshot extraction
    test1_success = test_adult_content_processing()
    
    # Test 2: Full indexing pipeline
    test2_success = test_full_video_indexing()
    
    print(f"\n📊 Test Results:")
    print(f"  Screenshot extraction: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"  Full indexing pipeline: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Adult content processing is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
