#!/usr/bin/env python3
"""
Test script to demonstrate enhanced ad and modal handling capabilities
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from metadata_extractor import MetadataExtractor
    from browser_handler import BrowserVideoHandler
except ImportError:
    # Try absolute imports
    from src.metadata_extractor import MetadataExtractor
    from src.browser_handler import <PERSON>rowser<PERSON>ideo<PERSON><PERSON><PERSON>

def test_enhanced_ytdlp_options():
    """Test the enhanced yt-dlp configuration."""
    
    print("=== Enhanced yt-dlp Configuration Test ===")
    
    extractor = MetadataExtractor()
    
    print("✅ Enhanced yt-dlp options configured:")
    print(f"   - Age limit: {extractor.yt_dlp_opts.get('age_limit')}")
    print(f"   - Retries: {extractor.yt_dlp_opts.get('retries')}")
    print(f"   - Socket timeout: {extractor.yt_dlp_opts.get('socket_timeout')}s")
    print(f"   - Cookie file: {extractor.yt_dlp_opts.get('cookiefile')}")
    print(f"   - Skip unavailable fragments: {extractor.yt_dlp_opts.get('skip_unavailable_fragments')}")
    
    # Show extractor-specific args
    extractor_args = extractor.yt_dlp_opts.get('extractor_args', {})
    print(f"   - YouTube ad skipping: {extractor_args.get('youtube', {}).get('player_skip_ads')}")
    
    # Show enhanced headers
    headers = extractor.yt_dlp_opts.get('http_headers', {})
    print(f"   - Enhanced headers: {len(headers)} headers configured")
    print(f"     * DNT: {headers.get('DNT')}")
    print(f"     * Sec-Fetch-Dest: {headers.get('Sec-Fetch-Dest')}")

def test_browser_handler_patterns():
    """Test browser handler site patterns."""
    
    print("\n=== Browser Handler Site Patterns Test ===")
    
    handler = BrowserVideoHandler()
    
    print("✅ Site-specific patterns configured:")
    
    for site, patterns in handler.site_patterns.items():
        if site == 'generic':
            continue
            
        print(f"\n🌐 {site}:")
        print(f"   - Age modal selectors: {len(patterns['age_modal_selectors'])}")
        print(f"     * {patterns['age_modal_selectors'][:2]}...")  # Show first 2
        print(f"   - Ad skip selectors: {len(patterns['ad_skip_selectors'])}")
        print(f"     * {patterns['ad_skip_selectors'][:2]}...")   # Show first 2
        print(f"   - Video selectors: {len(patterns['video_selectors'])}")
        print(f"     * {patterns['video_selectors']}")
    
    # Show generic patterns
    generic = handler.site_patterns['generic']
    print(f"\n🔧 Generic patterns (fallback for unknown sites):")
    print(f"   - Age modal selectors: {len(generic['age_modal_selectors'])}")
    print(f"   - Ad skip selectors: {len(generic['ad_skip_selectors'])}")
    print(f"   - Video selectors: {len(generic['video_selectors'])}")

async def test_browser_automation():
    """Test browser automation capabilities."""
    
    print("\n=== Browser Automation Test ===")
    
    try:
        from playwright.async_api import async_playwright
        print("✅ Playwright is available")
        
        handler = BrowserVideoHandler()
        
        # Test with a simple video URL (not adult content for testing)
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll
        
        print(f"🧪 Testing browser automation with: {test_url}")
        
        result = await handler.extract_video_info(test_url)
        
        if result['success']:
            video_info = result['video_info']
            print("✅ Browser automation successful!")
            print(f"   - Title: {video_info.get('title', 'Not found')}")
            print(f"   - Domain: {video_info.get('domain', 'Not found')}")
            print(f"   - Video URL: {video_info.get('video_url', 'Not found')[:50]}...")
            print(f"   - Thumbnail: {video_info.get('thumbnail_url', 'Not found')[:50]}...")
        else:
            print(f"❌ Browser automation failed: {result.get('error')}")
            
    except ImportError:
        print("⚠️  Playwright not available")
        print("   Install with: pip install playwright")
        print("   Then run: playwright install chromium")

def test_metadata_extraction_flow():
    """Test the complete metadata extraction flow with fallbacks."""
    
    print("\n=== Complete Metadata Extraction Flow Test ===")
    
    extractor = MetadataExtractor()
    
    # Test URLs (including potentially problematic ones)
    test_urls = [
        {
            'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'platform': 'youtube',
            'description': 'Standard YouTube video (should work with yt-dlp)'
        },
        {
            'url': 'https://vimeo.com/148751763',
            'platform': 'vimeo', 
            'description': 'Vimeo video (should work with yt-dlp)'
        }
    ]
    
    for test_case in test_urls:
        url = test_case['url']
        platform = test_case['platform']
        description = test_case['description']
        
        print(f"\n🧪 Testing: {description}")
        print(f"   URL: {url}")
        
        try:
            result = extractor.extract_metadata(url, platform)
            
            if result.get('success'):
                print("✅ Extraction successful!")
                print(f"   - Method: {result.get('extraction_method', 'unknown')}")
                print(f"   - Title: {result.get('title', 'Not found')}")
                print(f"   - Duration: {result.get('duration', 'Not found')}")
                print(f"   - Upload date: {result.get('upload_date', 'Not found')}")
                print(f"   - Thumbnail: {result.get('thumbnail_url', 'Not found')[:50]}...")
            else:
                print(f"❌ Extraction failed: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ Exception during extraction: {e}")

def show_configuration_summary():
    """Show summary of all enhancements."""
    
    print("\n=== Configuration Summary ===")
    
    print("🚀 Enhanced Video Processing Capabilities:")
    print()
    print("1. 📺 Enhanced yt-dlp Configuration:")
    print("   - Age limit set to 99 (allows all age-restricted content)")
    print("   - Retry logic for failed downloads (5 retries)")
    print("   - Extended timeouts for slow/ad-heavy sites")
    print("   - Cookie persistence for login states")
    print("   - Enhanced browser headers for better compatibility")
    print("   - YouTube ad skipping enabled")
    print()
    print("2. 🤖 Browser Automation Fallback:")
    print("   - Handles 18+ age verification modals")
    print("   - Automatic ad detection and skipping")
    print("   - Anti-adblock bypass techniques")
    print("   - Site-specific handling patterns")
    print("   - Cookie persistence across sessions")
    print()
    print("3. 🎯 Site-Specific Patterns:")
    print("   - Pornhub, XVideos, RedTube patterns")
    print("   - Generic patterns for unknown sites")
    print("   - Configurable selectors for modals and ads")
    print()
    print("4. 🔄 Three-Tier Extraction Process:")
    print("   - Primary: Enhanced yt-dlp with ad/modal handling")
    print("   - Fallback: Platform-specific extraction")
    print("   - Final: Browser automation for complex cases")
    print()
    print("💡 This system can now handle:")
    print("   ✅ 18+ confirmation modals")
    print("   ✅ Pre-roll ads that need to be skipped")
    print("   ✅ Anti-adblock detection")
    print("   ✅ Complex JavaScript-based video players")
    print("   ✅ Login-required content (with cookie persistence)")
    print("   ✅ Adult content sites with various protection mechanisms")

if __name__ == '__main__':
    print("🚀 Testing Enhanced Ad and Modal Handling")
    print("=" * 60)
    
    # Test enhanced configuration
    test_enhanced_ytdlp_options()
    test_browser_handler_patterns()
    
    # Test browser automation (async)
    try:
        asyncio.run(test_browser_automation())
    except Exception as e:
        print(f"Browser automation test failed: {e}")
    
    # Test complete extraction flow
    test_metadata_extraction_flow()
    
    # Show summary
    show_configuration_summary()
    
    print(f"\n✅ Testing complete!")
    print(f"The system is now equipped to handle complex video sites with:")
    print(f"   - Age verification modals")
    print(f"   - Pre-roll advertisements") 
    print(f"   - Anti-adblock detection")
    print(f"   - Adult content restrictions")
    print(f"   - Complex JavaScript interactions")
