#!/usr/bin/env python3
"""
Web Application Launcher for Video Search Indexer

This script starts the Flask web interface for the video indexing system.
Run this script to access the web interface at http://localhost:5000
"""

import os
import sys
import argparse
import webbrowser
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def main():
    """Main function to start the web application."""
    parser = argparse.ArgumentParser(description='Video Search Indexer Web Interface')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--no-browser', action='store_true', help='Don\'t open browser automatically')
    parser.add_argument('--config', default='config/settings.yaml', help='Config file path')
    
    args = parser.parse_args()
    
    # Check if config file exists
    config_path = Path(args.config)
    if not config_path.exists():
        print(f"❌ Config file not found: {config_path}")
        print("Please run setup.py first to initialize the configuration.")
        sys.exit(1)
    
    # Check if required directories exist
    required_dirs = ['data', 'logs']
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            print(f"📁 Creating directory: {dir_path}")
            dir_path.mkdir(parents=True, exist_ok=True)
    
    try:
        # Import and create the simple web app
        try:
            from src.simple_web_app import SimpleVideoIndexerApp
        except ImportError:
            from simple_web_app import SimpleVideoIndexerApp
        
        print("🚀 Starting Video Search Indexer Web Interface...")
        print(f"📍 URL: http://{args.host}:{args.port}")
        print("🔧 Press Ctrl+C to stop the server")
        print()
        
        # Create and configure the app
        web_app = SimpleVideoIndexerApp(args.config)
        
        # Open browser if requested
        if not args.no_browser and args.host in ['127.0.0.1', 'localhost']:
            url = f"http://{args.host}:{args.port}"
            print(f"🌐 Opening browser to {url}")
            webbrowser.open(url)
        
        # Run the application
        web_app.run(host=args.host, port=args.port, debug=args.debug)
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please make sure all dependencies are installed.")
        print("Run: python setup.py")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting web application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
