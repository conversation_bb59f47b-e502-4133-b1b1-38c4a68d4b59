#!/usr/bin/env python3
"""
Setup script for browser automation capabilities

Installs Playwright and sets up browser automation for handling:
- 18+ age verification modals
- Pre-roll ads that need to be skipped
- Anti-adblock detection
- Complex JavaScript-based video players
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout.strip():
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        return False

def install_playwright():
    """Install Playwright and browser dependencies."""
    
    print("=== Installing Playwright for Browser Automation ===")
    
    # Install Playwright Python package
    if not run_command(
        f"{sys.executable} -m pip install playwright",
        "Installing Playwright Python package"
    ):
        return False
    
    # Install browser binaries
    if not run_command(
        f"{sys.executable} -m playwright install chromium",
        "Installing Chromium browser"
    ):
        return False
    
    # Install system dependencies (Linux/macOS)
    if sys.platform.startswith('linux'):
        run_command(
            f"{sys.executable} -m playwright install-deps chromium",
            "Installing system dependencies for Chromium"
        )
    
    return True

def create_data_directories():
    """Create necessary data directories."""
    
    print("\n=== Creating Data Directories ===")
    
    directories = [
        'data',
        'data/cookies',
        'data/browser_cache'
    ]
    
    for directory in directories:
        path = Path(directory)
        path.mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def test_installation():
    """Test that browser automation is working."""
    
    print("\n=== Testing Browser Automation Installation ===")
    
    try:
        # Test Playwright import
        from playwright.sync_api import sync_playwright
        print("✅ Playwright import successful")
        
        # Test browser launch
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://example.com")
            title = page.title()
            browser.close()
            
            if title:
                print(f"✅ Browser automation test successful")
                print(f"   Test page title: {title}")
                return True
            else:
                print("❌ Browser automation test failed - no title")
                return False
                
    except ImportError as e:
        print(f"❌ Playwright import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Browser automation test failed: {e}")
        return False

def show_usage_examples():
    """Show usage examples for the enhanced system."""
    
    print("\n=== Usage Examples ===")
    
    print("🚀 The enhanced video indexer can now handle:")
    print()
    
    print("1. 📺 Age-Restricted Content:")
    print("   - Automatically clicks through 18+ verification modals")
    print("   - Handles 'Are you 18 or older?' confirmations")
    print("   - Bypasses age gates on adult video sites")
    print()
    
    print("2. 📱 Advertisement Handling:")
    print("   - Waits for pre-roll ads to finish")
    print("   - Automatically clicks 'Skip Ad' buttons")
    print("   - Handles multiple ad formats and timings")
    print()
    
    print("3. 🛡️ Anti-Adblock Detection:")
    print("   - Uses realistic browser headers")
    print("   - Mimics human browsing behavior")
    print("   - Bypasses basic anti-adblock measures")
    print()
    
    print("4. 🍪 Session Persistence:")
    print("   - Saves cookies between sessions")
    print("   - Maintains login states")
    print("   - Remembers age verification choices")
    print()
    
    print("📝 Example Usage:")
    print("```python")
    print("from src.metadata_extractor import MetadataExtractor")
    print()
    print("extractor = MetadataExtractor()")
    print()
    print("# This will now handle ads and modals automatically")
    print("result = extractor.extract_metadata(")
    print("    'https://example-adult-site.com/video/12345',")
    print("    'generic'")
    print(")")
    print()
    print("if result['success']:")
    print("    print(f\"Title: {result['title']}\")")
    print("    print(f\"Method: {result['extraction_method']}\")")
    print("```")

def main():
    """Main setup function."""
    
    print("🚀 Setting up Enhanced Video Indexer with Ad/Modal Handling")
    print("=" * 70)
    
    print("This setup will enable the video indexer to handle:")
    print("  ✅ 18+ age verification modals")
    print("  ✅ Pre-roll advertisements")
    print("  ✅ Anti-adblock detection")
    print("  ✅ Complex JavaScript-based video players")
    print("  ✅ Adult content sites with various protections")
    print()
    
    # Install Playwright
    if not install_playwright():
        print("❌ Failed to install Playwright. Exiting.")
        return False
    
    # Create directories
    create_data_directories()
    
    # Test installation
    if not test_installation():
        print("❌ Installation test failed. Please check the errors above.")
        return False
    
    print("\n🎉 Setup completed successfully!")
    print()
    print("The video indexer now has enhanced capabilities:")
    print("  🔧 Enhanced yt-dlp configuration with ad handling")
    print("  🤖 Browser automation fallback for complex cases")
    print("  🎯 Site-specific patterns for major video platforms")
    print("  🍪 Cookie persistence for login states")
    print()
    
    # Show usage examples
    show_usage_examples()
    
    print("\n✅ You can now process videos from sites with:")
    print("   - Age verification requirements")
    print("   - Pre-roll advertisements")
    print("   - Anti-adblock protection")
    print("   - Complex modal systems")
    print()
    print("🧪 Test the system with:")
    print("   python3 test_ad_modal_handling.py")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
