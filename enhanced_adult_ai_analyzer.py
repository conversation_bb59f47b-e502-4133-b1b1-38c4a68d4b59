#!/usr/bin/env python3
"""
Enhanced AI analyzer specifically designed for detailed adult content analysis.
Generates explicit, uncensored descriptions with comprehensive narrative flow.
"""

import sys
import os
from pathlib import Path
import json
import logging
from typing import List, Dict, Any
import torch
from PIL import Image
import numpy as np

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_analyzer import CLIPAnalyzer, BLIPCaptioner

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAdultAIAnalyzer:
    """Enhanced AI analyzer for detailed adult content analysis."""
    
    def __init__(self):
        """Initialize the enhanced analyzer."""
        self.clip_analyzer = CLIPAnalyzer()
        self.blip_captioner = BLIPCaptioner()
        
        # Adult content vocabulary for enhanced descriptions
        self.adult_vocabulary = {
            'actions': [
                'fucking', 'sucking', 'riding', 'penetrating', 'thrusting', 'pounding', 
                'drilling', 'ramming', 'slamming', 'banging', 'screwing', 'nailing',
                'deepthroating', 'gagging', 'choking', 'swallowing', 'cumming', 'orgasming',
                'moaning', 'screaming', 'begging', 'submitting', 'dominating', 'controlling',
                'spanking', 'slapping', 'grabbing', 'squeezing', 'pinching', 'biting',
                'licking', 'kissing', 'caressing', 'fondling', 'groping', 'stroking'
            ],
            'body_parts': [
                'cock', 'dick', 'penis', 'shaft', 'head', 'balls', 'testicles',
                'pussy', 'cunt', 'vagina', 'clit', 'lips', 'hole', 'slit',
                'ass', 'asshole', 'anus', 'butt', 'cheeks', 'crack',
                'tits', 'boobs', 'breasts', 'nipples', 'areolas',
                'mouth', 'throat', 'tongue', 'lips', 'face',
                'legs', 'thighs', 'hips', 'waist', 'stomach', 'back'
            ],
            'positions': [
                'doggy style', 'missionary', 'cowgirl', 'reverse cowgirl', 'sideways',
                'standing', 'bent over', 'on top', 'underneath', 'from behind',
                'face down', 'legs up', 'spread eagle', 'on knees', 'squatting'
            ],
            'descriptors': [
                'hard', 'deep', 'rough', 'gentle', 'slow', 'fast', 'intense',
                'passionate', 'wild', 'aggressive', 'dominant', 'submissive',
                'wet', 'tight', 'loose', 'stretched', 'filled', 'stuffed',
                'big', 'huge', 'massive', 'thick', 'long', 'small', 'tiny',
                'hot', 'steamy', 'sweaty', 'dirty', 'nasty', 'kinky', 'perverted'
            ],
            'clothing': [
                'naked', 'nude', 'topless', 'bottomless', 'lingerie', 'underwear',
                'bra', 'panties', 'thong', 'stockings', 'fishnets', 'garters',
                'dress', 'skirt', 'shirt', 'pants', 'shorts', 'heels', 'boots'
            ]
        }
        
    def generate_enhanced_description(self, image_path: str, frame_number: int, total_frames: int) -> Dict[str, Any]:
        """
        Generate enhanced, explicit description for a single frame.
        
        Args:
            image_path: Path to the image file
            frame_number: Current frame number (1-based)
            total_frames: Total number of frames
            
        Returns:
            Dictionary with enhanced analysis results
        """
        try:
            # Load image
            image = Image.open(image_path).convert('RGB')
            
            # Get basic BLIP description
            basic_description = self.blip_captioner.generate_caption(Path(image_path))

            # Generate CLIP embedding using analyze_image method
            clip_analysis = self.clip_analyzer.analyze_image(Path(image_path))
            embedding = clip_analysis.get('embedding', [])
            
            # Enhance the description with explicit details
            enhanced_description = self._enhance_description(basic_description, frame_number, total_frames)
            
            # Extract comprehensive tags
            comprehensive_tags = self._extract_comprehensive_tags(enhanced_description, basic_description)
            
            # Generate contextual narrative
            narrative_context = self._generate_narrative_context(frame_number, total_frames, enhanced_description)
            
            return {
                'frame_number': frame_number,
                'image_path': image_path,
                'basic_description': basic_description,
                'enhanced_description': enhanced_description,
                'narrative_context': narrative_context,
                'comprehensive_tags': comprehensive_tags,
                'embedding': embedding if isinstance(embedding, list) else (embedding.tolist() if embedding is not None else []),
                'timestamp': f"{(frame_number-1) * 10}s",  # Assuming 10-second intervals
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze frame {frame_number}: {e}")
            return {
                'frame_number': frame_number,
                'image_path': image_path,
                'error': str(e),
                'success': False
            }
    
    def _enhance_description(self, basic_description: str, frame_number: int, total_frames: int) -> str:
        """
        Enhance a basic description with explicit adult content details.
        
        Args:
            basic_description: Basic BLIP-generated description
            frame_number: Current frame number
            total_frames: Total frames for context
            
        Returns:
            Enhanced explicit description
        """
        # Convert basic terms to explicit equivalents
        enhanced = basic_description.lower()
        
        # Replace euphemisms with explicit terms
        replacements = {
            'having sex': 'fucking hard',
            'making love': 'fucking passionately',
            'intimate': 'fucking intensely',
            'together': 'fucking each other',
            'close': 'fucking deep',
            'touching': 'groping and fondling',
            'kissing': 'making out and touching',
            'embracing': 'grinding against each other',
            'lying': 'positioned for fucking',
            'sitting': 'riding and bouncing',
            'standing': 'fucking standing up',
            'on top': 'riding cock',
            'underneath': 'getting pounded',
            'behind': 'getting fucked from behind',
            'in front': 'sucking cock',
            'between': 'getting fucked by multiple cocks',
            'with': 'getting fucked by',
            'and': 'while getting fucked by'
        }
        
        for old, new in replacements.items():
            enhanced = enhanced.replace(old, new)
        
        # Add position and intensity based on frame progression
        progress = frame_number / total_frames
        
        if progress < 0.2:  # Beginning - setup/foreplay
            intensity_words = ['starting to', 'beginning to', 'getting ready for']
            position_context = 'in the opening scene'
        elif progress < 0.5:  # Early action
            intensity_words = ['actively', 'intensely', 'passionately']
            position_context = 'as the action heats up'
        elif progress < 0.8:  # Peak action
            intensity_words = ['aggressively', 'roughly', 'wildly', 'frantically']
            position_context = 'in the intense main action'
        else:  # Climax/ending
            intensity_words = ['desperately', 'climactically', 'explosively']
            position_context = 'approaching the climactic finale'
        
        # Add contextual enhancement
        if any(word in enhanced for word in ['blonde', 'woman', 'girl']):
            enhanced = f"The blonde woman is {enhanced} {position_context}"
        elif any(word in enhanced for word in ['black', 'man', 'guy']):
            enhanced = f"The black man is {enhanced} {position_context}"
        else:
            enhanced = f"In this scene, {enhanced} {position_context}"
        
        # Add explicit details based on common adult video patterns
        if 'fucking' in enhanced and 'hard' not in enhanced:
            enhanced += ' with deep, powerful thrusts'
        if 'sucking' in enhanced:
            enhanced += ' taking it deep in her throat'
        if 'riding' in enhanced:
            enhanced += ' bouncing up and down on the thick shaft'
        
        return enhanced.capitalize()
    
    def _extract_comprehensive_tags(self, enhanced_description: str, basic_description: str) -> List[str]:
        """
        Extract comprehensive tags from both enhanced and basic descriptions.
        
        Args:
            enhanced_description: Enhanced explicit description
            basic_description: Basic BLIP description
            
        Returns:
            List of comprehensive tags
        """
        tags = set()
        
        # Extract from both descriptions
        all_text = f"{enhanced_description} {basic_description}".lower()
        
        # Add vocabulary-based tags
        for category, words in self.adult_vocabulary.items():
            for word in words:
                if word in all_text:
                    tags.add(word)
        
        # Add basic descriptive tags
        basic_tags = all_text.replace(',', ' ').replace('.', ' ').split()
        for tag in basic_tags:
            if len(tag) > 2 and tag.isalpha():  # Only alphabetic tags longer than 2 chars
                tags.add(tag)
        
        # Remove common stop words but keep adult-relevant terms
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an'}
        
        filtered_tags = [tag for tag in tags if tag not in stop_words and len(tag) > 2]
        
        return sorted(list(set(filtered_tags)))
    
    def _generate_narrative_context(self, frame_number: int, total_frames: int, description: str) -> str:
        """
        Generate narrative context for the frame within the video story.
        
        Args:
            frame_number: Current frame number
            total_frames: Total frames
            description: Frame description
            
        Returns:
            Narrative context string
        """
        progress = frame_number / total_frames
        timestamp = (frame_number - 1) * 10  # Assuming 10-second intervals
        
        if progress < 0.1:
            context = f"Opening scene ({timestamp}s): {description}"
        elif progress < 0.3:
            context = f"Building intensity ({timestamp}s): {description}"
        elif progress < 0.7:
            context = f"Peak action ({timestamp}s): {description}"
        elif progress < 0.9:
            context = f"Climactic moments ({timestamp}s): {description}"
        else:
            context = f"Finale ({timestamp}s): {description}"
        
        return context
    
    def analyze_video_comprehensive(self, screenshots_dir: Path) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of all video screenshots.
        
        Args:
            screenshots_dir: Directory containing screenshots
            
        Returns:
            Comprehensive analysis results
        """
        # Find all image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_files = [
            f for f in screenshots_dir.iterdir()
            if f.is_file() and f.suffix.lower() in image_extensions
        ]
        
        if not image_files:
            logger.error(f"No image files found in: {screenshots_dir}")
            return {}
        
        # Sort files for consistent ordering
        image_files.sort()
        total_frames = len(image_files)
        
        logger.info(f"Starting comprehensive analysis of {total_frames} frames...")
        
        # Analyze each frame
        frame_analyses = []
        for i, image_file in enumerate(image_files, 1):
            logger.info(f"Analyzing frame {i}/{total_frames}: {image_file.name}")
            analysis = self.generate_enhanced_description(str(image_file), i, total_frames)
            frame_analyses.append(analysis)
        
        # Generate comprehensive video narrative
        video_narrative = self._generate_video_narrative(frame_analyses)
        
        # Generate detailed summary
        detailed_summary = self._generate_detailed_summary(frame_analyses)
        
        return {
            'frame_analyses': frame_analyses,
            'video_narrative': video_narrative,
            'detailed_summary': detailed_summary,
            'total_frames': total_frames,
            'successful_frames': sum(1 for f in frame_analyses if f.get('success', False)),
            'analysis_timestamp': str(Path().cwd())
        }
    
    def _generate_video_narrative(self, frame_analyses: List[Dict]) -> str:
        """
        Generate a comprehensive narrative of the entire video.
        
        Args:
            frame_analyses: List of frame analysis results
            
        Returns:
            Comprehensive video narrative
        """
        successful_frames = [f for f in frame_analyses if f.get('success', False)]
        
        if not successful_frames:
            return "No successful frame analyses to generate narrative."
        
        # Group frames by story progression
        total_frames = len(successful_frames)
        
        opening = successful_frames[:total_frames//4] if total_frames > 4 else successful_frames[:1]
        building = successful_frames[total_frames//4:total_frames//2] if total_frames > 4 else []
        peak = successful_frames[total_frames//2:3*total_frames//4] if total_frames > 4 else []
        climax = successful_frames[3*total_frames//4:] if total_frames > 4 else []
        
        narrative_parts = []
        
        if opening:
            narrative_parts.append(f"OPENING: {opening[0].get('enhanced_description', 'Scene begins')}")
        
        if building:
            mid_building = building[len(building)//2]
            narrative_parts.append(f"BUILDING INTENSITY: {mid_building.get('enhanced_description', 'Action intensifies')}")
        
        if peak:
            mid_peak = peak[len(peak)//2]
            narrative_parts.append(f"PEAK ACTION: {mid_peak.get('enhanced_description', 'Intense action')}")
        
        if climax:
            narrative_parts.append(f"CLIMAX: {climax[-1].get('enhanced_description', 'Scene concludes')}")
        
        return " | ".join(narrative_parts)
    
    def _generate_detailed_summary(self, frame_analyses: List[Dict]) -> Dict[str, Any]:
        """
        Generate detailed summary with explicit content analysis.
        
        Args:
            frame_analyses: List of frame analysis results
            
        Returns:
            Detailed summary dictionary
        """
        successful_frames = [f for f in frame_analyses if f.get('success', False)]
        
        # Collect all tags
        all_tags = []
        for frame in successful_frames:
            all_tags.extend(frame.get('comprehensive_tags', []))
        
        # Count tag frequency
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # Sort by frequency
        dominant_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)
        
        # Generate explicit summary
        explicit_summary = self._generate_explicit_summary(successful_frames, dominant_tags)
        
        return {
            'explicit_summary': explicit_summary,
            'dominant_tags': [tag for tag, count in dominant_tags[:20]],
            'tag_frequencies': dict(dominant_tags[:20]),
            'total_unique_tags': len(tag_counts),
            'frame_count': len(successful_frames),
            'narrative_flow': [f.get('narrative_context', '') for f in successful_frames[:10]]  # First 10 for overview
        }
    
    def _generate_explicit_summary(self, frames: List[Dict], dominant_tags: List[tuple]) -> str:
        """
        Generate explicit summary of video content.
        
        Args:
            frames: Successful frame analyses
            dominant_tags: Tag frequency tuples
            
        Returns:
            Explicit summary string
        """
        if not frames:
            return "No content to summarize."
        
        # Get most common explicit terms
        explicit_actions = [tag for tag, count in dominant_tags if tag in self.adult_vocabulary['actions']][:5]
        body_parts = [tag for tag, count in dominant_tags if tag in self.adult_vocabulary['body_parts']][:5]
        positions = [tag for tag, count in dominant_tags if tag in self.adult_vocabulary['positions']][:3]
        
        # Build explicit summary
        summary_parts = []
        
        if explicit_actions:
            summary_parts.append(f"Primary sexual activities: {', '.join(explicit_actions)}")
        
        if body_parts:
            summary_parts.append(f"Focus on: {', '.join(body_parts)}")
        
        if positions:
            summary_parts.append(f"Positions featured: {', '.join(positions)}")
        
        # Add narrative flow
        if len(frames) > 10:
            beginning = frames[0].get('enhanced_description', '')
            middle = frames[len(frames)//2].get('enhanced_description', '')
            end = frames[-1].get('enhanced_description', '')
            
            summary_parts.append(f"Story progression: Begins with {beginning[:50]}..., intensifies to {middle[:50]}..., concludes with {end[:50]}...")
        
        return " | ".join(summary_parts)

def main():
    """Main function to run enhanced adult content analysis."""
    
    # Default video directory
    video_dir = Path("data/xvideos/index/White_girl_Creampie_ubhotuk9e37")
    screenshots_dir = video_dir / "screenshots"
    
    if not screenshots_dir.exists():
        logger.error(f"Screenshots directory not found: {screenshots_dir}")
        return 1
    
    logger.info(f"Starting enhanced adult content analysis for: {video_dir}")
    
    # Initialize enhanced analyzer
    analyzer = EnhancedAdultAIAnalyzer()
    
    # Perform comprehensive analysis
    results = analyzer.analyze_video_comprehensive(screenshots_dir)
    
    if not results:
        logger.error("Analysis failed")
        return 1
    
    # Save enhanced results
    enhanced_results_file = video_dir / "enhanced_ai_analysis.json"
    try:
        with open(enhanced_results_file, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Enhanced analysis saved to: {enhanced_results_file}")
    except Exception as e:
        logger.error(f"Failed to save enhanced results: {e}")
        return 1
    
    # Print summary
    summary = results.get('detailed_summary', {})
    logger.info(f"Enhanced analysis complete:")
    logger.info(f"  Total frames analyzed: {results.get('total_frames', 0)}")
    logger.info(f"  Successful analyses: {results.get('successful_frames', 0)}")
    logger.info(f"  Unique tags extracted: {summary.get('total_unique_tags', 0)}")
    logger.info(f"  Explicit summary: {summary.get('explicit_summary', 'N/A')[:200]}...")
    
    return 0

if __name__ == "__main__":
    exit(main())
