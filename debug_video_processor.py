#!/usr/bin/env python3
"""
Debug script to test video processor AI initialization
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_video_processor():
    """Test video processor AI initialization."""
    
    print("=== Testing Video Processor AI Initialization ===")
    
    try:
        print("1. Importing VideoProcessor...")
        from video_processor import VideoProcessor
        print("✅ VideoProcessor imported successfully")
        
        print("2. Creating VideoProcessor instance...")
        processor = VideoProcessor()
        print("✅ VideoProcessor instance created")
        
        print("3. Checking AI analyzer...")
        print(f"   AI analyzer: {processor.ai_analyzer}")
        print(f"   Enable AI analysis: {processor.enable_ai_analysis}")
        
        if processor.ai_analyzer:
            print("   ✅ AI analyzer is available")
            print(f"   AI analyzer available: {processor.ai_analyzer.is_available()}")
            print(f"   BLIP captioner available: {processor.ai_analyzer.captioner.is_available()}")
        else:
            print("   ❌ AI analyzer is None")
            
            # Check AI_AVAILABLE flag
            from video_processor import AI_AVAILABLE
            print(f"   AI_AVAILABLE flag: {AI_AVAILABLE}")
            
            # Try to import directly
            try:
                from video_processor import CLIPAnalyzer
                print("   ✅ CLIPAnalyzer can be imported from video_processor")
                
                # Try to create directly
                analyzer = CLIPAnalyzer()
                print(f"   ✅ Direct CLIPAnalyzer creation successful: {analyzer.is_available()}")
            except Exception as e:
                print(f"   ❌ Direct CLIPAnalyzer creation failed: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_video_processor()
