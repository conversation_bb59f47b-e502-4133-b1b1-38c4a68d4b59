#!/usr/bin/env python3
"""
Test script to re-process existing video and verify AI integration.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.storage import VideoStorage
from pathlib import Path
import json

def test_reprocess_existing_video():
    """Test AI integration by re-processing existing video data."""
    
    # Initialize storage
    storage = VideoStorage("data/video_index.db")
    
    print("🔄 Testing AI Integration by Re-processing Existing Video")
    print("=" * 60)
    
    # Get existing video
    videos = storage.search_videos("", limit=1)
    if not videos:
        print("❌ No videos found in database")
        return
    
    video = videos[0]
    print(f"📹 Re-processing video: {video['title']}")
    print(f"📁 Video directory: {video['video_dir']}")
    
    # Check AI analysis files
    video_path = Path(video['video_dir'])
    ai_analysis_file = video_path / "ai_analysis.json"
    ai_embeddings_file = video_path / "screenshots" / "ai_embeddings.json"
    
    if not ai_analysis_file.exists():
        print("❌ AI analysis file not found")
        return
    
    if not ai_embeddings_file.exists():
        print("❌ AI embeddings file not found")
        return
    
    print("✅ AI analysis files found")
    
    # Load and display AI analysis data
    with open(ai_analysis_file, 'r') as f:
        ai_data = json.load(f)
    
    print(f"🤖 AI Analysis Summary:")
    print(f"   Success: {ai_data.get('success', False)}")
    print(f"   Auto tags: {len(ai_data.get('auto_tags', []))}")
    print(f"   Sample tags: {ai_data.get('auto_tags', [])[:5]}")
    
    # Load embeddings data
    with open(ai_embeddings_file, 'r') as f:
        embeddings_data = json.load(f)

    # embeddings_data is a list directly
    embeddings_list = embeddings_data if isinstance(embeddings_data, list) else []
    successful_embeddings = [e for e in embeddings_list if e.get('success', True)]  # Assume success if not specified

    print(f"🖼️  Embeddings Data:")
    print(f"   Total frames: {len(embeddings_list)}")
    print(f"   Successful: {len(successful_embeddings)}")
    
    # Now test the storage integration by calling _process_ai_analysis directly
    print("\n🔄 Testing AI data integration into database...")
    
    import sqlite3
    with sqlite3.connect("data/video_index.db") as conn:
        # Call the AI processing method directly
        ai_tags = storage._process_ai_analysis(conn, video['id'], video['video_dir'])
        conn.commit()
        
        print(f"✅ Processed {len(ai_tags)} AI tags into database")
        
        # Verify the data was stored
        cursor = conn.execute("SELECT COUNT(*) FROM ai_tags WHERE video_id = ?", (video['id'],))
        ai_tag_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM frame_analysis WHERE video_id = ?", (video['id'],))
        frame_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM frame_embeddings")
        embedding_count = cursor.fetchone()[0]
        
        print(f"📊 Database verification:")
        print(f"   AI tags stored: {ai_tag_count}")
        print(f"   Frame analysis records: {frame_count}")
        print(f"   Embedding records: {embedding_count}")
        
        if ai_tag_count > 0:
            cursor = conn.execute("SELECT tag FROM ai_tags WHERE video_id = ? LIMIT 5", (video['id'],))
            tags = [row[0] for row in cursor.fetchall()]
            print(f"   Sample stored tags: {tags}")
    
    # Test search with AI tags
    print("\n🔍 Testing search with AI integration...")
    updated_videos = storage.search_videos("", limit=1)
    if updated_videos:
        updated_video = updated_videos[0]
        print(f"📹 Updated video AI tags: {updated_video.get('ai_tags', [])}")
    
    print("\n✅ AI Integration Test Complete!")

if __name__ == "__main__":
    test_reprocess_existing_video()
