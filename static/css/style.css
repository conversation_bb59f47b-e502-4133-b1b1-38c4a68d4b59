/* Custom CSS for Video Search Indexer */

/* Global Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.2);
    font-weight: 600;
}

/* Video Cards */
.video-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    overflow: hidden;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    height: 180px;
    object-fit: cover;
    transition: transform 0.3s ease-in-out;
}

.video-card:hover .video-thumbnail {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.video-card:hover .video-overlay {
    opacity: 1;
}

/* Screenshot Cards */
.screenshot-card {
    transition: transform 0.2s ease-in-out;
    cursor: pointer;
}

.screenshot-card:hover {
    transform: scale(1.05);
}

.screenshot-thumbnail {
    transition: opacity 0.2s ease-in-out;
}

.screenshot-thumbnail:hover {
    opacity: 0.8;
}

/* Search Results */
.search-result-item {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.search-result-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.similarity-bar {
    height: 4px;
    border-radius: 2px;
    transition: width 0.3s ease-in-out;
}

/* Buttons */
.btn {
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

/* Badges */
.badge {
    font-weight: 500;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info,
.card.bg-danger {
    border: none;
    background: linear-gradient(135deg, var(--bs-primary) 0%, rgba(var(--bs-primary-rgb), 0.8) 100%);
}

.card.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, rgba(25, 135, 84, 0.8) 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, rgba(255, 193, 7, 0.8) 100%);
}

.card.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, rgba(13, 202, 240, 0.8) 100%);
}

/* Loading States */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
    transition: all 0.2s ease-in-out;
}

.pagination .page-link:hover {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Modal */
.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1.5rem;
}

/* Footer */
footer {
    margin-top: auto;
    background-color: var(--light-color) !important;
    border-top: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-thumbnail {
        height: 150px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .video-thumbnail {
        height: 120px;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-hover {
    transition: box-shadow 0.15s ease-in-out;
}

.shadow-hover:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}
