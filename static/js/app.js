/**
 * Main JavaScript file for Video Search Indexer
 * Handles common functionality across all pages
 */

// Global app object
window.VideoIndexer = {
    // Configuration
    config: {
        apiBaseUrl: '/api',
        refreshInterval: 30000, // 30 seconds
        maxRetries: 3
    },
    
    // State management
    state: {
        isLoading: false,
        currentPage: null,
        searchQuery: null
    },
    
    // Initialize the application
    init: function() {
        this.setupEventListeners();
        this.detectCurrentPage();
        this.initializeComponents();
        console.log('Video Indexer app initialized');
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        // Handle form submissions with loading states
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Handle AJAX errors globally
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
        
        // Handle navigation
        document.addEventListener('click', this.handleNavigation.bind(this));
        
        // Handle keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    },
    
    // Detect current page for page-specific functionality
    detectCurrentPage: function() {
        const path = window.location.pathname;
        if (path === '/') {
            this.state.currentPage = 'dashboard';
        } else if (path === '/videos') {
            this.state.currentPage = 'videos';
        } else if (path === '/search') {
            this.state.currentPage = 'search';
        } else if (path === '/add-url') {
            this.state.currentPage = 'add-url';
        } else if (path.startsWith('/video/')) {
            this.state.currentPage = 'video-detail';
        }
    },
    
    // Initialize page-specific components
    initializeComponents: function() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize image lazy loading
        this.initLazyLoading();
        
        // Initialize auto-refresh for dashboard
        if (this.state.currentPage === 'dashboard') {
            this.initDashboardRefresh();
        }
        
        // Initialize search functionality
        if (this.state.currentPage === 'search') {
            this.initSearchPage();
        }
    },
    
    // Initialize Bootstrap tooltips
    initTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // Initialize lazy loading for images
    initLazyLoading: function() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    },
    
    // Initialize dashboard auto-refresh
    initDashboardRefresh: function() {
        setInterval(() => {
            this.refreshDashboardStats();
        }, this.config.refreshInterval);
    },
    
    // Refresh dashboard statistics
    refreshDashboardStats: function() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                this.updateDashboardStats(data);
            })
            .catch(error => {
                console.warn('Failed to refresh dashboard stats:', error);
            });
    },
    
    // Update dashboard statistics in the UI
    updateDashboardStats: function(stats) {
        const updateStat = (selector, value) => {
            const element = document.querySelector(selector);
            if (element) {
                element.textContent = value || 0;
            }
        };
        
        updateStat('.bg-primary .h2', stats.total_videos);
        updateStat('.bg-success .h2', stats.processed_videos);
        updateStat('.bg-warning .h2', stats.pending_videos);
        updateStat('.bg-info .h2', stats.recent_activity);
    },
    
    // Handle form submissions with loading states
    handleFormSubmit: function(event) {
        const form = event.target;
        if (!form.matches('form')) return;
        
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn && !submitBtn.disabled) {
            this.setLoadingState(submitBtn, true);
            
            // Reset loading state after timeout (fallback)
            setTimeout(() => {
                this.setLoadingState(submitBtn, false);
            }, 10000);
        }
    },
    
    // Set loading state for buttons
    setLoadingState: function(button, isLoading) {
        if (isLoading) {
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
        } else {
            button.disabled = false;
            if (button.dataset.originalText) {
                button.innerHTML = button.dataset.originalText;
            }
        }
    },
    
    // Handle unhandled promise rejections
    handleUnhandledRejection: function(event) {
        console.error('Unhandled promise rejection:', event.reason);
        this.showNotification('An error occurred. Please try again.', 'error');
    },
    
    // Handle navigation clicks
    handleNavigation: function(event) {
        const link = event.target.closest('a[href]');
        if (link && link.href.startsWith(window.location.origin)) {
            // Add loading state for internal navigation
            const icon = link.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-arrow-clockwise spin';
                setTimeout(() => {
                    icon.className = icon.dataset.originalClass || 'bi bi-arrow-right';
                }, 1000);
            }
        }
    },
    
    // Handle keyboard shortcuts
    handleKeyboardShortcuts: function(event) {
        // Ctrl/Cmd + K for search
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            const searchInput = document.querySelector('input[type="search"], input[name="q"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close modals
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) modal.hide();
            }
        }
    },
    
    // Initialize search page functionality
    initSearchPage: function() {
        const searchForm = document.getElementById('aiSearchForm');
        if (searchForm) {
            this.setupAISearch(searchForm);
        }
    },
    
    // Setup AI search functionality
    setupAISearch: function(form) {
        const queryInput = form.querySelector('#searchQuery');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Add example query handlers
        document.querySelectorAll('.example-query').forEach(btn => {
            btn.addEventListener('click', () => {
                queryInput.value = btn.dataset.query;
                form.dispatchEvent(new Event('submit'));
            });
        });
        
        // Handle form submission
        form.addEventListener('submit', (event) => {
            event.preventDefault();
            this.performAISearch(queryInput.value.trim());
        });
    },
    
    // Perform AI search
    performAISearch: function(query) {
        if (!query) return;
        
        this.state.isLoading = true;
        this.showSearchLoading(true);
        
        fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query: query })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.displaySearchResults(data.results, data.query, data.count);
            } else {
                this.showSearchError(data.error || 'Search failed');
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            this.showSearchError('Network error occurred');
        })
        .finally(() => {
            this.state.isLoading = false;
            this.showSearchLoading(false);
        });
    },
    
    // Show/hide search loading state
    showSearchLoading: function(isLoading) {
        const submitBtn = document.querySelector('#aiSearchForm button[type="submit"]');
        const searchBtnText = submitBtn?.querySelector('.search-btn-text');
        const loadingSpinner = submitBtn?.querySelector('.loading-spinner');
        
        if (submitBtn) {
            submitBtn.disabled = isLoading;
            if (searchBtnText) {
                searchBtnText.textContent = isLoading ? 'Searching...' : 'Search';
            }
            if (loadingSpinner) {
                loadingSpinner.style.display = isLoading ? 'inline-block' : 'none';
            }
        }
    },
    
    // Display search results
    displaySearchResults: function(results, query, count) {
        const searchResults = document.getElementById('searchResults');
        const noResults = document.getElementById('noResults');
        const searchError = document.getElementById('searchError');
        const resultsContainer = document.getElementById('resultsContainer');
        const resultCount = document.getElementById('resultCount');
        const searchedQuery = document.getElementById('searchedQuery');
        
        // Hide other states
        if (noResults) noResults.style.display = 'none';
        if (searchError) searchError.style.display = 'none';
        
        if (results.length === 0) {
            if (noResults) noResults.style.display = 'block';
            return;
        }
        
        // Update search info
        if (searchedQuery) searchedQuery.textContent = query;
        if (resultCount) resultCount.textContent = count;
        
        // Clear and populate results
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
            results.forEach(result => {
                const resultElement = this.createSearchResultElement(result);
                resultsContainer.appendChild(resultElement);
            });
        }
        
        // Show results
        if (searchResults) searchResults.style.display = 'block';
    },
    
    // Create search result element
    createSearchResultElement: function(result) {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6';
        
        const similarityPercent = Math.round(result.similarity * 100);
        const similarityColor = this.getSimilarityColor(result.similarity);
        
        col.innerHTML = `
            <div class="card search-result-item h-100 shadow-sm">
                <div class="position-relative">
                    <img src="${result.screenshot_url}" class="card-img-top" alt="Screenshot" 
                         style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge" style="background-color: ${similarityColor}">
                            ${similarityPercent}% match
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title text-truncate" title="${this.escapeHtml(result.video_title)}">
                        ${this.escapeHtml(result.video_title)}
                    </h6>
                    <div class="similarity-bar mb-2" style="width: ${similarityPercent}%; background-color: ${similarityColor}"></div>
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="${result.video_detail_url}" class="btn btn-sm btn-primary">
                            <i class="bi bi-eye"></i> View Video
                        </a>
                        <a href="${result.video_url}" target="_blank" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        return col;
    },
    
    // Get similarity color based on score
    getSimilarityColor: function(similarity) {
        if (similarity >= 0.7) return '#28a745'; // Green
        if (similarity >= 0.5) return '#ffc107'; // Yellow
        if (similarity >= 0.3) return '#fd7e14'; // Orange
        return '#dc3545'; // Red
    },
    
    // Show search error
    showSearchError: function(message) {
        const searchError = document.getElementById('searchError');
        const errorMessage = document.getElementById('errorMessage');
        const searchResults = document.getElementById('searchResults');
        const noResults = document.getElementById('noResults');
        
        if (searchResults) searchResults.style.display = 'none';
        if (noResults) noResults.style.display = 'none';
        
        if (errorMessage) errorMessage.textContent = message;
        if (searchError) searchError.style.display = 'block';
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'success' ? 'alert-success' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${this.escapeHtml(message)}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.container');
        if (container) {
            container.insertAdjacentHTML('afterbegin', alertHtml);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    },
    
    // Escape HTML to prevent XSS
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    // Utility function to format numbers
    formatNumber: function(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },
    
    // Utility function to format file sizes
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    VideoIndexer.init();
});

// Export for use in other scripts
window.VideoIndexer = VideoIndexer;
