#!/usr/bin/env python3
"""
Interactive Playwright script to explore xvideos and figure out how to bypass login/access video.
This will help us understand the site structure and develop effective bypass methods.
"""

import asyncio
import logging
from playwright.async_api import async_playwright
from pathlib import Path
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class XvideosExplorer:
    def __init__(self):
        self.url = "https://www.xvideos.com/video70986409/hot_blonde_gets_fucked"
        self.findings = {
            "page_structure": {},
            "login_elements": [],
            "video_elements": [],
            "bypass_attempts": [],
            "successful_methods": []
        }
    
    async def explore_site(self):
        """Main exploration method."""
        async with async_playwright() as p:
            # Launch browser with visible window
            browser = await p.chromium.launch(
                headless=False,  # Keep visible so we can see what's happening
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-extensions',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--window-size=1920,1080',
                    '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]
            )
            
            try:
                context = await browser.new_context()
                page = await context.new_page()
                
                # Set up aggressive blocking
                await self._setup_aggressive_blocking(page)
                
                logger.info(f"🌐 Navigating to: {self.url}")
                await page.goto(self.url, wait_until='domcontentloaded', timeout=30000)
                
                # Wait for page to load
                await page.wait_for_timeout(5000)
                
                # Step 1: Analyze initial page structure
                await self._analyze_page_structure(page)
                
                # Step 2: Detect and analyze login/modal elements
                await self._detect_login_elements(page)
                
                # Step 3: Try different bypass methods
                await self._try_bypass_methods(page)
                
                # Step 4: Look for video elements
                await self._find_video_elements(page)
                
                # Step 5: Try to make video play
                await self._attempt_video_playback(page)
                
                # Step 6: Take screenshots for analysis
                await self._take_analysis_screenshots(page)
                
                # Keep browser open for manual inspection
                logger.info("🔍 Browser will stay open for manual inspection...")
                logger.info("📋 Findings summary:")
                self._print_findings()
                
                # Wait for user input before closing
                input("Press Enter to close browser and continue...")
                
            finally:
                await browser.close()
    
    async def _setup_aggressive_blocking(self, page):
        """Set up aggressive ad blocking and anti-detection."""
        # Block ads, trackers, and analytics
        await page.route("**/*", lambda route: (
            route.abort() if any(blocked in route.request.url for blocked in [
                'googletagmanager', 'google-analytics', 'googlesyndication',
                'doubleclick', 'adsystem', 'ads', 'analytics', 'tracking',
                'facebook.com', 'twitter.com', 'instagram.com'
            ]) else route.continue_()
        ))
        
        # Set headers to avoid detection
        await page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    async def _analyze_page_structure(self, page):
        """Analyze the overall page structure."""
        logger.info("📊 Analyzing page structure...")
        
        try:
            # Get page title
            title = await page.title()
            self.findings["page_structure"]["title"] = title
            logger.info(f"📄 Page title: {title}")
            
            # Check for common container elements
            containers = [
                'body', 'main', '#content', '.content', '#main', '.main',
                '#player', '.player', '#video', '.video', '.video-container'
            ]
            
            found_containers = []
            for container in containers:
                try:
                    element = await page.query_selector(container)
                    if element:
                        found_containers.append(container)
                        logger.info(f"✅ Found container: {container}")
                except:
                    pass
            
            self.findings["page_structure"]["containers"] = found_containers
            
            # Get page URL after any redirects
            current_url = page.url
            self.findings["page_structure"]["final_url"] = current_url
            logger.info(f"🔗 Final URL: {current_url}")
            
        except Exception as e:
            logger.error(f"❌ Error analyzing page structure: {str(e)}")
    
    async def _detect_login_elements(self, page):
        """Detect login modals, popups, and blocking elements."""
        logger.info("🔐 Detecting login/modal elements...")
        
        # Common login/modal selectors
        login_selectors = [
            # Modal containers
            '[role="dialog"]', '.modal', '.popup', '.overlay',
            '[class*="modal"]', '[class*="popup"]', '[class*="overlay"]',
            '[id*="modal"]', '[id*="popup"]', '[id*="overlay"]',
            
            # Login specific
            '[class*="login"]', '[class*="signup"]', '[class*="register"]',
            '[id*="login"]', '[id*="signup"]', '[id*="register"]',
            
            # Form elements
            'form[action*="login"]', 'form[action*="signup"]',
            'input[type="email"]', 'input[type="password"]',
            'input[name*="email"]', 'input[name*="password"]'
        ]
        
        found_login_elements = []
        for selector in login_selectors:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    for element in elements:
                        # Get element info
                        tag_name = await element.evaluate('el => el.tagName')
                        class_name = await element.evaluate('el => el.className')
                        id_attr = await element.evaluate('el => el.id')
                        text_content = await element.evaluate('el => el.textContent')
                        
                        element_info = {
                            "selector": selector,
                            "tag": tag_name,
                            "class": class_name,
                            "id": id_attr,
                            "text": text_content[:100] if text_content else ""
                        }
                        found_login_elements.append(element_info)
                        logger.info(f"🔍 Found login element: {selector} - {tag_name}.{class_name}")
            except:
                pass
        
        self.findings["login_elements"] = found_login_elements
        logger.info(f"📊 Total login elements found: {len(found_login_elements)}")
    
    async def _try_bypass_methods(self, page):
        """Try different methods to bypass login/modals."""
        logger.info("🚀 Attempting bypass methods...")
        
        bypass_methods = [
            ("escape_key", self._try_escape_key),
            ("close_buttons", self._try_close_buttons),
            ("javascript_removal", self._try_javascript_removal),
            ("click_outside", self._try_click_outside),
            ("continue_buttons", self._try_continue_buttons)
        ]
        
        for method_name, method_func in bypass_methods:
            try:
                logger.info(f"🔧 Trying bypass method: {method_name}")
                success = await method_func(page)
                
                result = {
                    "method": method_name,
                    "success": success,
                    "timestamp": "now"
                }
                self.findings["bypass_attempts"].append(result)
                
                if success:
                    logger.info(f"✅ Bypass method successful: {method_name}")
                    self.findings["successful_methods"].append(method_name)
                else:
                    logger.info(f"❌ Bypass method failed: {method_name}")
                
                # Wait between attempts
                await page.wait_for_timeout(2000)
                
            except Exception as e:
                logger.error(f"💥 Error in bypass method {method_name}: {str(e)}")
    
    async def _try_escape_key(self, page):
        """Try pressing Escape key."""
        await page.keyboard.press('Escape')
        await page.wait_for_timeout(1000)
        return True  # Always returns True as we can't easily detect if it worked
    
    async def _try_close_buttons(self, page):
        """Try clicking close buttons."""
        close_selectors = [
            'button:has-text("×")', 'button:has-text("Close")',
            '[aria-label="Close"]', '.close', '.modal-close',
            '[class*="close"]', '[id*="close"]'
        ]
        
        for selector in close_selectors:
            try:
                button = await page.query_selector(selector)
                if button:
                    await button.click()
                    logger.info(f"🖱️ Clicked close button: {selector}")
                    return True
            except:
                pass
        return False
    
    async def _try_javascript_removal(self, page):
        """Try removing modals with JavaScript."""
        try:
            await page.evaluate("""
                // Remove modal elements
                const modals = document.querySelectorAll(`
                    [class*="modal"], [class*="popup"], [class*="overlay"],
                    [class*="login"], [class*="signup"], [role="dialog"]
                `);
                modals.forEach(modal => modal.remove());
                
                // Enable scrolling
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
            """)
            logger.info("🔧 JavaScript modal removal executed")
            return True
        except:
            return False
    
    async def _try_click_outside(self, page):
        """Try clicking outside modals."""
        try:
            await page.click('body', position={'x': 10, 'y': 10})
            return True
        except:
            return False
    
    async def _try_continue_buttons(self, page):
        """Try clicking continue/skip buttons."""
        continue_selectors = [
            'button:has-text("Continue")', 'button:has-text("Skip")',
            'button:has-text("Watch")', 'button:has-text("Play")',
            'a:has-text("Continue")', 'a:has-text("Skip")'
        ]
        
        for selector in continue_selectors:
            try:
                button = await page.query_selector(selector)
                if button:
                    await button.click()
                    logger.info(f"🖱️ Clicked continue button: {selector}")
                    return True
            except:
                pass
        return False
    
    async def _find_video_elements(self, page):
        """Look for video elements on the page."""
        logger.info("🎥 Searching for video elements...")
        
        video_selectors = [
            'video', 'video[src]', 'video[data-src]',
            '#player video', '.player video', '.video-player video',
            '[id*="video"] video', '[class*="video"] video',
            'iframe[src*="embed"]', 'embed', 'object'
        ]
        
        found_videos = []
        for selector in video_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    tag_name = await element.evaluate('el => el.tagName')
                    src = await element.evaluate('el => el.src || el.getAttribute("data-src") || ""')
                    
                    video_info = {
                        "selector": selector,
                        "tag": tag_name,
                        "src": src
                    }
                    found_videos.append(video_info)
                    logger.info(f"🎬 Found video element: {selector} - {tag_name}")
                    if src:
                        logger.info(f"   📹 Source: {src[:100]}...")
            except:
                pass
        
        self.findings["video_elements"] = found_videos
        logger.info(f"📊 Total video elements found: {len(found_videos)}")
    
    async def _attempt_video_playback(self, page):
        """Try to make video play."""
        logger.info("▶️ Attempting video playback...")
        
        # Try clicking on video elements
        video_selectors = ['video', '#player video', '.player video']
        for selector in video_selectors:
            try:
                video = await page.query_selector(selector)
                if video:
                    logger.info(f"🖱️ Clicking video element: {selector}")
                    await video.click()
                    await page.wait_for_timeout(2000)
            except:
                pass
        
        # Try play buttons
        play_selectors = [
            'button:has-text("Play")', '.play-button', '[class*="play"]',
            '[aria-label*="play"]', '[title*="play"]'
        ]
        for selector in play_selectors:
            try:
                button = await page.query_selector(selector)
                if button:
                    logger.info(f"🖱️ Clicking play button: {selector}")
                    await button.click()
                    await page.wait_for_timeout(2000)
            except:
                pass
    
    async def _take_analysis_screenshots(self, page):
        """Take screenshots for analysis."""
        logger.info("📸 Taking analysis screenshots...")
        
        try:
            # Create screenshots directory
            screenshots_dir = Path("exploration_screenshots")
            screenshots_dir.mkdir(exist_ok=True)
            
            # Full page screenshot
            await page.screenshot(path=screenshots_dir / "full_page.png", full_page=True)
            
            # Viewport screenshot
            await page.screenshot(path=screenshots_dir / "viewport.png")
            
            logger.info(f"📸 Screenshots saved to: {screenshots_dir}")
            
        except Exception as e:
            logger.error(f"❌ Error taking screenshots: {str(e)}")
    
    def _print_findings(self):
        """Print summary of findings."""
        print("\n" + "="*60)
        print("🔍 EXPLORATION FINDINGS SUMMARY")
        print("="*60)
        
        print(f"\n📄 Page Title: {self.findings['page_structure'].get('title', 'Unknown')}")
        print(f"🔗 Final URL: {self.findings['page_structure'].get('final_url', 'Unknown')}")
        
        print(f"\n🔐 Login Elements Found: {len(self.findings['login_elements'])}")
        for element in self.findings['login_elements'][:5]:  # Show first 5
            print(f"   - {element['selector']} ({element['tag']})")
        
        print(f"\n🎥 Video Elements Found: {len(self.findings['video_elements'])}")
        for element in self.findings['video_elements']:
            print(f"   - {element['selector']} ({element['tag']})")
            if element['src']:
                print(f"     Source: {element['src'][:80]}...")
        
        print(f"\n🚀 Bypass Methods Tried: {len(self.findings['bypass_attempts'])}")
        print(f"✅ Successful Methods: {self.findings['successful_methods']}")
        
        # Save findings to file
        with open("exploration_findings.json", "w") as f:
            json.dump(self.findings, f, indent=2)
        print(f"\n💾 Detailed findings saved to: exploration_findings.json")

async def main():
    """Main function."""
    logger.info("🚀 Starting Xvideos exploration...")
    explorer = XvideosExplorer()
    await explorer.explore_site()
    logger.info("✅ Exploration complete!")

if __name__ == "__main__":
    asyncio.run(main())
