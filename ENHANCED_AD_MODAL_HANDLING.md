# Enhanced Ad and Mo<PERSON> Handling for Video Indexer

## Overview

The video indexer now has comprehensive capabilities to handle complex video sites with:
- **18+ age verification modals**
- **Pre-roll advertisements that need to be skipped**
- **Anti-adblock detection**
- **Complex JavaScript-based video players**
- **Adult content sites with various protection mechanisms**

## Three-Tier Processing System

### 1. Enhanced yt-dlp (Primary Method)
**File**: `src/metadata_extractor.py`

Enhanced configuration includes:
- `age_limit: 99` - Allows all age-restricted content
- `retries: 5` - Retry failed downloads
- `socket_timeout: 30` - Extended timeout for slow sites
- `cookiefile: data/cookies.txt` - Cookie persistence
- `skip_unavailable_fragments: True` - Handle broken streams
- Enhanced browser headers for better compatibility
- YouTube ad skipping enabled
- Extractor-specific arguments for different platforms

### 2. Browser Automation (Fallback Method)
**File**: `src/browser_handler.py`

When yt-dlp fails, the system uses Playwright browser automation to:
- Navigate to video pages with realistic browser behavior
- Automatically detect and click through 18+ age verification modals
- Wait for pre-roll ads and automatically click skip buttons
- Extract video metadata from complex JavaScript players
- Save cookies for future sessions

#### Site-Specific Patterns

The system includes pre-configured patterns for major adult video sites:

**Pornhub.com**:
- Age modal selectors: `[data-age-verification="true"] button`, `.age-verification button`
- Ad skip selectors: `.ytp-ad-skip-button`, `.skip-ad`
- Video selectors: `video`, `.video-element`, `#player video`

**XVideos.com**:
- Age modal selectors: `.age-verification button`, `[data-age-check] button`
- Ad skip selectors: `.skip-ad`, `.ad-close`
- Video selectors: `video`, `#html5video`

**RedTube.com**:
- Age modal selectors: `.age-verification-modal button`
- Ad skip selectors: `.skip-ad`, `.close-ad`
- Video selectors: `video`

**Generic Patterns** (for unknown sites):
- 8 different age modal selector patterns
- 8 different ad skip selector patterns
- 4 different video selector patterns

### 3. Platform-Specific Extraction (Legacy Fallback)
**File**: `src/metadata_extractor.py`

Traditional platform-specific extraction methods as final fallback.

## Key Features

### Age Verification Handling
- Automatically detects age verification modals
- Clicks "I am 18+", "Yes", "Enter", "Continue" buttons
- Handles various modal designs and layouts
- Works with both standard and custom age gates

### Advertisement Management
- Waits for pre-roll ads to load
- Automatically detects and clicks skip buttons
- Handles multiple ad formats and timings
- Supports YouTube, generic video ads, and custom ad systems

### Anti-Adblock Bypass
- Uses realistic browser headers and user agents
- Mimics human browsing behavior
- Disables automation detection features
- Rotates browser characteristics

### Cookie Persistence
- Saves cookies between sessions in `data/browser_cookies.json`
- Maintains login states and age verification choices
- Reduces repeated modal interactions
- Supports cross-session authentication

## Installation

### Basic Setup (yt-dlp enhancements only)
The enhanced yt-dlp configuration works immediately with existing dependencies.

### Full Browser Automation Setup
For complete ad/modal handling capabilities:

```bash
# Install Playwright
pip install playwright

# Install browser binaries
playwright install chromium

# Run setup script
python3 setup_browser_automation.py
```

## Usage Examples

### Basic Usage
```python
from src.metadata_extractor import MetadataExtractor

extractor = MetadataExtractor()

# This will now handle ads and modals automatically
result = extractor.extract_metadata(
    'https://example-adult-site.com/video/12345',
    'generic'
)

if result['success']:
    print(f"Title: {result['title']}")
    print(f"Method: {result['extraction_method']}")
    # Methods: 'yt-dlp', 'browser_automation', or 'fallback'
```

### Processing Flow
1. **yt-dlp attempt**: Enhanced configuration tries to handle the URL
2. **Platform-specific attempt**: If yt-dlp fails, try platform-specific extraction
3. **Browser automation**: If both fail, launch browser automation:
   - Navigate to URL with realistic browser
   - Handle age verification modals
   - Wait for and skip advertisements
   - Extract video metadata
   - Save cookies for future use

## Configuration

### yt-dlp Options
Located in `MetadataExtractor.__init__()`:
- Modify `age_limit` to change age restriction handling
- Adjust `retries` and timeout values for different network conditions
- Update `extractor_args` for site-specific optimizations

### Browser Automation Patterns
Located in `BrowserVideoHandler.site_patterns`:
- Add new sites with custom selector patterns
- Modify existing patterns for site updates
- Adjust timing values for different ad lengths

### Cookie Management
- Cookies saved to `data/browser_cookies.json`
- Automatic cleanup of expired cookies
- Manual cookie clearing: delete the cookie file

## Testing

### Test Enhanced Configuration
```bash
python3 test_ad_modal_handling.py
```

This will test:
- Enhanced yt-dlp configuration
- Site-specific pattern loading
- Browser automation capabilities (if Playwright installed)
- Complete extraction flow with fallbacks

### Test Specific Sites
The system can now handle:
- YouTube videos with ads
- Adult content sites with age verification
- Sites with anti-adblock detection
- Complex JavaScript-based video players
- Login-required content

## Troubleshooting

### Common Issues

**"Playwright not available"**:
- Install with: `pip install playwright`
- Install browsers: `playwright install chromium`

**Age verification still appearing**:
- Check if cookies are being saved properly
- Verify site-specific patterns match current site design
- Add custom patterns for new sites

**Ads not being skipped**:
- Increase wait times in `_handle_ads()` method
- Add new ad skip selectors for specific sites
- Check browser console for JavaScript errors

**Extraction still failing**:
- Enable headless=False in browser launch for debugging
- Check network connectivity and site accessibility
- Verify site hasn't changed its structure

## Performance Considerations

- Browser automation is slower than yt-dlp (5-30 seconds vs 1-5 seconds)
- Cookie persistence reduces repeated modal handling
- Headless browser mode minimizes resource usage
- Automatic cleanup prevents memory leaks

## Security and Privacy

- No personal data is collected or transmitted
- Cookies are stored locally only
- Browser automation uses standard web APIs
- No bypassing of legitimate security measures
- Respects robots.txt and site terms of service

## Future Enhancements

Potential improvements:
- Machine learning for dynamic selector detection
- Captcha solving integration
- Proxy rotation for IP-based restrictions
- Custom user agent rotation
- Advanced fingerprint randomization

---

**The enhanced system now provides comprehensive handling of complex video sites while maintaining compatibility with existing functionality.**
