#!/usr/bin/env python3
"""
Script to measure AI model resource usage (RAM, CPU) for optimization planning
"""

import sys
import os
import time
import psutil
import threading
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def measure_memory_usage():
    """Monitor memory usage during AI model operations."""
    process = psutil.Process()
    return process.memory_info().rss / 1024 / 1024  # MB

def measure_ai_model_resources():
    """Measure resource usage of AI models."""
    
    print("=== AI Model Resource Usage Analysis ===")
    
    # Baseline memory
    baseline_memory = measure_memory_usage()
    print(f"Baseline memory usage: {baseline_memory:.1f} MB")
    
    try:
        print("\n1. Loading CLIP Model (ViT-B-32)...")
        from ai_analyzer import CLIPAnalyzer
        
        # Measure CLIP loading
        start_time = time.time()
        clip_analyzer = CLIPAnalyzer()
        clip_load_time = time.time() - start_time
        clip_memory = measure_memory_usage()
        
        print(f"   ✅ CLIP loaded in {clip_load_time:.2f}s")
        print(f"   📊 Memory after CLIP: {clip_memory:.1f} MB (+{clip_memory - baseline_memory:.1f} MB)")
        
        # Test CLIP inference
        sample_image = None
        for video_dir in Path('data/youtube/index').iterdir():
            if video_dir.is_dir():
                screenshots_dir = video_dir / 'screenshots'
                if screenshots_dir.exists():
                    for img_file in screenshots_dir.glob('frame_*.jpg'):
                        sample_image = img_file
                        break
                if sample_image:
                    break
        
        if sample_image:
            print(f"\n2. Testing CLIP inference with {sample_image.name}...")
            
            # Measure CLIP inference
            start_time = time.time()
            result = clip_analyzer.analyze_image(sample_image)
            clip_inference_time = time.time() - start_time
            
            if result.get('success'):
                print(f"   ✅ CLIP inference: {clip_inference_time:.3f}s")
                print(f"   📊 Embedding dimension: {len(result.get('embedding', []))}")
            
            # Test BLIP if available
            if clip_analyzer.captioner.is_available():
                print(f"\n3. Testing BLIP inference...")
                blip_memory_before = measure_memory_usage()
                
                start_time = time.time()
                caption = clip_analyzer.captioner.generate_caption(sample_image)
                blip_inference_time = time.time() - start_time
                blip_memory_after = measure_memory_usage()
                
                print(f"   ✅ BLIP inference: {blip_inference_time:.3f}s")
                print(f"   📊 Memory during BLIP: {blip_memory_after:.1f} MB (+{blip_memory_after - blip_memory_before:.1f} MB)")
                print(f"   📝 Caption: {caption}")
                
                # Test batch processing
                print(f"\n4. Testing batch processing (5 frames)...")
                sample_images = list(sample_image.parent.glob('frame_*.jpg'))[:5]
                
                start_time = time.time()
                batch_memory_before = measure_memory_usage()
                
                batch_results = []
                for img in sample_images:
                    result = clip_analyzer.analyze_image(img)
                    if result.get('success'):
                        batch_results.append(result)
                
                batch_time = time.time() - start_time
                batch_memory_after = measure_memory_usage()
                
                print(f"   ✅ Batch processing (5 frames): {batch_time:.3f}s ({batch_time/5:.3f}s per frame)")
                print(f"   📊 Memory during batch: {batch_memory_after:.1f} MB (+{batch_memory_after - batch_memory_before:.1f} MB)")
                
        # Final memory measurement
        final_memory = measure_memory_usage()
        total_ai_memory = final_memory - baseline_memory
        
        print(f"\n📊 Resource Usage Summary:")
        print(f"   - Total AI memory overhead: {total_ai_memory:.1f} MB")
        print(f"   - CLIP model memory: ~{clip_memory - baseline_memory:.1f} MB")
        print(f"   - BLIP additional memory: ~{final_memory - clip_memory:.1f} MB")
        print(f"   - Device: {clip_analyzer.device}")
        print(f"   - CLIP model: {clip_analyzer.model_name}")
        print(f"   - BLIP model: {clip_analyzer.captioner.model_name}")
        
        # CPU usage estimation
        print(f"\n🖥️  Performance Characteristics:")
        if sample_image:
            print(f"   - CLIP inference: ~{clip_inference_time*1000:.1f}ms per frame")
            if clip_analyzer.captioner.is_available():
                print(f"   - BLIP inference: ~{blip_inference_time*1000:.1f}ms per frame")
                print(f"   - Combined processing: ~{(clip_inference_time + blip_inference_time)*1000:.1f}ms per frame")
        
        # Parallelization recommendations
        print(f"\n🚀 Parallelization Recommendations:")
        available_memory = psutil.virtual_memory().available / 1024 / 1024  # MB
        cpu_count = psutil.cpu_count()
        
        max_instances = min(
            int(available_memory / total_ai_memory * 0.8),  # 80% of available memory
            cpu_count,  # Don't exceed CPU cores
            8  # Reasonable upper limit
        )
        
        print(f"   - Available memory: {available_memory:.0f} MB")
        print(f"   - CPU cores: {cpu_count}")
        print(f"   - Recommended parallel instances: {max_instances}")
        print(f"   - Memory per instance: ~{total_ai_memory:.1f} MB")
        print(f"   - Total memory for {max_instances} instances: ~{total_ai_memory * max_instances:.1f} MB")
        
        if max_instances > 1:
            estimated_speedup = min(max_instances, cpu_count * 0.8)  # Account for overhead
            print(f"   - Estimated speedup: {estimated_speedup:.1f}x")
        
    except Exception as e:
        print(f"❌ Error during resource measurement: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    measure_ai_model_resources()
