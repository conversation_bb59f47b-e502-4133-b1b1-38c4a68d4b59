#!/usr/bin/env python3
"""
Script to update database schema for enhanced AI analysis
"""

import sys
import os
import sqlite3
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def update_database_schema():
    """Update database schema to support enhanced AI analysis."""
    
    db_path = Path('data/video_index.db')
    backup_path = Path('data/video_index.db.backup_before_schema_update')
    
    print("=== Updating Database Schema for Enhanced AI Analysis ===")
    
    # Create backup
    if db_path.exists():
        print(f"Creating backup: {backup_path}")
        import shutil
        shutil.copy2(db_path, backup_path)
        print("✅ Backup created")
    
    try:
        with sqlite3.connect(db_path) as conn:
            print("1. Dropping old FTS table...")
            conn.execute('DROP TABLE IF EXISTS videos_fts')
            
            print("2. Creating new tables...")
            
            # Video summaries table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS video_summaries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    summary TEXT,
                    key_themes TEXT,  -- JSON array of key themes
                    dominant_tags TEXT,  -- JSON array of dominant tags
                    frame_count INTEGER DEFAULT 0,
                    successful_frames INTEGER DEFAULT 0,
                    description_coverage TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id) ON DELETE CASCADE
                )
            ''')
            
            # Frame descriptions table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS frame_descriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    frame_analysis_id INTEGER,
                    description TEXT NOT NULL,
                    auto_tags TEXT,  -- JSON array of auto-extracted tags
                    model_name TEXT DEFAULT 'blip-image-captioning-base',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (frame_analysis_id) REFERENCES frame_analysis (id) ON DELETE CASCADE
                )
            ''')
            
            print("3. Creating indexes...")
            conn.execute('CREATE INDEX IF NOT EXISTS idx_video_summaries_video_id ON video_summaries (video_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_frame_descriptions_frame_analysis_id ON frame_descriptions (frame_analysis_id)')
            
            print("4. Creating new FTS table with enhanced columns...")
            conn.execute('''
                CREATE VIRTUAL TABLE videos_fts USING fts5(
                    title, description, uploader, tags, categories, ai_tags, video_summary, frame_descriptions,
                    content='videos',
                    content_rowid='id'
                )
            ''')
            
            print("5. Rebuilding FTS index...")
            # Get all videos and rebuild FTS index
            cursor = conn.execute('''
                SELECT v.id, v.title, v.description, v.uploader,
                       GROUP_CONCAT(DISTINCT t.tag) as tags,
                       GROUP_CONCAT(DISTINCT c.category) as categories,
                       GROUP_CONCAT(DISTINCT at.tag) as ai_tags
                FROM videos v
                LEFT JOIN tags t ON v.id = t.video_id
                LEFT JOIN categories c ON v.id = c.video_id
                LEFT JOIN ai_tags at ON v.id = at.video_id
                GROUP BY v.id
            ''')
            
            for row in cursor.fetchall():
                video_id, title, description, uploader, tags, categories, ai_tags = row
                
                # Get video summary
                summary_cursor = conn.execute('SELECT summary FROM video_summaries WHERE video_id = ?', (video_id,))
                summary_row = summary_cursor.fetchone()
                video_summary = summary_row[0] if summary_row else ''
                
                # Get frame descriptions
                desc_cursor = conn.execute('''
                    SELECT fd.description 
                    FROM frame_descriptions fd
                    JOIN frame_analysis fa ON fd.frame_analysis_id = fa.id
                    WHERE fa.video_id = ?
                ''', (video_id,))
                descriptions = [row[0] for row in desc_cursor.fetchall() if row[0]]
                frame_descriptions = ' | '.join(descriptions) if descriptions else ''
                
                # Insert into FTS
                conn.execute('''
                    INSERT INTO videos_fts (
                        rowid, title, description, uploader, tags, categories, 
                        ai_tags, video_summary, frame_descriptions
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    video_id,
                    title or '',
                    description or '',
                    uploader or '',
                    tags or '',
                    categories or '',
                    ai_tags or '',
                    video_summary,
                    frame_descriptions
                ))
            
            conn.commit()
            print("✅ Database schema updated successfully!")
            
            # Show stats
            cursor = conn.execute('SELECT COUNT(*) FROM videos')
            video_count = cursor.fetchone()[0]
            
            cursor = conn.execute('SELECT COUNT(*) FROM video_summaries')
            summary_count = cursor.fetchone()[0]
            
            cursor = conn.execute('SELECT COUNT(*) FROM frame_descriptions')
            description_count = cursor.fetchone()[0]
            
            print(f"\n📊 Database Statistics:")
            print(f"   - Videos: {video_count}")
            print(f"   - Video summaries: {summary_count}")
            print(f"   - Frame descriptions: {description_count}")
            
    except Exception as e:
        print(f"❌ Error updating database schema: {e}")
        import traceback
        traceback.print_exc()
        
        # Restore backup if something went wrong
        if backup_path.exists():
            print(f"Restoring backup from {backup_path}")
            shutil.copy2(backup_path, db_path)
            print("✅ Backup restored")

if __name__ == '__main__':
    update_database_schema()
