{"page_structure": {"title": "XVIDEOS with no ads and extra exclusive porn videos - XV PREMIUM", "containers": ["body", "#content", "#main", ".main", ".video"], "final_url": "https://www.xvideos.red/video.ukbiaav5794/whore_between_neighbors?sxcaf=4353LFJE75_video__"}, "login_elements": [{"selector": "[class*=\"popup\"]", "tag": "DIV", "class": "x-overlay x-overlay-box auto-width-popup", "id": "", "text": "\n\t\n\t\t\n\t Hold on \nYou must create an account\n\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\tThis is a   video, only members can pl"}, {"selector": "[class*=\"overlay\"]", "tag": "BODY", "class": "video-page body--video x-overlay-floating-opened", "id": "", "text": "\n\txv.disclaimer.display('XV PREMIUM',false,false)\n.img-blured img,.img-blured video,.img-blured .exo"}, {"selector": "[class*=\"overlay\"]", "tag": "SPAN", "class": "premium-log-overlay", "id": "", "text": "100%"}, {"selector": "[class*=\"overlay\"]", "tag": "DIV", "class": "x-overlay x-overlay-box auto-width-popup", "id": "", "text": "\n\t\n\t\t\n\t Hold on \nYou must create an account\n\t\n\t\t\n\t\t\t\n\t\t\t\n\t\t\n\t\tThis is a   video, only members can pl"}, {"selector": "[class*=\"overlay\"]", "tag": "SPAN", "class": "premium-log-overlay", "id": "", "text": ""}, {"selector": "[id*=\"popup\"]", "tag": "A", "class": "suggestion", "id": "metadata_suggestion_popup_opener", "text": "Edit tags and models"}, {"selector": "[class*=\"login\"]", "tag": "DIV", "class": "head__login-btn-group", "id": "", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> now"}, {"selector": "[class*=\"login\"]", "tag": "A", "class": "head__btn head__btn--connection head__btn--login btn-clear", "id": "anc-tst-login-btn", "text": "<PERSON><PERSON>"}, {"selector": "[id*=\"login\"]", "tag": "A", "class": "head__btn head__btn--connection head__btn--login btn-clear", "id": "anc-tst-login-btn", "text": "<PERSON><PERSON>"}], "video_elements": [], "bypass_attempts": [{"method": "escape_key", "success": true, "timestamp": "now"}, {"method": "close_buttons", "success": false, "timestamp": "now"}, {"method": "javascript_removal", "success": false, "timestamp": "now"}, {"method": "click_outside", "success": false, "timestamp": "now"}, {"method": "continue_buttons", "success": false, "timestamp": "now"}], "successful_methods": ["escape_key"]}