#!/usr/bin/env python3
"""
Test script for adult content processing with the new aggressive browser automation.

This script tests the complete adult content processing pipeline:
1. Add an adult content URL
2. Process it using the new AdultContentProcessor
3. Verify that screenshots are extracted successfully
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.indexer import VideoIndexer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_adult_content_processing():
    """Test adult content processing with the new system."""
    
    # Initialize indexer
    logger.info("Initializing video indexer...")
    indexer = VideoIndexer()
    
    # Test URL - using the existing xvideos URL that failed before
    test_url = "https://www.xvideos.com/video70986409/hot_blonde_gets_fucked"

    logger.info(f"Testing adult content processing with URL: {test_url}")

    try:
        # Process the URL directly (it already exists in the system)
        logger.info("Processing URL directly...")
        result = indexer.process_url(test_url)

        if result and result.get('success'):
            logger.info("✅ URL processed successfully")
            processed = 1
            failed = 0
        else:
            logger.warning("⚠️ URL processing returned failure or no result")
            processed = 0
            failed = 1

        logger.info(f"Processing complete: {processed} processed, {failed} failed")

        if processed > 0:
            logger.info("✅ Adult content processing successful!")

            # Check if screenshots were extracted
            data_dir = Path("data")
            xvideos_dirs = list(data_dir.glob("xvideos/index/*/screenshots"))

            if xvideos_dirs:
                for screenshot_dir in xvideos_dirs:
                    screenshots = list(screenshot_dir.glob("*.jpg"))
                    logger.info(f"Found {len(screenshots)} screenshots in {screenshot_dir}")

                    if screenshots:
                        logger.info("✅ Screenshots successfully extracted using browser automation!")
                        logger.info(f"Screenshot files: {[s.name for s in screenshots[:3]]}...")  # Show first 3
                        return True
                    else:
                        logger.warning("❌ No screenshots found")
            else:
                logger.warning("❌ No screenshot directories found")
        else:
            logger.error("❌ No videos were processed")
            
    except Exception as e:
        logger.error(f"❌ Error during testing: {str(e)}")
        return False
    
    return False

if __name__ == "__main__":
    logger.info("Starting adult content processing test...")
    
    success = test_adult_content_processing()
    
    if success:
        logger.info("🎉 Adult content processing test PASSED!")
        sys.exit(0)
    else:
        logger.error("💥 Adult content processing test FAILED!")
        sys.exit(1)
