"""
URL Manager Component for Video Search Indexer

This module handles URL input, validation, storage, and domain extraction
for organizing video content by base domain names.
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Optional, Set
from urllib.parse import urlparse, parse_qs
import validators
import logging

# Set up logging for this module
logger = logging.getLogger(__name__)


class URLManager:
    """
    Manages video URLs including validation, storage, and domain organization.
    
    Features:
    - URL validation and normalization
    - Domain extraction (removes .com, .org, etc.)
    - Duplicate detection
    - Persistent storage of URLs and their status
    - Support for major video platforms
    """
    
    def __init__(self, storage_path: str = "data/urls.json"):
        """
        Initialize the URL manager.
        
        Args:
            storage_path: Path to store URL database JSON file
        """
        self.storage_path = Path(storage_path)
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Load existing URLs from storage
        self.urls_db = self._load_urls_database()
        
        # Supported video platforms and their URL patterns
        self.supported_platforms = {
            'youtube': [
                r'(?:https?://)?(?:www\.)?(?:youtube\.com/watch\?v=|youtu\.be/)',
                r'(?:https?://)?(?:www\.)?youtube\.com/embed/',
                r'(?:https?://)?(?:www\.)?youtube\.com/v/'
            ],
            'vimeo': [
                r'(?:https?://)?(?:www\.)?vimeo\.com/\d+',
                r'(?:https?://)?player\.vimeo\.com/video/\d+'
            ],
            'dailymotion': [
                r'(?:https?://)?(?:www\.)?dailymotion\.com/video/',
                r'(?:https?://)?dai\.ly/'
            ],
            'twitch': [
                r'(?:https?://)?(?:www\.)?twitch\.tv/videos/',
                r'(?:https?://)?(?:www\.)?twitch\.tv/\w+/clip/'
            ],
            # Adult content platforms (with browser automation support)
            'pornhub': [
                r'(?:https?://)?(?:www\.)?pornhub\.com/view_video\.php\?viewkey=',
                r'(?:https?://)?(?:www\.)?pornhub\.com/embed/'
            ],
            'xvideos': [
                r'(?:https?://)?(?:www\.)?xvideos\.com/video\.',  # Format: /video.ID/title
                r'(?:https?://)?(?:www\.)?xvideos\.com/video\d+/', # Format: /videoID/title
                r'(?:https?://)?(?:www\.)?xvideos\.com/embedframe/'
            ],
            'redtube': [
                r'(?:https?://)?(?:www\.)?redtube\.com/\d+',
                r'(?:https?://)?(?:www\.)?redtube\.com/embed/'
            ],
            'xhamster': [
                r'(?:https?://)?(?:www\.)?xhamster\.com/videos/',
                r'(?:https?://)?(?:www\.)?xhamster\.com/embed/'
            ],
            'youporn': [
                r'(?:https?://)?(?:www\.)?youporn\.com/watch/',
                r'(?:https?://)?(?:www\.)?youporn\.com/embed/'
            ],
            'tube8': [
                r'(?:https?://)?(?:www\.)?tube8\.com/\w+/\w+/\d+/',
                r'(?:https?://)?(?:www\.)?tube8\.com/embed/'
            ],
            'spankbang': [
                r'(?:https?://)?(?:www\.)?spankbang\.com/\w+/video/',
                r'(?:https?://)?(?:www\.)?spankbang\.com/embed/'
            ]
        }
    
    def add_url(self, url: str, metadata: Optional[Dict] = None) -> Dict:
        """
        Add a new URL to the manager.
        
        Args:
            url: Video URL to add
            metadata: Optional metadata to store with the URL
            
        Returns:
            Dict with status and information about the added URL
        """
        # Validate and normalize the URL
        validation_result = self.validate_url(url)
        if not validation_result['valid']:
            return {
                'success': False,
                'error': validation_result['error'],
                'url': url
            }
        
        normalized_url = validation_result['normalized_url']
        domain = self.extract_domain(normalized_url)
        platform = validation_result['platform']
        
        # Check for duplicates
        if self._is_duplicate(normalized_url):
            return {
                'success': False,
                'error': 'URL already exists in database',
                'url': normalized_url,
                'existing_entry': self.urls_db[normalized_url]
            }
        
        # Create URL entry
        url_entry = {
            'original_url': url,
            'normalized_url': normalized_url,
            'domain': domain,
            'platform': platform,
            'status': 'pending',  # pending, processing, completed, failed
            'added_date': self._get_current_timestamp(),
            'metadata': metadata or {},
            'processing_attempts': 0,
            'last_error': None
        }
        
        # Store in database
        self.urls_db[normalized_url] = url_entry
        self._save_urls_database()
        
        logger.info(f"Added URL: {normalized_url} (platform: {platform}, domain: {domain})")
        
        return {
            'success': True,
            'url': normalized_url,
            'domain': domain,
            'platform': platform,
            'entry': url_entry
        }
    
    def validate_url(self, url: str) -> Dict:
        """
        Validate and normalize a video URL.
        
        Args:
            url: URL to validate
            
        Returns:
            Dict with validation results
        """
        # Basic URL validation
        if not validators.url(url):
            return {
                'valid': False,
                'error': 'Invalid URL format'
            }
        
        # Check if it's a supported video platform
        platform = self._detect_platform(url)
        if not platform:
            return {
                'valid': False,
                'error': 'Unsupported video platform'
            }
        
        # Normalize the URL (remove tracking parameters, etc.)
        normalized_url = self._normalize_url(url, platform)
        
        return {
            'valid': True,
            'normalized_url': normalized_url,
            'platform': platform
        }
    
    def extract_domain(self, url: str) -> str:
        """
        Extract clean domain name from URL (removes .com, .org, etc.).
        
        Args:
            url: URL to extract domain from
            
        Returns:
            Clean domain name (e.g., 'youtube' from 'youtube.com')
        """
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove 'www.' prefix
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Remove common TLDs to get base domain name
        tlds_to_remove = ['.com', '.org', '.net', '.tv', '.ly', '.be']
        for tld in tlds_to_remove:
            if domain.endswith(tld):
                domain = domain[:-len(tld)]
                break
        
        return domain
    
    def get_pending_urls(self) -> List[Dict]:
        """Get all URLs with 'pending' status."""
        return [entry for entry in self.urls_db.values() if entry['status'] == 'pending']
    
    def get_urls_by_domain(self, domain: str) -> List[Dict]:
        """Get all URLs for a specific domain."""
        return [entry for entry in self.urls_db.values() if entry['domain'] == domain]
    
    def get_urls_by_platform(self, platform: str) -> List[Dict]:
        """Get all URLs for a specific platform."""
        return [entry for entry in self.urls_db.values() if entry['platform'] == platform]
    
    def update_url_status(self, url: str, status: str, error: Optional[str] = None) -> bool:
        """
        Update the processing status of a URL.
        
        Args:
            url: URL to update
            status: New status (pending, processing, completed, failed)
            error: Error message if status is 'failed'
            
        Returns:
            True if update successful, False if URL not found
        """
        if url not in self.urls_db:
            return False
        
        self.urls_db[url]['status'] = status
        self.urls_db[url]['processing_attempts'] += 1
        
        if error:
            self.urls_db[url]['last_error'] = error
        
        self._save_urls_database()
        return True
    
    def remove_url(self, url: str) -> bool:
        """Remove a URL from the database."""
        if url in self.urls_db:
            del self.urls_db[url]
            self._save_urls_database()
            logger.info(f"Removed URL: {url}")
            return True
        return False
    
    def get_statistics(self) -> Dict:
        """Get statistics about stored URLs."""
        total = len(self.urls_db)
        by_status = {}
        by_platform = {}
        by_domain = {}
        
        for entry in self.urls_db.values():
            # Count by status
            status = entry['status']
            by_status[status] = by_status.get(status, 0) + 1
            
            # Count by platform
            platform = entry['platform']
            by_platform[platform] = by_platform.get(platform, 0) + 1
            
            # Count by domain
            domain = entry['domain']
            by_domain[domain] = by_domain.get(domain, 0) + 1
        
        return {
            'total_urls': total,
            'by_status': by_status,
            'by_platform': by_platform,
            'by_domain': by_domain
        }
    
    def _detect_platform(self, url: str) -> Optional[str]:
        """Detect which video platform a URL belongs to."""
        for platform, patterns in self.supported_platforms.items():
            for pattern in patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    return platform
        return None
    
    def _normalize_url(self, url: str, platform: str) -> str:
        """Normalize URL by removing tracking parameters and standardizing format."""
        parsed = urlparse(url)
        
        if platform == 'youtube':
            # Extract video ID and create clean URL
            video_id = None
            if 'v=' in parsed.query:
                video_id = parse_qs(parsed.query)['v'][0]
            elif 'youtu.be' in parsed.netloc:
                video_id = parsed.path.lstrip('/')
            
            if video_id:
                return f"https://www.youtube.com/watch?v={video_id}"
        
        elif platform == 'vimeo':
            # Extract video ID from path
            video_id = re.search(r'/(\d+)', parsed.path)
            if video_id:
                return f"https://vimeo.com/{video_id.group(1)}"

        elif platform == 'pornhub':
            # Extract viewkey parameter
            if 'viewkey=' in parsed.query:
                viewkey = parse_qs(parsed.query)['viewkey'][0]
                return f"https://www.pornhub.com/view_video.php?viewkey={viewkey}"

        elif platform == 'xvideos':
            # For xvideos, keep the full path including title as it's required
            # Format: /video.ID/title - both parts are needed
            return f"https://www.xvideos.com{parsed.path}"

        elif platform == 'redtube':
            # Extract video ID from path
            video_id = re.search(r'/(\d+)', parsed.path)
            if video_id:
                return f"https://www.redtube.com/{video_id.group(1)}"

        # For other platforms (including other adult sites), return as-is for now
        # This allows browser automation to handle complex URLs
        return url
    
    def _is_duplicate(self, url: str) -> bool:
        """Check if URL already exists in database."""
        return url in self.urls_db
    
    def _load_urls_database(self) -> Dict:
        """Load URLs database from storage file."""
        if self.storage_path.exists():
            try:
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"Error loading URLs database: {e}")
                return {}
        return {}
    
    def _save_urls_database(self) -> None:
        """Save URLs database to storage file."""
        try:
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(self.urls_db, f, indent=2, ensure_ascii=False)
        except IOError as e:
            logger.error(f"Error saving URLs database: {e}")
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp as ISO format string."""
        from datetime import datetime
        return datetime.now().isoformat()
