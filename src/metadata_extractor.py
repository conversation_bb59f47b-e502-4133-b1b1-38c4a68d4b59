"""
Metadata Extractor Component for Video Search Indexer

This module extracts video metadata (title, description, thumbnail URL, duration, etc.)
from various video platforms using yt-dlp and web scraping techniques.
"""

import logging
import re
import requests
import asyncio
from typing import Dict, Optional, Any
from pathlib import Path
import yt_dlp
from datetime import datetime, timedelta
try:
    from .browser_handler import BrowserVideoHandler
except ImportError:
    from browser_handler import BrowserVideoHandler

# Set up logging for this module
logger = logging.getLogger(__name__)


class MetadataExtractor:
    """
    Extracts comprehensive metadata from video URLs across multiple platforms.
    
    Features:
    - Uses yt-dlp for robust video metadata extraction
    - Handles multiple video platforms (YouTube, Vimeo, etc.)
    - Extracts thumbnails, titles, descriptions, duration, and more
    - Provides fallback methods for when yt-dlp fails
    - Sanitizes and normalizes extracted data
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the metadata extractor.
        
        Args:
            config: Configuration dictionary with extraction settings
        """
        self.config = config or {}

        # Initialize browser handler for complex cases
        self.browser_handler = BrowserVideoHandler(config)

        # Enhanced yt-dlp options for metadata extraction with ad/modal handling
        self.yt_dlp_opts = {
            'quiet': True,  # Suppress yt-dlp output
            'no_warnings': True,
            'extractaudio': False,
            'extractvideo': False,
            'writeinfojson': False,
            'writethumbnail': False,
            'writesubtitles': False,
            'writeautomaticsub': False,
            'ignoreerrors': True,
            'no_check_certificate': True,
            'user_agent': self.config.get('user_agent',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),

            # Age restriction and adult content handling
            'age_limit': 99,  # Allow all age-restricted content
            'skip_unavailable_fragments': True,  # Skip broken video fragments

            # Retry and timeout settings for handling slow/ad-heavy sites
            'retries': 5,  # Retry failed downloads
            'fragment_retries': 5,  # Retry failed fragments
            'socket_timeout': 30,  # Socket timeout in seconds
            'sleep_interval': 1,  # Sleep between requests
            'max_sleep_interval': 5,  # Maximum sleep interval

            # Cookie handling for login/age verification persistence
            'cookiefile': str(Path('data/cookies.txt')),  # Save cookies for reuse
            'cookiesfrombrowser': None,  # Can be set to browser name if needed

            # Additional headers to appear more like a real browser
            'http_headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
            },

            # Extractor-specific arguments for handling ads and modals
            'extractor_args': {
                'youtube': {
                    'skip_dash_manifest': True,  # Skip DASH for faster extraction
                    'player_skip_ads': True,     # Try to skip YouTube ads
                },
                'generic': {
                    'check_formats': True,       # Check all available formats
                }
            }
        }
        
        # Headers for direct HTTP requests (fallback method)
        self.headers = {
            'User-Agent': self.yt_dlp_opts['user_agent'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
    
    def extract_metadata(self, url: str, platform: str) -> Dict[str, Any]:
        """
        Extract comprehensive metadata from a video URL.
        
        Args:
            url: Video URL to extract metadata from
            platform: Platform name (youtube, vimeo, etc.)
            
        Returns:
            Dictionary containing extracted metadata
        """
        logger.info(f"Extracting metadata from {platform} URL: {url}")
        
        try:
            # Primary method: Use yt-dlp for extraction
            metadata = self._extract_with_ytdlp(url)
            
            if metadata and metadata.get('title'):
                logger.info(f"Successfully extracted metadata using yt-dlp: {metadata.get('title')}")
                return self._normalize_metadata(metadata, platform)
            
            # Fallback method: Platform-specific extraction
            logger.warning(f"yt-dlp failed, trying platform-specific extraction for {platform}")
            metadata = self._extract_platform_specific(url, platform)

            if metadata and metadata.get('title'):
                logger.info(f"Successfully extracted metadata using fallback method: {metadata.get('title')}")
                return self._normalize_metadata(metadata, platform)

            # Final fallback: Browser automation for complex cases (ads, modals, etc.)
            logger.warning(f"Platform-specific extraction failed, trying browser automation for {url}")
            browser_result = self._extract_with_browser(url)

            if browser_result and browser_result.get('success'):
                video_info = browser_result.get('video_info', {})
                if video_info.get('title'):
                    logger.info(f"Successfully extracted metadata using browser automation: {video_info.get('title')}")
                    return self._normalize_metadata(video_info, platform)

            # If all methods fail
            logger.error(f"Failed to extract metadata from URL: {url}")
            return self._create_error_metadata(url, "Failed to extract metadata with all methods")
            
        except Exception as e:
            logger.error(f"Error extracting metadata from {url}: {str(e)}")
            return self._create_error_metadata(url, str(e))
    
    def _extract_with_ytdlp(self, url: str) -> Optional[Dict]:
        """Extract metadata using yt-dlp library."""
        try:
            with yt_dlp.YoutubeDL(self.yt_dlp_opts) as ydl:
                # Extract info without downloading
                info = ydl.extract_info(url, download=False)
                
                if not info:
                    return None
                
                # Extract relevant metadata
                metadata = {
                    'title': info.get('title'),
                    'description': info.get('description'),
                    'duration': info.get('duration'),  # in seconds
                    'upload_date': info.get('upload_date'),
                    'uploader': info.get('uploader'),
                    'uploader_id': info.get('uploader_id'),
                    'view_count': info.get('view_count'),
                    'like_count': info.get('like_count'),
                    'dislike_count': info.get('dislike_count'),
                    'comment_count': info.get('comment_count'),
                    'tags': info.get('tags', []),
                    'categories': info.get('categories', []),
                    'thumbnail': info.get('thumbnail'),
                    'thumbnails': info.get('thumbnails', []),
                    'webpage_url': info.get('webpage_url'),
                    'original_url': info.get('original_url'),
                    'id': info.get('id'),
                    'extractor': info.get('extractor'),
                    'format_id': info.get('format_id'),
                    'width': info.get('width'),
                    'height': info.get('height'),
                    'fps': info.get('fps'),
                    'filesize': info.get('filesize'),
                    'language': info.get('language'),
                    'subtitles': list(info.get('subtitles', {}).keys()) if info.get('subtitles') else [],
                }
                
                return metadata
                
        except Exception as e:
            logger.error(f"yt-dlp extraction failed: {str(e)}")
            return None

    def _extract_with_browser(self, url: str) -> Optional[Dict]:
        """
        Extract metadata using browser automation for complex cases.

        This method handles:
        - 18+ age verification modals
        - Pre-roll ads that need to be skipped
        - Anti-adblock detection
        - Complex JavaScript-based video players
        """
        try:
            # Run browser automation in async context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    self.browser_handler.extract_video_info(url)
                )
                return result
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Browser automation extraction failed: {str(e)}")
            return None
    
    def _extract_platform_specific(self, url: str, platform: str) -> Optional[Dict]:
        """Platform-specific metadata extraction as fallback."""
        if platform == 'youtube':
            return self._extract_youtube_fallback(url)
        elif platform == 'vimeo':
            return self._extract_vimeo_fallback(url)
        else:
            return self._extract_generic_fallback(url)
    
    def _extract_youtube_fallback(self, url: str) -> Optional[Dict]:
        """Fallback YouTube metadata extraction using direct HTTP requests."""
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            html = response.text
            
            metadata = {}
            
            # Extract title
            title_match = re.search(r'<title>([^<]+)</title>', html)
            if title_match:
                metadata['title'] = title_match.group(1).replace(' - YouTube', '')
            
            # Extract description (from meta tag)
            desc_match = re.search(r'<meta name="description" content="([^"]*)"', html)
            if desc_match:
                metadata['description'] = desc_match.group(1)
            
            # Extract thumbnail
            thumb_match = re.search(r'"thumbnailUrl":"([^"]+)"', html)
            if thumb_match:
                metadata['thumbnail'] = thumb_match.group(1).replace('\\u0026', '&')
            
            # Extract uploader
            uploader_match = re.search(r'"author":"([^"]+)"', html)
            if uploader_match:
                metadata['uploader'] = uploader_match.group(1)
            
            return metadata if metadata else None
            
        except Exception as e:
            logger.error(f"YouTube fallback extraction failed: {str(e)}")
            return None
    
    def _extract_vimeo_fallback(self, url: str) -> Optional[Dict]:
        """Fallback Vimeo metadata extraction."""
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            html = response.text
            
            metadata = {}
            
            # Extract title
            title_match = re.search(r'<title>([^<]+)</title>', html)
            if title_match:
                metadata['title'] = title_match.group(1).replace(' on Vimeo', '')
            
            # Extract description
            desc_match = re.search(r'<meta name="description" content="([^"]*)"', html)
            if desc_match:
                metadata['description'] = desc_match.group(1)
            
            return metadata if metadata else None
            
        except Exception as e:
            logger.error(f"Vimeo fallback extraction failed: {str(e)}")
            return None
    
    def _extract_generic_fallback(self, url: str) -> Optional[Dict]:
        """Generic fallback metadata extraction for unsupported platforms."""
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            html = response.text
            
            metadata = {}
            
            # Extract title
            title_match = re.search(r'<title>([^<]+)</title>', html)
            if title_match:
                metadata['title'] = title_match.group(1).strip()
            
            # Extract description
            desc_match = re.search(r'<meta name="description" content="([^"]*)"', html)
            if desc_match:
                metadata['description'] = desc_match.group(1)
            
            # Extract Open Graph image (often the thumbnail)
            og_image_match = re.search(r'<meta property="og:image" content="([^"]*)"', html)
            if og_image_match:
                metadata['thumbnail'] = og_image_match.group(1)
            
            return metadata if metadata else None
            
        except Exception as e:
            logger.error(f"Generic fallback extraction failed: {str(e)}")
            return None
    
    def _normalize_metadata(self, metadata: Dict, platform: str) -> Dict[str, Any]:
        """Normalize and clean extracted metadata."""
        normalized = {
            'extraction_date': datetime.now().isoformat(),
            'platform': platform,
            'extraction_method': 'yt-dlp' if metadata.get('extractor') else 'fallback',
            'success': True,
            'error': None
        }
        
        # Title (required)
        normalized['title'] = self._sanitize_text(metadata.get('title', 'Unknown Title'))
        
        # Description (optional)
        description = metadata.get('description', '')
        if description:
            # Truncate very long descriptions
            if len(description) > 2000:
                description = description[:2000] + '...'
            normalized['description'] = self._sanitize_text(description)
        else:
            normalized['description'] = ''
        
        # Duration (convert to readable format)
        duration = metadata.get('duration')
        if duration:
            normalized['duration_seconds'] = duration
            normalized['duration_formatted'] = self._format_duration(duration)
        
        # Upload date
        upload_date = metadata.get('upload_date')
        if upload_date:
            try:
                # yt-dlp returns dates as YYYYMMDD
                if isinstance(upload_date, str) and len(upload_date) == 8:
                    date_obj = datetime.strptime(upload_date, '%Y%m%d')
                    normalized['upload_date'] = date_obj.isoformat()
                else:
                    normalized['upload_date'] = str(upload_date)
            except:
                normalized['upload_date'] = str(upload_date)
        
        # Uploader information
        normalized['uploader'] = self._sanitize_text(metadata.get('uploader', ''))
        normalized['uploader_id'] = metadata.get('uploader_id', '')
        
        # View and engagement metrics
        normalized['view_count'] = metadata.get('view_count', 0)
        normalized['like_count'] = metadata.get('like_count', 0)
        normalized['comment_count'] = metadata.get('comment_count', 0)
        
        # Tags and categories
        normalized['tags'] = metadata.get('tags', [])[:20]  # Limit to 20 tags
        normalized['categories'] = metadata.get('categories', [])
        
        # Thumbnail information
        normalized['thumbnail_url'] = metadata.get('thumbnail', '')
        
        # Technical information
        normalized['video_id'] = metadata.get('id', '')
        normalized['width'] = metadata.get('width', 0)
        normalized['height'] = metadata.get('height', 0)
        normalized['fps'] = metadata.get('fps', 0)
        
        # URLs
        normalized['webpage_url'] = metadata.get('webpage_url', '')
        normalized['original_url'] = metadata.get('original_url', '')
        
        return normalized
    
    def _create_error_metadata(self, url: str, error: str) -> Dict[str, Any]:
        """Create metadata structure for failed extractions."""
        return {
            'extraction_date': datetime.now().isoformat(),
            'success': False,
            'error': error,
            'original_url': url,
            'title': 'Extraction Failed',
            'description': f'Failed to extract metadata: {error}',
        }
    
    def _sanitize_text(self, text: str) -> str:
        """Sanitize text by removing problematic characters."""
        if not text:
            return ''
        
        # Remove or replace problematic characters
        text = re.sub(r'[^\w\s\-_.,!?()[\]{}:;"\']', '', text)
        
        # Normalize whitespace
        text = ' '.join(text.split())
        
        return text.strip()
    
    def _format_duration(self, seconds: int) -> str:
        """Format duration from seconds to HH:MM:SS or MM:SS."""
        if not seconds:
            return '00:00'
        
        duration = timedelta(seconds=seconds)
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
