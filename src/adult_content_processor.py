"""
Adult Content Video Processor for Video Search Indexer

This module handles video processing specifically for adult content sites that
require more aggressive browser automation techniques. It operates completely
separately from the standard YouTube-based video processor to avoid interference.

Features:
- Browser automation for direct video frame capture
- Aggressive ad blocking and modal handling
- Multiple fallback strategies for difficult sites
- Direct video stream extraction when possible
- Screenshot capture through browser automation
- Specialized handling for adult content platforms
"""

import os
import logging
import tempfile
import asyncio
import time
import base64
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from PIL import Image
import io

# Import browser automation
try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# Set up logging for this module
logger = logging.getLogger(__name__)


class AdultContentProcessor:
    """
    Specialized processor for adult content sites using aggressive browser automation.
    
    This processor is completely separate from the standard VideoProcessor and uses
    different techniques specifically designed for adult content platforms that
    block standard video extraction tools.
    
    Features:
    - Direct browser-based video frame capture
    - Aggressive ad blocking and popup handling
    - Age verification modal automation
    - Multiple extraction strategies with fallbacks
    - Specialized handling for each adult platform
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the adult content processor.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Screenshot extraction settings
        self.screenshot_interval = self.config.get('screenshot_interval', 10)  # seconds
        self.max_screenshots = self.config.get('max_screenshots', 50)
        self.screenshot_quality = self.config.get('screenshot_quality', 85)
        self.max_video_duration = self.config.get('max_video_duration', 3600)  # 1 hour
        
        # Browser automation settings
        self.page_timeout = self.config.get('page_timeout', 60000)  # 60 seconds
        self.video_load_timeout = self.config.get('video_load_timeout', 30000)  # 30 seconds
        self.ad_skip_timeout = self.config.get('ad_skip_timeout', 15000)  # 15 seconds
        
        # Create temporary directory for processing
        self.temp_dir = Path(tempfile.mkdtemp(prefix='adult_vid_search_'))
        logger.info(f"Created adult content temporary directory: {self.temp_dir}")
        
        # Platform-specific configurations
        self.platform_configs = {
            'xvideos': {
                'video_selectors': [
                    'video:nth-of-type(2)',  # Main video is usually the second video element (first is ad)
                    'video[src*="blob:"]',   # Free videos use blob URLs
                    'video[style*="visible"]',  # Visible video elements
                    'video',  # Standard video element
                    '#player video',
                    '.video-player video',
                    '#main video',  # xvideos uses #main container
                    '.main video',  # xvideos uses .main container
                    '.video video',  # xvideos uses .video container
                    '[id*="video"] video',
                    '[class*="video"] video',
                    'video[src]',
                    'video[data-src]',
                    'video[autoplay]'  # Many adult sites use autoplay
                ],
                'play_button_selectors': [
                    '.play-button',
                    '.video-play-button',
                    '[data-testid="play-button"]',
                    'button[aria-label*="play"]',
                    '.play',
                    '[class*="play"]'
                ],
                'ad_skip_selectors': [
                    '.skip-ad',
                    '.ad-skip-button',
                    '[data-testid="skip-ad"]',
                    'button:has-text("Skip")',
                    'button:has-text("Skip Ad")'
                ],
                'age_verification_selectors': [
                    'button:has-text("Enter - I am 18 years old or older")',  # Exact text from real site
                    'button:has-text("Enter")',
                    'button:has-text("I am 18")',
                    'button:has-text("Yes")',
                    '[data-testid="age-verification"]'
                ],
                'login_bypass_selectors': [
                    'button:has-text("Continue without account")',
                    'button:has-text("Skip")',
                    'button:has-text("Not now")',
                    'button:has-text("Maybe later")',
                    'a:has-text("Continue without")',
                    'a:has-text("Skip")',
                    'a:has-text("Continue")',
                    'button:has-text("Continue")',
                    'button:has-text("Watch")',
                    'button:has-text("Play")',
                    '.close',
                    '[aria-label="Close"]',
                    'button:has-text("×")',
                    '.modal-close',
                    '.popup-close',
                    '[class*="close"]',
                    '[id*="close"]'
                ],
                'login_modal_selectors': [
                    '.x-overlay.x-overlay-box.auto-width-popup',  # Specific xvideos modal
                    '[class*="x-overlay"]',
                    '[class*="login"]',
                    '[class*="signup"]',
                    '[class*="register"]',
                    '[id*="login"]',
                    '[id*="signup"]',
                    '.modal',
                    '[role="dialog"]'
                ],
                # Focus on free content only - avoid premium "RED" videos
                'free_content_indicators': [
                    ':not([class*="red"])',  # Avoid RED premium content
                    ':not([href*="/red/"])',  # Avoid RED premium links
                    '[class*="free"]',
                    'video[duration]'  # Videos with duration are usually accessible
                ]
            },
            'pornhub': {
                'video_selectors': [
                    'video',
                    '#player video',
                    '.video-player video',
                    '[id*="video"] video',
                    '[class*="video"] video'
                ],
                'play_button_selectors': [
                    '.play-button',
                    '.video-play-button',
                    '[data-testid="play-button"]'
                ],
                'ad_skip_selectors': [
                    '.skip-ad',
                    '.ad-skip-button',
                    'button:has-text("Skip")'
                ],
                'age_verification_selectors': [
                    'button:has-text("Enter")',
                    'button:has-text("I am 18")'
                ],
                'login_bypass_selectors': [
                    'button:has-text("Continue without account")',
                    'button:has-text("Skip")',
                    'button:has-text("Not now")',
                    'button:has-text("Maybe later")',
                    'a:has-text("Continue without")',
                    '.close',
                    '[aria-label="Close"]',
                    'button:has-text("×")'
                ],
                'login_modal_selectors': [
                    '[class*="login"]',
                    '[class*="signup"]',
                    '[class*="register"]',
                    '[id*="login"]',
                    '[id*="signup"]',
                    '.modal',
                    '[role="dialog"]'
                ]
            }
        }
    
    async def extract_screenshots_browser(self, url: str, output_dir: Path, platform: str, metadata: Dict = None) -> List[Path]:
        """
        Extract screenshots from adult content video using simplified browser automation.

        Args:
            url: Video URL to process
            output_dir: Directory to save screenshots
            platform: Platform name (xvideos, pornhub, etc.)
            metadata: Optional metadata containing video duration info

        Returns:
            List of paths to extracted screenshot files
        """
        if not PLAYWRIGHT_AVAILABLE:
            logger.error("Playwright not available for browser automation")
            return []

        logger.info(f"🔞 Processing adult content: {url}")

        # Create output directory
        output_dir.mkdir(parents=True, exist_ok=True)

        screenshots = []

        async with async_playwright() as p:
            try:
                # Launch browser with optimized settings
                browser = await p.chromium.launch(
                    headless=True,  # Use headless for production
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu',
                        '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    ]
                )

                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                )

                page = await context.new_page()

                # Navigate directly to the video page
                logger.info(f"📱 Navigating to: {url}")
                await page.goto(url, wait_until='networkidle', timeout=30000)

                # Handle age verification if present
                await self._handle_age_verification(page)

                # Find and prepare the main video element
                video_element = await self._find_main_video_element(page)
                if not video_element:
                    logger.warning("❌ Could not find main video element")
                    return []

                # Make video visible and start playback
                await self._prepare_video_for_capture(page, video_element)

                # Capture screenshots from video
                screenshots = await self._capture_video_screenshots_simple(page, video_element, output_dir, self.screenshot_interval)

                await browser.close()

                logger.info(f"✅ Successfully captured {len(screenshots)} screenshots from adult content")
                return screenshots

            except Exception as e:
                logger.error(f"❌ Error processing adult content: {str(e)}")
                try:
                    await browser.close()
                except:
                    pass
                return []
    
    async def _find_main_video_element(self, page):
        """
        Find the main video element on the page.
        Based on real-world testing, the main video is typically index 2 with a blob URL.
        """
        try:
            # Wait for page to load and video elements to be present
            await page.wait_for_timeout(5000)  # Give page time to load

            # Get all video elements (including hidden ones)
            video_elements = await page.query_selector_all('video')
            logger.info(f"Found {len(video_elements)} video elements")

            if not video_elements:
                logger.warning("No video elements found on page")
                return None

            # Wait a bit more for video metadata to load
            await page.wait_for_timeout(3000)

            # Look for the main video element (typically index 2 with blob URL)
            for i, video in enumerate(video_elements):
                try:
                    src = await video.get_attribute('src')

                    # Try to get duration, but don't fail if it's not available yet
                    try:
                        duration = await video.evaluate('element => element.duration')
                    except:
                        duration = None

                    logger.info(f"Video {i}: src={src}, duration={duration}")

                    # Main video typically has blob URL
                    if src and 'blob:' in src:
                        logger.info(f"✅ Found main video element at index {i} with blob URL")
                        return video

                except Exception as e:
                    logger.debug(f"Error checking video {i}: {str(e)}")
                    continue

            # Fallback: try to find any video with reasonable duration
            for i, video in enumerate(video_elements):
                try:
                    duration = await video.evaluate('element => element.duration')
                    if duration and duration > 30:  # More than 30 seconds
                        logger.info(f"✅ Using fallback video element at index {i}")
                        return video
                except Exception as e:
                    continue

            # Last resort: use the last video element (often the main content)
            if video_elements:
                logger.info(f"✅ Using last video element as fallback (index {len(video_elements)-1})")
                return video_elements[-1]

            return None

        except Exception as e:
            logger.error(f"Error finding main video element: {str(e)}")
            return None

    async def _handle_age_verification(self, page):
        """Handle age verification modal if present."""
        try:
            # Look for age verification button
            age_button_selectors = [
                'text="Enter - I am 18 years old or older"',
                'text="I am 18 years old or older"',
                'text="Enter"',
                '[data-testid="age-verification-enter"]',
                '.age-verification button',
                'button:has-text("18")'
            ]

            for selector in age_button_selectors:
                try:
                    button = await page.wait_for_selector(selector, timeout=3000)
                    if button:
                        await button.click()
                        logger.info("✅ Clicked age verification button")
                        await page.wait_for_timeout(2000)
                        return
                except:
                    continue

        except Exception as e:
            logger.debug(f"No age verification needed or failed: {str(e)}")

    async def _prepare_video_for_capture(self, page, video_element):
        """Prepare video element for screenshot capture."""
        try:
            # Scroll video into view
            await video_element.scroll_into_view_if_needed()

            # Make video visible by modifying CSS if needed
            await page.evaluate("""
                () => {
                    const videos = document.querySelectorAll('video');
                    videos.forEach(video => {
                        video.style.visibility = 'visible';
                        video.style.display = 'block';
                        video.style.opacity = '1';
                    });
                }
            """)

            # Try to start video playback
            try:
                await video_element.evaluate('element => element.play()')
                logger.info("✅ Started video playback")
            except:
                logger.debug("Could not start video playback")

            # Wait for video to be ready
            await page.wait_for_timeout(3000)

        except Exception as e:
            logger.warning(f"Error preparing video for capture: {str(e)}")

    async def _capture_video_screenshots_simple(self, page, video_element, output_dir: Path, interval: int):
        """
        Capture screenshots from video element at regular intervals.
        """
        screenshots = []

        try:
            # Try to get video duration, but handle NaN/undefined cases
            duration = None
            try:
                duration_raw = await video_element.evaluate('element => element.duration')
                if duration_raw and not (str(duration_raw).lower() in ['nan', 'infinity', 'undefined']):
                    duration = float(duration_raw)
            except:
                pass

            if not duration or duration <= 0:
                logger.warning("Could not get valid video duration, using default approach")
                # Use a default duration and capture screenshots anyway
                duration = 300  # 5 minutes default
                num_screenshots = 10  # Capture 10 screenshots
            else:
                logger.info(f"📹 Video duration: {duration:.2f} seconds")
                # Calculate number of screenshots
                num_screenshots = min(int(duration / interval), self.max_screenshots)

            logger.info(f"📸 Will capture {num_screenshots} screenshots at {interval}s intervals")

            # Capture screenshots at regular intervals
            for i in range(num_screenshots):
                try:
                    # Calculate timestamp
                    timestamp = i * interval

                    # Try to seek to timestamp (may not work for all videos)
                    try:
                        await video_element.evaluate(f'element => element.currentTime = {timestamp}')
                        await page.wait_for_timeout(1000)  # Wait for seek
                    except:
                        # If seeking fails, just wait and capture current frame
                        await page.wait_for_timeout(2000)

                    # Take screenshot of video element
                    screenshot_path = output_dir / f"screenshot_{i:04d}_{timestamp:06.2f}s.png"
                    await video_element.screenshot(path=str(screenshot_path))

                    screenshots.append(screenshot_path)
                    logger.info(f"📸 Captured screenshot {i+1}/{num_screenshots} at {timestamp:.2f}s")

                except Exception as e:
                    logger.warning(f"Failed to capture screenshot at {timestamp:.2f}s: {str(e)}")
                    continue

            return screenshots

        except Exception as e:
            logger.error(f"Error capturing video screenshots: {str(e)}")
            return screenshots

    async def _setup_aggressive_blocking(self, page):
        """Set up aggressive ad blocking and request filtering."""
        # Block ads, trackers, and unnecessary resources
        await page.route("**/*", lambda route: (
            route.abort() if any(blocked in route.request.url for blocked in [
                'googlesyndication', 'doubleclick', 'googletagmanager',
                'facebook.com', 'twitter.com', 'analytics',
                'ads', 'advertisement', 'tracking', 'metrics',
                'popup', 'banner', 'sponsor'
            ]) else route.continue_()
        ))
        
        # Set user agent to avoid detection
        await page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    async def _handle_adult_site_modals(self, page, platform: str):
        """Handle age verification modals for free content access."""
        config = self.platform_configs.get(platform, {})

        # Wait a moment for page to load
        await page.wait_for_timeout(3000)

        # Handle age verification first (this is what we need for free content)
        age_selectors = config.get('age_verification_selectors', [])
        for selector in age_selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=5000)
                if element:
                    logger.info(f"✅ Clicking age verification: {selector}")
                    await element.click()
                    await page.wait_for_timeout(2000)
                    logger.info("Age verification completed, should have access to free content")
                    return True
            except:
                continue

        # If no age verification button found, the page might already be accessible
        logger.info("No age verification modal found - page may already be accessible")
        return True

    async def _handle_login_modals(self, page, config: dict):
        """Handle login/signup modals that block content access."""
        login_bypass_selectors = config.get('login_bypass_selectors', [])
        login_modal_selectors = config.get('login_modal_selectors', [])

        # First, try to detect if there's a login modal
        login_modal_present = False
        for modal_selector in login_modal_selectors:
            try:
                modal = await page.wait_for_selector(modal_selector, timeout=2000)
                if modal:
                    logger.info(f"Detected login modal: {modal_selector}")
                    login_modal_present = True
                    break
            except:
                continue

        if login_modal_present:
            logger.info("Login modal detected, attempting aggressive bypass...")

            # PRIORITY METHOD: Try Escape key first (we know this works for xvideos)
            logger.info("🔑 Trying Escape key (known to work for xvideos)")
            await page.keyboard.press('Escape')
            await page.wait_for_timeout(3000)

            # Check if modal is gone after Escape
            modal_still_present = False
            for modal_selector in login_modal_selectors:
                try:
                    modal = await page.wait_for_selector(modal_selector, timeout=1000)
                    if modal:
                        modal_still_present = True
                        break
                except:
                    continue

            if not modal_still_present:
                logger.info("✅ Escape key successfully bypassed login modal!")
                return

            # If Escape didn't work, try bypass buttons
            logger.info("Escape key didn't work, trying bypass buttons...")
            for bypass_selector in login_bypass_selectors:
                try:
                    bypass_button = await page.wait_for_selector(bypass_selector, timeout=2000)
                    if bypass_button:
                        logger.info(f"Clicking login bypass: {bypass_selector}")
                        await bypass_button.click()
                        await page.wait_for_timeout(2000)
                        return
                except:
                    continue

            # Try more aggressive JavaScript removal
            logger.info("Attempting JavaScript modal removal...")
            try:
                await page.evaluate("""
                    // Remove all modal-like elements
                    const modals = document.querySelectorAll(`
                        [class*="modal"], [class*="popup"], [class*="overlay"],
                        [class*="login"], [class*="signup"], [class*="register"],
                        [id*="modal"], [id*="popup"], [id*="overlay"],
                        [id*="login"], [id*="signup"], [id*="register"],
                        [role="dialog"], [role="alertdialog"]
                    `);
                    modals.forEach(modal => {
                        modal.style.display = 'none';
                        modal.remove();
                    });

                    // Remove backdrop/overlay elements
                    const backdrops = document.querySelectorAll(`
                        [class*="backdrop"], [class*="overlay"], [class*="mask"]
                    `);
                    backdrops.forEach(backdrop => {
                        backdrop.style.display = 'none';
                        backdrop.remove();
                    });

                    // Enable scrolling if disabled
                    document.body.style.overflow = 'auto';
                    document.documentElement.style.overflow = 'auto';
                """)
                await page.wait_for_timeout(2000)
            except Exception as e:
                logger.warning(f"JavaScript modal removal failed: {str(e)}")

            # If no bypass button found, try pressing Escape key
            logger.info("Trying Escape key")
            await page.keyboard.press('Escape')
            await page.wait_for_timeout(1000)

            # Try clicking outside the modal
            logger.info("Trying to click outside modal")
            try:
                await page.click('body', position={'x': 10, 'y': 10})
                await page.wait_for_timeout(1000)
            except:
                pass

    async def _try_alternative_access(self, page, url: str, platform: str):
        """Try alternative methods to access video content when blocked by login."""
        logger.info("Attempting alternative access methods...")

        # Method 1: Try to find and click any "Watch" or "Play" buttons
        watch_buttons = [
            'button:has-text("Watch")',
            'button:has-text("Play")',
            'a:has-text("Watch")',
            'a:has-text("Play")',
            '[class*="watch"]',
            '[class*="play"]',
            '[id*="watch"]',
            '[id*="play"]'
        ]

        for selector in watch_buttons:
            try:
                button = await page.wait_for_selector(selector, timeout=2000)
                if button:
                    logger.info(f"Found watch/play button: {selector}")
                    await button.click()
                    await page.wait_for_timeout(3000)
                    return
            except:
                continue

        # Method 2: Try to access embed version of the video
        if platform == 'xvideos':
            try:
                # Extract video ID from URL
                import re
                video_id_match = re.search(r'video(\d+)', url)
                if video_id_match:
                    video_id = video_id_match.group(1)
                    embed_url = f"https://www.xvideos.com/embedframe/{video_id}"
                    logger.info(f"Trying embed URL: {embed_url}")
                    await page.goto(embed_url, timeout=self.page_timeout)
                    await page.wait_for_timeout(3000)
                    return
            except Exception as e:
                logger.warning(f"Embed URL attempt failed: {str(e)}")

        # Method 3: Try JavaScript execution to bypass restrictions
        try:
            logger.info("Attempting JavaScript bypass...")
            await page.evaluate("""
                // Remove login modals
                document.querySelectorAll('[class*="modal"], [class*="popup"], [class*="login"], [class*="signup"]').forEach(el => el.remove());

                // Try to find and click play buttons
                const playButtons = document.querySelectorAll('button, a, div');
                for (let btn of playButtons) {
                    const text = btn.textContent.toLowerCase();
                    if (text.includes('play') || text.includes('watch') || text.includes('continue')) {
                        btn.click();
                        break;
                    }
                }
            """)
            await page.wait_for_timeout(2000)
        except Exception as e:
            logger.warning(f"JavaScript bypass failed: {str(e)}")

    async def _prepare_video_for_capture(self, page, platform: str) -> Optional[Any]:
        """Find video element and prepare it for screenshot capture."""
        config = self.platform_configs.get(platform, {})
        video_selectors = config.get('video_selectors', ['video'])

        video_element = None

        try:
            # Try multiple video selectors with intelligent selection
            for selector in video_selectors:
                try:
                    logger.info(f"Trying video selector: {selector}")

                    # For xvideos, we need to be smart about which video element to use
                    if platform == 'xvideos' and selector == 'video':
                        # Get all video elements and analyze them
                        video_elements = await page.query_selector_all('video')
                        logger.info(f"Found {len(video_elements)} video elements")

                        # Analyze each video element to find the main one
                        for i, element in enumerate(video_elements):
                            try:
                                # Check if video has blob URL (indicates main video)
                                src = await element.get_attribute('src')
                                style = await element.get_attribute('style')

                                logger.info(f"Video Element #{i}: src={src}, style={style}")

                                # Prefer blob URLs (main video) over regular URLs (ads)
                                if src and 'blob:' in src:
                                    logger.info(f"✅ Selected main video element #{i} with blob URL")
                                    video_element = element
                                    break
                                # If no blob URL, prefer visible videos (index 1 usually)
                                elif i == 1 and (not style or 'display: none' not in style):
                                    logger.info(f"✅ Selected video element #{i} as main video (index 1)")
                                    video_element = element
                                    break
                            except Exception as e:
                                logger.debug(f"Error analyzing video element #{i}: {e}")
                                continue

                        if video_element:
                            break
                    else:
                        # For other selectors or platforms, use standard approach
                        video_element = await page.wait_for_selector(selector, timeout=5000)
                        if video_element:
                            logger.info(f"Found video element with selector: {selector}")
                            break
                except Exception as e:
                    logger.debug(f"Video selector failed: {selector} - {e}")
                    continue

            if not video_element:
                logger.error("No video element found with any selector")
                return None
            
            logger.info("Found video element, attempting to start playback")
            
            # Try to click play button
            play_selectors = config.get('play_button_selectors', [])
            for selector in play_selectors:
                try:
                    play_button = await page.wait_for_selector(selector, timeout=3000)
                    if play_button:
                        logger.info(f"Clicking play button: {selector}")
                        await play_button.click()
                        await page.wait_for_timeout(2000)
                        break
                except:
                    continue
            
            # Try clicking on video itself to start playback
            try:
                await video_element.click()
                await page.wait_for_timeout(2000)
            except:
                pass
            
            # Handle any ads that appear
            await self._handle_video_ads(page, platform)
            
            return video_element
            
        except Exception as e:
            logger.error(f"Failed to prepare video for capture: {str(e)}")
            return None
    
    async def _handle_video_ads(self, page, platform: str):
        """Handle video ads with aggressive skipping."""
        config = self.platform_configs.get(platform, {})
        ad_skip_selectors = config.get('ad_skip_selectors', [])
        
        # Wait for potential ads to load
        await page.wait_for_timeout(3000)
        
        # Try to skip ads
        for selector in ad_skip_selectors:
            try:
                skip_button = await page.wait_for_selector(selector, timeout=2000)
                if skip_button:
                    logger.info(f"Skipping ad: {selector}")
                    await skip_button.click()
                    await page.wait_for_timeout(1000)
            except:
                continue
        
        # Wait for ad to finish if skip didn't work
        await page.wait_for_timeout(5000)
    
    def cleanup_temp_directory(self):
        """Clean up temporary directory."""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary directory: {str(e)}")
    
    async def _capture_video_screenshots(self, page, video_element: Any, output_dir: Path, platform: str) -> List[Path]:
        """Capture screenshots from video at regular intervals."""
        screenshots = []

        try:
            # Get video duration if possible
            duration = await page.evaluate("""
                () => {
                    const video = document.querySelector('video');
                    return video ? video.duration : 0;
                }
            """)

            if not duration or duration <= 0:
                logger.warning("Could not get video duration, using default capture strategy")
                duration = 300  # Default to 5 minutes

            logger.info(f"Video duration: {duration} seconds")

            # Calculate screenshot intervals
            total_screenshots = min(int(duration / self.screenshot_interval), self.max_screenshots)
            if total_screenshots < 1:
                total_screenshots = 1

            interval = duration / total_screenshots
            logger.info(f"Will capture {total_screenshots} screenshots at {interval:.1f}s intervals")

            # Capture screenshots
            for i in range(total_screenshots):
                try:
                    # Seek to specific time
                    seek_time = i * interval

                    await page.evaluate(f"""
                        () => {{
                            const videos = document.querySelectorAll('video');
                            // Find the main video (usually index 1 for xvideos)
                            const video = videos.length > 1 ? videos[1] : videos[0];
                            if (video) {{
                                video.currentTime = {seek_time};
                            }}
                        }}
                    """)

                    # Wait for video to seek
                    await page.wait_for_timeout(2000)

                    # Re-find the video element to avoid DOM detachment issues
                    current_video_element = None
                    config = self.platform_configs.get(platform, {})
                    video_selectors = config.get('video_selectors', ['video'])

                    # Try to find the current video element
                    for selector in video_selectors:
                        try:
                            if platform == 'xvideos' and selector == 'video':
                                # For xvideos, get all videos and select the main one
                                video_elements = await page.query_selector_all('video')
                                if len(video_elements) > 1:
                                    current_video_element = video_elements[1]  # Main video
                                elif len(video_elements) > 0:
                                    current_video_element = video_elements[0]
                                break
                            else:
                                current_video_element = await page.query_selector(selector)
                                if current_video_element:
                                    break
                        except:
                            continue

                    if not current_video_element:
                        logger.warning(f"Could not find video element for screenshot {i}")
                        continue

                    # Take screenshot of current video element
                    screenshot_data = await current_video_element.screenshot()

                    # Save screenshot
                    screenshot_path = output_dir / f"screenshot_{i:04d}_{seek_time:.1f}s.jpg"

                    # Convert to PIL Image and save with quality
                    image = Image.open(io.BytesIO(screenshot_data))
                    image.save(screenshot_path, 'JPEG', quality=self.screenshot_quality)

                    screenshots.append(screenshot_path)
                    logger.info(f"Captured screenshot {i+1}/{total_screenshots} at {seek_time:.1f}s")

                except Exception as e:
                    logger.warning(f"Failed to capture screenshot {i}: {str(e)}")
                    continue

            return screenshots

        except Exception as e:
            logger.error(f"Error during video screenshot capture: {str(e)}")
            return screenshots

    async def _capture_page_screenshots_fallback(self, page, output_dir: Path, platform: str) -> List[Path]:
        """Fallback method to capture page screenshots when video element isn't found."""
        screenshots = []

        try:
            logger.info("Using fallback page screenshot capture method")

            # Wait for page to fully load
            await page.wait_for_timeout(5000)

            # Try to find any video-like elements and click them
            video_like_selectors = [
                '[class*="video"]',
                '[id*="video"]',
                '[class*="player"]',
                '[id*="player"]',
                'iframe',
                'embed'
            ]

            for selector in video_like_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        logger.info(f"Found {len(elements)} elements with selector: {selector}")
                        # Click the first one to try to activate video
                        await elements[0].click()
                        await page.wait_for_timeout(2000)
                        break
                except:
                    continue

            # Take multiple screenshots at different intervals
            for i in range(min(5, self.max_screenshots)):  # Limit to 5 screenshots for fallback
                try:
                    # Wait between screenshots
                    if i > 0:
                        await page.wait_for_timeout(3000)

                    # Take full page screenshot
                    screenshot_data = await page.screenshot(full_page=False)

                    # Save screenshot
                    screenshot_path = output_dir / f"fallback_screenshot_{i:04d}.jpg"

                    # Convert to PIL Image and save with quality
                    image = Image.open(io.BytesIO(screenshot_data))
                    image.save(screenshot_path, 'JPEG', quality=self.screenshot_quality)

                    screenshots.append(screenshot_path)
                    logger.info(f"Captured fallback screenshot {i+1}")

                except Exception as e:
                    logger.warning(f"Failed to capture fallback screenshot {i}: {str(e)}")
                    continue

            logger.info(f"Fallback capture completed: {len(screenshots)} screenshots")
            return screenshots

        except Exception as e:
            logger.error(f"Error during fallback screenshot capture: {str(e)}")
            return screenshots

    def __del__(self):
        """Cleanup when object is destroyed."""
        self.cleanup_temp_directory()
