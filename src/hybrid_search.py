"""
Hybrid Search Engine for Video Content

Combines literal text search with semantic/conceptual matching for comprehensive search results.
Designed to handle all content types without filtering or censorship.
"""

import logging
import re
from typing import List, Dict, Any, Set, Tuple, Optional
from pathlib import Path
import json

logger = logging.getLogger(__name__)

class QueryExpander:
    """
    Expands search queries with related terms, synonyms, and alternative phrasings.
    No content filtering - handles all types of queries including adult content.
    """
    
    def __init__(self):
        """Initialize query expander with comprehensive term mappings."""
        self.expansions = self._load_expansion_mappings()
    
    def _load_expansion_mappings(self) -> Dict[str, List[str]]:
        """
        Load comprehensive term expansion mappings.
        Includes all types of content without censorship or filtering.
        """
        return {
            # Basic actions and movements
            'walking': ['strolling', 'moving', 'stepping', 'pacing', 'wandering', 'hiking'],
            'running': ['jogging', 'sprinting', 'racing', 'dashing', 'rushing'],
            'sitting': ['seated', 'resting', 'positioned', 'perched'],
            'standing': ['upright', 'vertical', 'erect', 'posed'],
            'dancing': ['moving', 'grooving', 'swaying', 'performing', 'choreography'],
            'jumping': ['leaping', 'bouncing', 'hopping', 'vaulting'],
            
            # Emotions and expressions
            'happy': ['joyful', 'cheerful', 'smiling', 'pleased', 'delighted', 'content'],
            'sad': ['unhappy', 'depressed', 'melancholy', 'sorrowful', 'crying', 'tearful'],
            'angry': ['mad', 'furious', 'enraged', 'irritated', 'upset'],
            'excited': ['enthusiastic', 'energetic', 'thrilled', 'animated'],
            'calm': ['peaceful', 'serene', 'relaxed', 'tranquil', 'quiet'],
            'surprised': ['shocked', 'amazed', 'astonished', 'startled'],
            
            # Physical descriptions
            'beautiful': ['attractive', 'gorgeous', 'stunning', 'pretty', 'lovely'],
            'ugly': ['unattractive', 'hideous', 'unsightly'],
            'tall': ['high', 'elevated', 'towering', 'lengthy'],
            'short': ['small', 'brief', 'compact', 'petite'],
            'fat': ['overweight', 'heavy', 'large', 'big', 'thick'],
            'thin': ['skinny', 'slim', 'slender', 'lean', 'narrow'],
            'strong': ['powerful', 'muscular', 'robust', 'sturdy'],
            'weak': ['frail', 'feeble', 'fragile', 'delicate'],
            
            # Body parts and anatomy (uncensored)
            'face': ['head', 'facial', 'visage', 'countenance'],
            'body': ['figure', 'form', 'physique', 'anatomy'],
            'chest': ['torso', 'breast', 'bosom'],
            'legs': ['limbs', 'thighs', 'calves'],
            'arms': ['limbs', 'hands', 'forearms'],
            'butt': ['bottom', 'rear', 'behind', 'ass', 'buttocks'],
            'breasts': ['chest', 'boobs', 'tits', 'bosom'],
            'penis': ['dick', 'cock', 'member', 'phallus'],
            'vagina': ['pussy', 'cunt', 'vulva', 'genitals'],
            
            # Adult/sexual content (uncensored)
            'sex': ['intercourse', 'fucking', 'screwing', 'making love', 'intimate', 'sexual'],
            'naked': ['nude', 'bare', 'undressed', 'unclothed', 'exposed'],
            'kissing': ['making out', 'smooching', 'lip contact', 'romantic'],
            'touching': ['caressing', 'fondling', 'groping', 'feeling'],
            'orgasm': ['climax', 'cumming', 'coming', 'release'],
            'masturbation': ['self-pleasure', 'touching oneself', 'solo'],
            
            # Clothing and appearance
            'clothes': ['clothing', 'garments', 'attire', 'outfit', 'dress'],
            'underwear': ['lingerie', 'panties', 'bra', 'boxers', 'briefs'],
            'bikini': ['swimsuit', 'bathing suit', 'swimwear'],
            'dress': ['gown', 'frock', 'outfit'],
            'shirt': ['top', 'blouse', 'tee'],
            'pants': ['trousers', 'jeans', 'slacks'],
            
            # Locations and settings
            'bedroom': ['bed', 'sleeping area', 'private room'],
            'bathroom': ['restroom', 'toilet', 'shower', 'bath'],
            'kitchen': ['cooking area', 'dining'],
            'outside': ['outdoor', 'exterior', 'outdoors'],
            'inside': ['indoor', 'interior', 'indoors'],
            'beach': ['shore', 'seaside', 'coast', 'sand'],
            'pool': ['swimming pool', 'water'],
            
            # Activities and scenarios
            'party': ['celebration', 'gathering', 'event', 'social'],
            'workout': ['exercise', 'fitness', 'training', 'gym'],
            'shower': ['bathing', 'washing', 'cleaning'],
            'sleeping': ['resting', 'napping', 'lying down'],
            'eating': ['dining', 'consuming', 'feeding'],
            'drinking': ['consuming', 'sipping', 'alcohol'],
            
            # Intensity and quality descriptors
            'hot': ['sexy', 'attractive', 'steamy', 'heated', 'warm'],
            'wet': ['moist', 'damp', 'soaked', 'liquid'],
            'hard': ['firm', 'solid', 'stiff', 'rigid'],
            'soft': ['gentle', 'tender', 'smooth', 'delicate'],
            'rough': ['aggressive', 'forceful', 'harsh', 'violent'],
            'gentle': ['soft', 'tender', 'careful', 'mild'],
            
            # Colors and visual elements
            'red': ['crimson', 'scarlet', 'cherry'],
            'blue': ['azure', 'navy', 'cyan'],
            'black': ['dark', 'ebony', 'noir'],
            'white': ['pale', 'ivory', 'snow'],
            'bright': ['vivid', 'brilliant', 'luminous', 'glowing'],
            'dark': ['dim', 'shadowy', 'black', 'gloomy'],
            
            # Age and demographics (uncensored)
            'young': ['teen', 'teenage', 'adolescent', 'youth', 'minor'],
            'old': ['elderly', 'senior', 'aged', 'mature'],
            'adult': ['grown-up', 'mature', 'of age'],
            'child': ['kid', 'minor', 'youth', 'juvenile'],
            
            # Gender and identity
            'man': ['male', 'guy', 'gentleman', 'dude'],
            'woman': ['female', 'lady', 'girl', 'gal'],
            'gay': ['homosexual', 'same-sex', 'queer'],
            'lesbian': ['gay woman', 'homosexual woman'],
            'trans': ['transgender', 'transsexual'],
            
            # Technology and objects
            'phone': ['mobile', 'smartphone', 'device'],
            'computer': ['laptop', 'pc', 'device', 'screen'],
            'camera': ['recording', 'filming', 'photo'],
            'car': ['vehicle', 'automobile', 'auto'],
            'bed': ['mattress', 'sleeping area'],
            
            # Common verbs and actions
            'looking': ['watching', 'viewing', 'observing', 'staring', 'gazing'],
            'talking': ['speaking', 'chatting', 'conversing', 'discussing'],
            'laughing': ['giggling', 'chuckling', 'smiling', 'amused'],
            'crying': ['weeping', 'sobbing', 'tears', 'emotional'],
            'playing': ['gaming', 'having fun', 'entertaining'],
            'working': ['laboring', 'employed', 'job', 'task'],
        }
    
    def expand_query(self, query: str) -> Dict[str, Any]:
        """
        Expand a search query with related terms and concepts.
        
        Args:
            query: Original search query
            
        Returns:
            Dictionary with original query and expanded terms
        """
        original_terms = self._extract_terms(query)
        expanded_terms = set()
        expansion_map = {}
        
        # Expand each term
        for term in original_terms:
            term_lower = term.lower()
            if term_lower in self.expansions:
                related_terms = self.expansions[term_lower]
                expanded_terms.update(related_terms)
                expansion_map[term] = related_terms
        
        # Create expanded query variations
        expanded_queries = []
        
        # Add original query
        expanded_queries.append(query)
        
        # Add queries with individual expanded terms
        for original_term, related_terms in expansion_map.items():
            for related_term in related_terms[:3]:  # Limit to top 3 to avoid explosion
                expanded_query = query.replace(original_term, related_term)
                if expanded_query != query:
                    expanded_queries.append(expanded_query)
        
        # Add conceptual combinations
        if len(expanded_terms) > 0:
            # Create a query with multiple related terms
            conceptual_query = ' '.join(list(expanded_terms)[:5])  # Top 5 related terms
            expanded_queries.append(conceptual_query)
        
        return {
            'original_query': query,
            'original_terms': list(original_terms),
            'expanded_terms': list(expanded_terms),
            'expanded_queries': expanded_queries[:10],  # Limit to prevent overload
            'expansion_map': expansion_map
        }
    
    def _extract_terms(self, query: str) -> Set[str]:
        """Extract meaningful terms from a query."""
        # Remove punctuation and split
        clean_query = re.sub(r'[^\w\s]', ' ', query)
        terms = clean_query.split()
        
        # Filter out very short terms and common stop words
        stop_words = {'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
        
        meaningful_terms = set()
        for term in terms:
            if len(term) > 2 and term.lower() not in stop_words:
                meaningful_terms.add(term)
        
        return meaningful_terms

class HybridSearchEngine:
    """
    Advanced search engine that combines multiple search methods for comprehensive results.
    """
    
    def __init__(self, storage, ai_analyzer=None):
        """
        Initialize hybrid search engine.
        
        Args:
            storage: VideoStorage instance
            ai_analyzer: AI analyzer for semantic search (optional)
        """
        self.storage = storage
        self.ai_analyzer = ai_analyzer
        self.query_expander = QueryExpander()
        
        logger.info("Hybrid search engine initialized")
    
    def search(self, query: str, limit: int = 50) -> Dict[str, Any]:
        """
        Perform comprehensive hybrid search combining multiple methods.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            Dictionary with search results and metadata
        """
        if not query.strip():
            return {
                'success': False,
                'error': 'Empty query',
                'results': []
            }
        
        logger.info(f"Performing hybrid search for: '{query}'")
        
        # Expand the query
        expansion_data = self.query_expander.expand_query(query)
        
        # Collect results from different search methods
        all_results = []
        search_metadata = {
            'original_query': query,
            'expansion_data': expansion_data,
            'search_methods_used': []
        }
        
        # 1. Literal text search (highest weight for exact matches)
        literal_results = self._literal_search(query, limit)
        if literal_results:
            all_results.extend([(result, 1.0, 'literal') for result in literal_results])
            search_metadata['search_methods_used'].append('literal')
        
        # 2. Expanded text searches (medium weight)
        for expanded_query in expansion_data['expanded_queries'][1:]:  # Skip original
            expanded_results = self._literal_search(expanded_query, limit // 2)
            if expanded_results:
                all_results.extend([(result, 0.7, 'expanded') for result in expanded_results])
        
        if expansion_data['expanded_queries']:
            search_metadata['search_methods_used'].append('expanded')
        
        # 3. Semantic/AI search (medium weight for conceptual matches)
        if self.ai_analyzer and self.ai_analyzer.is_available():
            semantic_results = self._semantic_search(query, limit)
            if semantic_results:
                all_results.extend([(result, 0.8, 'semantic') for result in semantic_results])
                search_metadata['search_methods_used'].append('semantic')
            
            # Also search with expanded terms
            for expanded_query in expansion_data['expanded_queries'][1:3]:  # Top 2 expanded
                expanded_semantic = self._semantic_search(expanded_query, limit // 3)
                if expanded_semantic:
                    all_results.extend([(result, 0.6, 'semantic_expanded') for result in expanded_semantic])
        
        # 4. Combine and rank results
        final_results = self._combine_and_rank_results(all_results, limit)
        
        search_metadata['total_raw_results'] = len(all_results)
        search_metadata['final_results_count'] = len(final_results)
        
        return {
            'success': True,
            'query': query,
            'results': final_results,
            'metadata': search_metadata
        }
    
    def _literal_search(self, query: str, limit: int) -> List[Dict]:
        """Perform literal text search using FTS."""
        try:
            return self.storage.search_videos(query, limit=limit)
        except Exception as e:
            logger.error(f"Literal search error: {e}")
            return []
    
    def _semantic_search(self, query: str, limit: int) -> List[Dict]:
        """Perform semantic search using AI embeddings."""
        try:
            query_embedding = self.ai_analyzer.encode_text(query)
            if query_embedding:
                return self.storage.search_videos_by_embedding(query_embedding, limit=limit)
            return []
        except Exception as e:
            logger.error(f"Semantic search error: {e}")
            return []
    
    def _combine_and_rank_results(self, all_results: List[Tuple], limit: int) -> List[Dict]:
        """
        Combine results from different search methods and rank them intelligently.
        
        Args:
            all_results: List of (result_dict, weight, method) tuples
            limit: Maximum number of final results
            
        Returns:
            Ranked and deduplicated results
        """
        # Deduplicate by video ID while preserving best scores
        video_scores = {}
        
        for result, weight, method in all_results:
            video_id = result.get('id')
            if not video_id:
                continue
            
            # Calculate combined score
            base_score = result.get('similarity', 0.5)  # Default similarity for text results
            combined_score = base_score * weight
            
            if video_id not in video_scores:
                video_scores[video_id] = {
                    'result': result,
                    'score': combined_score,
                    'methods': [method],
                    'method_scores': {method: combined_score}
                }
            else:
                # Boost score for videos found by multiple methods
                existing = video_scores[video_id]
                existing['score'] = max(existing['score'], combined_score)
                existing['methods'].append(method)
                existing['method_scores'][method] = combined_score
                
                # Bonus for multi-method matches
                if len(existing['methods']) > 1:
                    existing['score'] *= 1.2
        
        # Sort by combined score
        ranked_videos = sorted(video_scores.values(), key=lambda x: x['score'], reverse=True)
        
        # Prepare final results with metadata
        final_results = []
        for item in ranked_videos[:limit]:
            result = item['result'].copy()
            result['hybrid_score'] = item['score']
            result['search_methods'] = list(set(item['methods']))  # Remove duplicates
            result['method_scores'] = item['method_scores']
            final_results.append(result)
        
        return final_results
