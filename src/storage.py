"""
Storage and Persistence Layer for Video Search Indexer

This module provides advanced storage, search, and retrieval functionality
for indexed video content and metadata.
"""

import json
import sqlite3
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import re

# Set up logging for this module
logger = logging.getLogger(__name__)


class VideoStorage:
    """
    Advanced storage system for indexed video content with search capabilities.
    
    Features:
    - SQLite database for fast metadata queries
    - Full-text search across video metadata
    - Tag-based filtering and organization
    - Content statistics and analytics
    - Backup and restore functionality
    - Index optimization and maintenance
    """
    
    def __init__(self, db_path: str = "data/video_index.db"):
        """
        Initialize the video storage system.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        logger.info(f"Video storage initialized: {self.db_path}")
    
    def _init_database(self):
        """Initialize SQLite database with required tables."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS videos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    platform TEXT NOT NULL,
                    domain TEXT NOT NULL,
                    video_id TEXT,
                    uploader TEXT,
                    duration_seconds INTEGER,
                    upload_date TEXT,
                    view_count INTEGER,
                    like_count INTEGER,
                    thumbnail_url TEXT,
                    video_dir TEXT NOT NULL,
                    screenshot_count INTEGER DEFAULT 0,
                    extraction_date TEXT NOT NULL,
                    status TEXT DEFAULT 'completed',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    tag TEXT NOT NULL,
                    FOREIGN KEY (video_id) REFERENCES videos (id) ON DELETE CASCADE
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    category TEXT NOT NULL,
                    FOREIGN KEY (video_id) REFERENCES videos (id) ON DELETE CASCADE
                )
            ''')

            # AI-generated tags table for visual content analysis
            conn.execute('''
                CREATE TABLE IF NOT EXISTS ai_tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    tag TEXT NOT NULL,
                    confidence REAL DEFAULT 0.0,
                    source TEXT DEFAULT 'ai_analysis',
                    FOREIGN KEY (video_id) REFERENCES videos (id) ON DELETE CASCADE
                )
            ''')

            # Frame analysis table for per-frame AI analysis results
            conn.execute('''
                CREATE TABLE IF NOT EXISTS frame_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    frame_path TEXT NOT NULL,
                    frame_number INTEGER,
                    timestamp_seconds REAL,
                    analysis_success BOOLEAN DEFAULT 0,
                    model_name TEXT DEFAULT 'ViT-B-32',
                    image_width INTEGER,
                    image_height INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id) ON DELETE CASCADE
                )
            ''')

            # Frame embeddings table for storing CLIP embeddings
            conn.execute('''
                CREATE TABLE IF NOT EXISTS frame_embeddings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    frame_analysis_id INTEGER,
                    embedding_vector TEXT NOT NULL,  -- JSON array of 512 float values
                    embedding_dim INTEGER DEFAULT 512,
                    FOREIGN KEY (frame_analysis_id) REFERENCES frame_analysis (id) ON DELETE CASCADE
                )
            ''')

            # Video summaries table for storing AI-generated video-level summaries
            conn.execute('''
                CREATE TABLE IF NOT EXISTS video_summaries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    summary TEXT,
                    key_themes TEXT,  -- JSON array of key themes
                    dominant_tags TEXT,  -- JSON array of dominant tags
                    frame_count INTEGER DEFAULT 0,
                    successful_frames INTEGER DEFAULT 0,
                    description_coverage TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id) ON DELETE CASCADE
                )
            ''')

            # Frame descriptions table for storing BLIP-generated descriptions
            conn.execute('''
                CREATE TABLE IF NOT EXISTS frame_descriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    frame_analysis_id INTEGER,
                    description TEXT NOT NULL,
                    auto_tags TEXT,  -- JSON array of auto-extracted tags
                    model_name TEXT DEFAULT 'blip-image-captioning-base',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (frame_analysis_id) REFERENCES frame_analysis (id) ON DELETE CASCADE
                )
            ''')

            # Create indexes for better search performance
            conn.execute('CREATE INDEX IF NOT EXISTS idx_videos_platform ON videos (platform)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_videos_domain ON videos (domain)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_videos_title ON videos (title)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_videos_status ON videos (status)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_tags_tag ON tags (tag)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_categories_category ON categories (category)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_ai_tags_tag ON ai_tags (tag)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_frame_analysis_video_id ON frame_analysis (video_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_frame_analysis_frame_number ON frame_analysis (frame_number)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_video_summaries_video_id ON video_summaries (video_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_frame_descriptions_frame_analysis_id ON frame_descriptions (frame_analysis_id)')

            # Enable full-text search (updated to include AI tags and summaries)
            conn.execute('''
                CREATE VIRTUAL TABLE IF NOT EXISTS videos_fts USING fts5(
                    title, description, uploader, tags, categories, ai_tags, video_summary, frame_descriptions,
                    content='videos',
                    content_rowid='id'
                )
            ''')
            
            conn.commit()
    
    def store_video(self, metadata: Dict[str, Any], video_dir: str, screenshot_count: int = 0) -> int:
        """
        Store video metadata in the database.
        
        Args:
            metadata: Video metadata dictionary
            video_dir: Path to video directory
            screenshot_count: Number of screenshots extracted
            
        Returns:
            Database ID of the stored video
        """
        with sqlite3.connect(self.db_path) as conn:
            # Insert main video record
            cursor = conn.execute('''
                INSERT OR REPLACE INTO videos (
                    url, title, description, platform, domain, video_id,
                    uploader, duration_seconds, upload_date, view_count,
                    like_count, thumbnail_url, video_dir, screenshot_count,
                    extraction_date, status, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metadata.get('original_url', ''),
                metadata.get('title', ''),
                metadata.get('description', ''),
                metadata.get('platform', ''),
                metadata.get('domain', ''),
                metadata.get('video_id', ''),
                metadata.get('uploader', ''),
                metadata.get('duration_seconds', 0),
                metadata.get('upload_date', ''),
                metadata.get('view_count', 0),
                metadata.get('like_count', 0),
                metadata.get('thumbnail_url', ''),
                video_dir,
                screenshot_count,
                metadata.get('extraction_date', datetime.now().isoformat()),
                'completed',
                datetime.now().isoformat()
            ))
            
            video_id = cursor.lastrowid
            
            # Store tags
            tags = metadata.get('tags', [])
            if tags:
                conn.execute('DELETE FROM tags WHERE video_id = ?', (video_id,))
                for tag in tags:
                    if tag:  # Skip empty tags
                        conn.execute('INSERT INTO tags (video_id, tag) VALUES (?, ?)', 
                                   (video_id, tag.strip()))
            
            # Store categories
            categories = metadata.get('categories', [])
            if categories:
                conn.execute('DELETE FROM categories WHERE video_id = ?', (video_id,))
                for category in categories:
                    if category:  # Skip empty categories
                        conn.execute('INSERT INTO categories (video_id, category) VALUES (?, ?)',
                                   (video_id, category.strip()))

            # Process and store AI analysis data
            ai_tags = self._process_ai_analysis(conn, video_id, video_dir)

            # Update full-text search index (now includes AI tags)
            self._update_fts_index(conn, video_id, metadata, tags, categories, ai_tags)
            
            conn.commit()
            
        logger.info(f"Stored video in database: {metadata.get('title', 'Unknown')} (ID: {video_id})")
        return video_id

    def _process_ai_analysis(self, conn: sqlite3.Connection, video_id: int, video_dir: str) -> List[str]:
        """
        Process enhanced AI analysis data from JSON files and store in database.

        Args:
            conn: Database connection
            video_id: Video database ID
            video_dir: Path to video directory containing AI analysis files

        Returns:
            List of AI-generated tags for FTS indexing
        """
        video_path = Path(video_dir)
        ai_analysis_file = video_path / "ai_analysis.json"
        ai_embeddings_file = video_path / "screenshots" / "ai_embeddings.json"
        enhanced_analysis_file = video_path / "screenshots" / "ai_analysis_enhanced.json"

        ai_tags = []

        try:
            # Clear existing AI analysis data for this video
            conn.execute('DELETE FROM ai_tags WHERE video_id = ?', (video_id,))
            conn.execute('''
                DELETE FROM frame_embeddings WHERE frame_analysis_id IN (
                    SELECT id FROM frame_analysis WHERE video_id = ?
                )
            ''', (video_id,))
            conn.execute('DELETE FROM frame_analysis WHERE video_id = ?', (video_id,))

            # Clear existing video summary and description data
            conn.execute('DELETE FROM video_summaries WHERE video_id = ?', (video_id,))
            conn.execute('''
                DELETE FROM frame_descriptions WHERE frame_analysis_id IN (
                    SELECT id FROM frame_analysis WHERE video_id = ?
                )
            ''', (video_id,))

            # Process enhanced AI analysis if it exists
            if enhanced_analysis_file.exists():
                logger.info(f"Processing enhanced AI analysis for video {video_id}")
                with open(enhanced_analysis_file, 'r') as f:
                    enhanced_data = json.load(f)

                # Store video-level summary
                video_summary = enhanced_data.get('video_summary', {})
                if video_summary:
                    conn.execute('''
                        INSERT INTO video_summaries (
                            video_id, summary, key_themes, dominant_tags,
                            frame_count, successful_frames, description_coverage
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        video_id,
                        video_summary.get('summary', ''),
                        json.dumps(video_summary.get('key_themes', [])),
                        json.dumps(video_summary.get('dominant_tags', [])),
                        video_summary.get('frame_count', 0),
                        video_summary.get('successful_frames', 0),
                        video_summary.get('description_coverage', '0/0')
                    ))

                # Process frame-level analysis results
                analysis_results = enhanced_data.get('analysis_results', [])
                descriptions = enhanced_data.get('descriptions', {})
                frame_tags = enhanced_data.get('frame_tags', {})

                for result in analysis_results:
                    if result.get('success', False):
                        image_path = result['image_path']
                        image_name = Path(image_path).name

                        # Store frame analysis
                        cursor = conn.execute('''
                            INSERT INTO frame_analysis (
                                video_id, frame_path, analysis_success, model_name
                            ) VALUES (?, ?, ?, ?)
                        ''', (video_id, image_path, True, 'enhanced_clip_blip'))

                        frame_analysis_id = cursor.lastrowid

                        # Store embedding
                        if 'embedding' in result:
                            conn.execute('''
                                INSERT INTO frame_embeddings (frame_analysis_id, embedding_vector)
                                VALUES (?, ?)
                            ''', (frame_analysis_id, json.dumps(result['embedding'])))

                        # Store description and auto tags
                        description = descriptions.get(image_name, '')
                        auto_tags = frame_tags.get(image_name, [])

                        if description or auto_tags:
                            conn.execute('''
                                INSERT INTO frame_descriptions (
                                    frame_analysis_id, description, auto_tags, model_name
                                ) VALUES (?, ?, ?, ?)
                            ''', (
                                frame_analysis_id,
                                description,
                                json.dumps(auto_tags),
                                'blip-image-captioning-base'
                            ))

                        # Add auto tags to AI tags table
                        for tag in auto_tags:
                            if tag and tag.strip():
                                conn.execute('''
                                    INSERT INTO ai_tags (video_id, tag, source)
                                    VALUES (?, ?, ?)
                                ''', (video_id, tag.strip(), 'blip_auto_tags'))
                                ai_tags.append(tag.strip())

                # Add dominant tags from video summary to AI tags
                dominant_tags = video_summary.get('dominant_tags', [])
                for tag in dominant_tags[:20]:  # Limit to top 20 dominant tags
                    if tag and tag.strip():
                        conn.execute('''
                            INSERT INTO ai_tags (video_id, tag, source)
                            VALUES (?, ?, ?)
                        ''', (video_id, tag.strip(), 'video_summary'))
                        ai_tags.append(tag.strip())

            # Fallback: Process legacy AI analysis if enhanced version doesn't exist
            elif ai_analysis_file.exists():
                logger.info(f"Processing legacy AI analysis for video {video_id}")
                with open(ai_analysis_file, 'r') as f:
                    ai_data = json.load(f)

                # Extract AI-generated tags from analysis
                # Handle both old format (auto_tags) and new format (tags with scores)
                if 'auto_tags' in ai_data:
                    for tag in ai_data['auto_tags']:
                        if tag and tag.strip():
                            conn.execute('''
                                INSERT INTO ai_tags (video_id, tag, source)
                                VALUES (?, ?, ?)
                            ''', (video_id, tag.strip(), 'auto_tags'))
                            ai_tags.append(tag.strip())

                # Process per-image tags with confidence scores
                if 'tags' in ai_data and ai_data['tags']:
                    # Collect all unique tags across all images
                    all_image_tags = {}
                    for image_name, image_tags in ai_data['tags'].items():
                        for tag, confidence in image_tags.items():
                            if tag in all_image_tags:
                                # Keep highest confidence score
                                all_image_tags[tag] = max(all_image_tags[tag], confidence)
                            else:
                                all_image_tags[tag] = confidence

                    # Store tags with their confidence scores
                    for tag, confidence in all_image_tags.items():
                        if tag and tag.strip():
                            conn.execute('''
                                INSERT INTO ai_tags (video_id, tag, confidence, source)
                                VALUES (?, ?, ?, ?)
                            ''', (video_id, tag.strip(), confidence, 'clip_analysis'))
                            ai_tags.append(tag.strip())

            # Process per-frame embeddings if they exist
            if ai_embeddings_file.exists():
                with open(ai_embeddings_file, 'r') as f:
                    embeddings_data = json.load(f)

                # Store frame analysis and embeddings
                # embeddings_data is a list directly
                embeddings_list = embeddings_data if isinstance(embeddings_data, list) else embeddings_data.get('embeddings', [])
                if embeddings_list:
                    for i, frame_data in enumerate(embeddings_list):
                        # Assume success if not specified (older format)
                        if frame_data.get('success', True):
                            # Extract frame information
                            frame_path = frame_data.get('image_path', '')
                            embedding = frame_data.get('embedding', [])
                            image_size = frame_data.get('image_size', [0, 0])
                            model_name = frame_data.get('model_name', 'ViT-B-32')

                            # Store frame analysis record
                            cursor = conn.execute('''
                                INSERT INTO frame_analysis (
                                    video_id, frame_path, frame_number, analysis_success,
                                    model_name, image_width, image_height
                                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                            ''', (
                                video_id, frame_path, i + 1, True,
                                model_name, image_size[0] if len(image_size) > 0 else 0,
                                image_size[1] if len(image_size) > 1 else 0
                            ))

                            frame_analysis_id = cursor.lastrowid

                            # Store embedding vector as JSON
                            if embedding:
                                conn.execute('''
                                    INSERT INTO frame_embeddings (
                                        frame_analysis_id, embedding_vector, embedding_dim
                                    ) VALUES (?, ?, ?)
                                ''', (
                                    frame_analysis_id,
                                    json.dumps(embedding),
                                    len(embedding)
                                ))

            logger.info(f"Processed AI analysis for video {video_id}: {len(ai_tags)} AI tags, stored frame embeddings")

        except Exception as e:
            logger.error(f"Error processing AI analysis for video {video_id}: {e}")

        return ai_tags

    def search_videos(self, query: str, filters: Optional[Dict] = None, limit: int = 50) -> List[Dict]:
        """
        Search videos using full-text search and filters.
        
        Args:
            query: Search query string
            filters: Optional filters (platform, domain, date_range, etc.)
            limit: Maximum number of results
            
        Returns:
            List of matching video records
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            if query.strip():
                # Full-text search (now includes AI tags, summaries, and descriptions)
                sql = '''
                    SELECT v.*, GROUP_CONCAT(DISTINCT t.tag) as tags,
                           GROUP_CONCAT(DISTINCT c.category) as categories,
                           GROUP_CONCAT(DISTINCT at.tag) as ai_tags,
                           vs.summary as video_summary,
                           vs.key_themes as video_key_themes,
                           vs.dominant_tags as video_dominant_tags,
                           vs.description_coverage as video_description_coverage
                    FROM videos_fts fts
                    JOIN videos v ON v.id = fts.rowid
                    LEFT JOIN tags t ON v.id = t.video_id
                    LEFT JOIN categories c ON v.id = c.video_id
                    LEFT JOIN ai_tags at ON v.id = at.video_id
                    LEFT JOIN video_summaries vs ON v.id = vs.video_id
                    WHERE videos_fts MATCH ?
                '''
                params = [query]
            else:
                # No search query, just apply filters
                sql = '''
                    SELECT v.*, GROUP_CONCAT(DISTINCT t.tag) as tags,
                           GROUP_CONCAT(DISTINCT c.category) as categories,
                           GROUP_CONCAT(DISTINCT at.tag) as ai_tags,
                           vs.summary as video_summary,
                           vs.key_themes as video_key_themes,
                           vs.dominant_tags as video_dominant_tags,
                           vs.description_coverage as video_description_coverage
                    FROM videos v
                    LEFT JOIN tags t ON v.id = t.video_id
                    LEFT JOIN categories c ON v.id = c.video_id
                    LEFT JOIN ai_tags at ON v.id = at.video_id
                    LEFT JOIN video_summaries vs ON v.id = vs.video_id
                    WHERE 1=1
                '''
                params = []
            
            # Apply filters
            if filters:
                if filters.get('platform'):
                    sql += ' AND v.platform = ?'
                    params.append(filters['platform'])
                
                if filters.get('domain'):
                    sql += ' AND v.domain = ?'
                    params.append(filters['domain'])
                
                if filters.get('min_duration'):
                    sql += ' AND v.duration_seconds >= ?'
                    params.append(filters['min_duration'])
                
                if filters.get('max_duration'):
                    sql += ' AND v.duration_seconds <= ?'
                    params.append(filters['max_duration'])
                
                if filters.get('date_from'):
                    sql += ' AND v.upload_date >= ?'
                    params.append(filters['date_from'])
                
                if filters.get('date_to'):
                    sql += ' AND v.upload_date <= ?'
                    params.append(filters['date_to'])
            
            sql += ' GROUP BY v.id ORDER BY v.created_at DESC LIMIT ?'
            params.append(limit)
            
            cursor = conn.execute(sql, params)
            results = []
            
            for row in cursor.fetchall():
                result = dict(row)
                # Convert comma-separated tags/categories/ai_tags back to lists
                result['tags'] = [t.strip() for t in (result['tags'] or '').split(',') if t.strip()]
                result['categories'] = [c.strip() for c in (result['categories'] or '').split(',') if c.strip()]
                result['ai_tags'] = [t.strip() for t in (result.get('ai_tags') or '').split(',') if t.strip()]

                # Parse JSON fields from video summary
                if result.get('video_key_themes'):
                    try:
                        result['video_key_themes'] = json.loads(result['video_key_themes'])
                    except (json.JSONDecodeError, TypeError):
                        result['video_key_themes'] = []
                else:
                    result['video_key_themes'] = []

                if result.get('video_dominant_tags'):
                    try:
                        result['video_dominant_tags'] = json.loads(result['video_dominant_tags'])
                    except (json.JSONDecodeError, TypeError):
                        result['video_dominant_tags'] = []
                else:
                    result['video_dominant_tags'] = []

                results.append(result)
            
            return results

    def search_videos_by_embedding(self, query_embedding: List[float], limit: int = 20) -> List[Dict]:
        """
        Search videos using AI embedding similarity.

        Args:
            query_embedding: CLIP embedding vector for the search query
            limit: Maximum number of results

        Returns:
            List of matching video records with similarity scores
        """
        try:
            import numpy as np
            from sklearn.metrics.pairwise import cosine_similarity
        except ImportError:
            logger.error("numpy and scikit-learn are required for AI embedding search. Please install: pip install numpy scikit-learn")
            return []

        results = []

        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row

            # Get all videos with their frame embeddings
            cursor = conn.execute('''
                SELECT v.*, fa.frame_path, fe.embedding_vector,
                       GROUP_CONCAT(DISTINCT t.tag) as tags,
                       GROUP_CONCAT(DISTINCT c.category) as categories,
                       GROUP_CONCAT(DISTINCT at.tag) as ai_tags
                FROM videos v
                JOIN frame_analysis fa ON v.id = fa.video_id
                JOIN frame_embeddings fe ON fa.id = fe.frame_analysis_id
                LEFT JOIN tags t ON v.id = t.video_id
                LEFT JOIN categories c ON v.id = c.video_id
                LEFT JOIN ai_tags at ON v.id = at.video_id
                WHERE fa.analysis_success = 1
                GROUP BY v.id, fa.id
            ''')

            video_similarities = {}
            query_embedding_np = np.array(query_embedding).reshape(1, -1)

            for row in cursor.fetchall():
                video_id = row['id']
                try:
                    # Parse embedding from JSON
                    frame_embedding = json.loads(row['embedding_vector'])
                    frame_embedding_np = np.array(frame_embedding).reshape(1, -1)

                    # Calculate cosine similarity
                    similarity = cosine_similarity(query_embedding_np, frame_embedding_np)[0][0]

                    # Keep track of best similarity for each video
                    if video_id not in video_similarities or similarity > video_similarities[video_id]['similarity']:
                        result = dict(row)
                        result['tags'] = [t.strip() for t in (result['tags'] or '').split(',') if t.strip()]
                        result['categories'] = [c.strip() for c in (result['categories'] or '').split(',') if c.strip()]
                        result['ai_tags'] = [t.strip() for t in (result.get('ai_tags') or '').split(',') if t.strip()]
                        result['similarity'] = similarity
                        video_similarities[video_id] = result

                except (json.JSONDecodeError, ValueError) as e:
                    logger.warning(f"Error parsing embedding for video {video_id}: {e}")
                    continue

            # Sort by similarity and return top results
            results = sorted(video_similarities.values(), key=lambda x: x['similarity'], reverse=True)[:limit]

        return results

    def get_video_by_url(self, url: str) -> Optional[Dict]:
        """Get video record by URL."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('SELECT * FROM videos WHERE url = ?', (url,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_video_by_id(self, video_id: int) -> Optional[Dict]:
        """Get video record by database ID."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT v.*, GROUP_CONCAT(DISTINCT t.tag) as tags,
                       GROUP_CONCAT(DISTINCT c.category) as categories
                FROM videos v
                LEFT JOIN tags t ON v.id = t.video_id
                LEFT JOIN categories c ON v.id = c.video_id
                WHERE v.id = ?
                GROUP BY v.id
            ''', (video_id,))
            
            row = cursor.fetchone()
            if row:
                result = dict(row)
                result['tags'] = [t.strip() for t in (result['tags'] or '').split(',') if t.strip()]
                result['categories'] = [c.strip() for c in (result['categories'] or '').split(',') if c.strip()]
                return result
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        with sqlite3.connect(self.db_path) as conn:
            stats = {}
            
            # Basic counts
            cursor = conn.execute('SELECT COUNT(*) FROM videos')
            stats['total_videos'] = cursor.fetchone()[0]
            
            cursor = conn.execute('SELECT COUNT(DISTINCT platform) FROM videos')
            stats['platforms_count'] = cursor.fetchone()[0]
            
            cursor = conn.execute('SELECT COUNT(DISTINCT domain) FROM videos')
            stats['domains_count'] = cursor.fetchone()[0]
            
            # By platform
            cursor = conn.execute('SELECT platform, COUNT(*) FROM videos GROUP BY platform')
            stats['by_platform'] = dict(cursor.fetchall())
            
            # By domain
            cursor = conn.execute('SELECT domain, COUNT(*) FROM videos GROUP BY domain')
            stats['by_domain'] = dict(cursor.fetchall())
            
            # Duration statistics
            cursor = conn.execute('''
                SELECT AVG(duration_seconds), MIN(duration_seconds), MAX(duration_seconds)
                FROM videos WHERE duration_seconds > 0
            ''')
            duration_stats = cursor.fetchone()
            if duration_stats[0]:
                stats['duration'] = {
                    'average_seconds': round(duration_stats[0], 2),
                    'min_seconds': duration_stats[1],
                    'max_seconds': duration_stats[2]
                }
            
            # Screenshot statistics
            cursor = conn.execute('SELECT SUM(screenshot_count) FROM videos')
            stats['total_screenshots'] = cursor.fetchone()[0] or 0
            
            # Most common tags
            cursor = conn.execute('''
                SELECT tag, COUNT(*) as count FROM tags 
                GROUP BY tag ORDER BY count DESC LIMIT 10
            ''')
            stats['top_tags'] = dict(cursor.fetchall())
            
            return stats
    
    def _update_fts_index(self, conn, video_id: int, metadata: Dict, tags: List[str], categories: List[str], ai_tags: List[str] = None):
        """Update full-text search index for a video including AI summaries and descriptions."""
        # Prepare searchable text
        title = metadata.get('title', '')
        description = metadata.get('description', '')
        uploader = metadata.get('uploader', '')
        tags_text = ' '.join(tags) if tags else ''
        categories_text = ' '.join(categories) if categories else ''
        ai_tags_text = ' '.join(ai_tags) if ai_tags else ''

        # Get video summary for FTS
        video_summary_text = ''
        cursor = conn.execute('SELECT summary FROM video_summaries WHERE video_id = ?', (video_id,))
        summary_row = cursor.fetchone()
        if summary_row:
            video_summary_text = summary_row[0] or ''

        # Get frame descriptions for FTS
        frame_descriptions_text = ''
        cursor = conn.execute('''
            SELECT fd.description
            FROM frame_descriptions fd
            JOIN frame_analysis fa ON fd.frame_analysis_id = fa.id
            WHERE fa.video_id = ?
        ''', (video_id,))
        descriptions = [row[0] for row in cursor.fetchall() if row[0]]
        if descriptions:
            frame_descriptions_text = ' | '.join(descriptions)

        # Insert or replace in FTS index (now includes AI summaries and descriptions)
        conn.execute('''
            INSERT OR REPLACE INTO videos_fts (
                rowid, title, description, uploader, tags, categories,
                ai_tags, video_summary, frame_descriptions
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            video_id, title, description, uploader, tags_text, categories_text,
            ai_tags_text, video_summary_text, frame_descriptions_text
        ))
    
    def backup_database(self, backup_path: str) -> bool:
        """Create a backup of the database."""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            logger.info(f"Database backed up to: {backup_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to backup database: {e}")
            return False
    
    def optimize_database(self):
        """Optimize database performance."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('VACUUM')
            conn.execute('ANALYZE')
            conn.execute('INSERT INTO videos_fts(videos_fts) VALUES("optimize")')
            conn.commit()
        logger.info("Database optimized")
