"""
Parallel AI Analyzer for Video Frame Processing

This module provides parallel processing capabilities for AI analysis of video frames,
allowing multiple AI instances to process different frames simultaneously for improved performance.
"""

import os
import time
import logging
import multiprocessing as mp
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
from concurrent.futures import ProcessPoolExecutor, as_completed

# Set up logging
logger = logging.getLogger(__name__)

def analyze_frame_batch(args):
    """
    Worker function to analyze a batch of frames in a separate process.
    
    Args:
        args: Tuple of (frame_paths, worker_id, model_config)
    
    Returns:
        List of analysis results
    """
    frame_paths, worker_id, model_config = args
    
    # Import AI analyzer in worker process (each process gets its own models)
    try:
        from ai_analyzer import CLIPAnalyzer
        
        # Initialize AI analyzer for this worker
        analyzer = CLIPAnalyzer()
        if not analyzer.is_available():
            return {
                'worker_id': worker_id,
                'success': False,
                'error': 'AI analyzer not available',
                'results': []
            }
        
        logger.info(f"Worker {worker_id}: Processing {len(frame_paths)} frames")
        
        results = []
        for i, frame_path in enumerate(frame_paths):
            try:
                # Analyze single frame
                result = analyzer.analyze_image(Path(frame_path))
                
                if result.get('success'):
                    # Add description if BLIP is available
                    if analyzer.captioner.is_available():
                        description = analyzer.captioner.generate_caption(Path(frame_path))
                        result['description'] = description
                        result['has_description'] = True

                        # Extract tags from description
                        auto_tags = analyzer._extract_tags_from_description(description)
                        result['auto_tags'] = auto_tags

                        # Generate enhanced explicit description if enabled
                        if model_config.get('enhanced_explicit', True):
                            enhanced_desc = analyzer._enhance_description_explicit(
                                description,
                                i + 1,  # Frame number (1-based)
                                len(frame_paths)  # Total frames in this batch
                            )
                            result['enhanced_description'] = enhanced_desc
                    
                    results.append(result)
                    
                    # Log progress for long batches
                    if (i + 1) % 10 == 0:
                        logger.info(f"Worker {worker_id}: Processed {i + 1}/{len(frame_paths)} frames")
                
            except Exception as e:
                logger.error(f"Worker {worker_id}: Error processing {frame_path}: {e}")
                results.append({
                    'image_path': str(frame_path),
                    'success': False,
                    'error': str(e)
                })
        
        return {
            'worker_id': worker_id,
            'success': True,
            'results': results,
            'processed_count': len(results)
        }
        
    except Exception as e:
        logger.error(f"Worker {worker_id}: Failed to initialize: {e}")
        return {
            'worker_id': worker_id,
            'success': False,
            'error': str(e),
            'results': []
        }

class ParallelAIAnalyzer:
    """
    Parallel AI analyzer that distributes frame analysis across multiple processes.
    """
    
    def __init__(self, max_workers: Optional[int] = None, frames_per_batch: int = 20):
        """
        Initialize parallel AI analyzer.
        
        Args:
            max_workers: Maximum number of worker processes (auto-detected if None)
            frames_per_batch: Number of frames to process per batch per worker
        """
        self.max_workers = max_workers or self._calculate_optimal_workers()
        self.frames_per_batch = frames_per_batch
        
        logger.info(f"Initialized ParallelAIAnalyzer with {self.max_workers} workers")
    
    def _calculate_optimal_workers(self) -> int:
        """Calculate optimal number of workers based on system resources."""
        try:
            import psutil

            # Get system resources
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            cpu_cores = psutil.cpu_count()

            # Each AI instance needs ~2.2GB RAM
            memory_limited_workers = int(available_memory_gb / 2.5)  # Conservative estimate
            cpu_limited_workers = max(1, cpu_cores - 1)  # Leave one core free

            # Take the minimum, but cap at reasonable limits for diminishing returns
            optimal_workers = min(memory_limited_workers, cpu_limited_workers, 6)

            logger.info(f"System resources: {available_memory_gb:.1f}GB RAM, {cpu_cores} CPU cores")
            logger.info(f"Memory can support: {memory_limited_workers} workers")
            logger.info(f"CPU can support: {cpu_limited_workers} workers")
            logger.info(f"Calculated optimal workers: {optimal_workers}")

            # Ensure we have at least 1 worker, but warn if resources are very limited
            if optimal_workers < 1:
                logger.warning(f"Very limited system resources detected. Using 1 worker.")
                return 1

            return optimal_workers

        except ImportError:
            # Fallback if psutil not available - conservative estimate
            cpu_count = mp.cpu_count()
            fallback_workers = min(2, max(1, cpu_count - 1))
            logger.warning(f"psutil not available, using fallback: {fallback_workers} workers")
            return fallback_workers

    def _detect_adult_content(self, screenshot_dir: Path) -> bool:
        """
        Detect if content is adult-oriented based on directory path and context.

        Args:
            screenshot_dir: Directory containing screenshots

        Returns:
            True if adult content detected, False for regular content
        """
        # Convert path to string for analysis
        path_str = str(screenshot_dir).lower()

        # Adult content site indicators
        adult_sites = [
            'xvideos', 'pornhub', 'xhamster', 'redtube', 'youporn', 'tube8',
            'spankbang', 'eporner', 'txxx', 'hqporner', 'porn', 'xxx',
            'adult', 'sex', 'nsfw', 'erotic'
        ]

        # Check if path contains adult site indicators
        for site in adult_sites:
            if site in path_str:
                return True

        # Regular content site indicators
        regular_sites = [
            'youtube', 'vimeo', 'dailymotion', 'twitch', 'tiktok',
            'instagram', 'facebook', 'twitter', 'news', 'educational'
        ]

        # Check if path contains regular site indicators
        for site in regular_sites:
            if site in path_str:
                return False

        # Default to regular content if uncertain
        return False
    
    def analyze_screenshots_parallel(self, screenshot_dir: Path, enhanced_explicit: bool = None) -> Dict[str, Any]:
        """
        Analyze screenshots in parallel using multiple AI instances.
        Now includes content-aware analysis that automatically detects adult vs regular content.

        Args:
            screenshot_dir: Directory containing screenshots
            enhanced_explicit: Whether to generate enhanced explicit descriptions (None = auto-detect)

        Returns:
            Combined analysis results from all workers
        """
        # Get all screenshot files
        screenshot_files = sorted(list(screenshot_dir.glob('frame_*.jpg')))
        
        if not screenshot_files:
            return {
                'success': False,
                'error': 'No screenshot files found',
                'results': []
            }
        
        # Auto-detect content type if not specified
        if enhanced_explicit is None:
            enhanced_explicit = self._detect_adult_content(screenshot_dir)
            content_type = "adult content" if enhanced_explicit else "regular content"
            logger.info(f"Auto-detected {content_type} based on directory path")

        logger.info(f"Starting parallel analysis of {len(screenshot_files)} screenshots")
        logger.info(f"Using {self.max_workers} workers with {self.frames_per_batch} frames per batch")
        if enhanced_explicit:
            logger.info("Using enhanced explicit analysis for adult content")
        else:
            logger.info("Using standard analysis for regular content")
        
        # Split frames into batches for workers
        frame_batches = self._create_frame_batches(screenshot_files)
        
        # Prepare worker arguments
        worker_args = []
        for worker_id, batch in enumerate(frame_batches):
            worker_args.append((
                [str(f) for f in batch],  # Convert paths to strings for serialization
                worker_id,
                {'enhanced_explicit': enhanced_explicit}  # Model config with enhanced analysis flag
            ))
        
        # Process batches in parallel
        start_time = time.time()
        all_results = []
        
        try:
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all batches
                future_to_worker = {
                    executor.submit(analyze_frame_batch, args): args[1] 
                    for args in worker_args
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_worker):
                    worker_id = future_to_worker[future]
                    
                    try:
                        worker_result = future.result()
                        
                        if worker_result['success']:
                            all_results.extend(worker_result['results'])
                            logger.info(f"Worker {worker_id}: Completed {worker_result['processed_count']} frames")
                        else:
                            logger.error(f"Worker {worker_id}: Failed - {worker_result['error']}")
                            
                    except Exception as e:
                        logger.error(f"Worker {worker_id}: Exception - {e}")
        
        except Exception as e:
            logger.error(f"Parallel processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': []
            }
        
        processing_time = time.time() - start_time
        
        # Generate video summary from all results
        video_summary = self._generate_video_summary(all_results)
        
        # Prepare final result
        result = {
            'success': True,
            'results': all_results,
            'embeddings_count': len([r for r in all_results if r.get('success')]),
            'processing_time': processing_time,
            'workers_used': self.max_workers,
            'frames_per_second': len(screenshot_files) / processing_time if processing_time > 0 else 0,
            'video_summary': video_summary
        }
        
        logger.info(f"Parallel analysis complete: {len(all_results)} frames in {processing_time:.2f}s")
        logger.info(f"Processing rate: {result['frames_per_second']:.2f} frames/second")
        
        return result
    
    def _create_frame_batches(self, screenshot_files: List[Path]) -> List[List[Path]]:
        """Split screenshot files into batches for parallel processing."""
        batches = []
        
        # Distribute frames evenly across workers
        frames_per_worker = len(screenshot_files) // self.max_workers
        remainder = len(screenshot_files) % self.max_workers
        
        start_idx = 0
        for worker_id in range(self.max_workers):
            # Add one extra frame to first 'remainder' workers
            batch_size = frames_per_worker + (1 if worker_id < remainder else 0)
            
            if batch_size > 0:
                end_idx = start_idx + batch_size
                batch = screenshot_files[start_idx:end_idx]
                batches.append(batch)
                start_idx = end_idx
        
        return batches
    
    def _generate_video_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate video-level summary from all frame results."""
        if not results:
            return {}
        
        # Collect descriptions and tags
        descriptions = []
        all_tags = []
        successful_frames = 0
        
        for result in results:
            if result.get('success'):
                successful_frames += 1
                if result.get('description'):
                    descriptions.append(result['description'])
                if result.get('auto_tags'):
                    all_tags.extend(result['auto_tags'])
        
        # Generate summary
        if descriptions:
            # Sample descriptions for summary
            sample_size = min(5, len(descriptions))
            step = max(1, len(descriptions) // sample_size)
            sample_descriptions = descriptions[::step][:sample_size]
            summary = " | ".join(sample_descriptions)
        else:
            summary = "Video analysis completed"
        
        # Count tag frequencies
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # Get dominant tags
        dominant_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:15]
        dominant_tags = [tag for tag, count in dominant_tags]
        
        return {
            'summary': summary,
            'dominant_tags': dominant_tags,
            'frame_count': len(results),
            'successful_frames': successful_frames,
            'description_coverage': f"{len(descriptions)}/{successful_frames}" if successful_frames > 0 else "0/0"
        }
