"""
File Organizer Component for Video Search Indexer

This module creates and manages the folder structure for organizing video content
by domain and video name, handling file naming conventions and storage.
"""

import os
import re
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, Optional, List, Any
from urllib.parse import urlparse
import requests
from PIL import Image

# Set up logging for this module
logger = logging.getLogger(__name__)


class FileOrganizer:
    """
    Manages file organization and storage for indexed video content.
    
    Features:
    - Creates domain-based folder structure (domain/index/video_name/)
    - Handles file naming conventions and sanitization
    - Downloads and stores thumbnails
    - Saves metadata to text files
    - Creates screenshot directories
    - Manages storage cleanup and organization
    """
    
    def __init__(self, base_data_dir: str = "data", config: Optional[Dict] = None):
        """
        Initialize the file organizer.
        
        Args:
            base_data_dir: Base directory for storing all indexed content
            config: Configuration dictionary with file organization settings
        """
        self.base_data_dir = Path(base_data_dir)
        self.config = config or {}
        
        # Create base data directory if it doesn't exist
        self.base_data_dir.mkdir(parents=True, exist_ok=True)
        
        # File naming configuration
        self.max_filename_length = self.config.get('max_filename_length', 100)
        self.replace_spaces_with = self.config.get('replace_spaces_with', '_')
        self.remove_special_chars = self.config.get('remove_special_chars', True)
        
        # Thumbnail configuration
        self.thumbnail_size = tuple(self.config.get('thumbnail_size', [320, 240]))
        
        # Request headers for downloading files
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
        }
    
    def create_video_directory(self, domain: str, video_title: str, video_id: str = None) -> Dict[str, Path]:
        """
        Create directory structure for a video.
        
        Args:
            domain: Domain name (e.g., 'youtube', 'vimeo')
            video_title: Title of the video
            video_id: Optional video ID for uniqueness
            
        Returns:
            Dictionary with paths to created directories
        """
        # Sanitize domain name
        clean_domain = self._sanitize_filename(domain)
        
        # Sanitize video title for folder name
        clean_title = self._sanitize_filename(video_title)
        
        # Add video ID to ensure uniqueness if provided
        if video_id:
            clean_id = self._sanitize_filename(video_id)
            folder_name = f"{clean_title}_{clean_id}"
        else:
            folder_name = clean_title
        
        # Ensure folder name isn't too long
        if len(folder_name) > self.max_filename_length:
            folder_name = folder_name[:self.max_filename_length]
        
        # Create directory structure: data/domain/index/video_name/
        domain_dir = self.base_data_dir / clean_domain
        index_dir = domain_dir / "index"
        video_dir = index_dir / folder_name
        screenshots_dir = video_dir / "screenshots"
        
        # Create all directories
        try:
            domain_dir.mkdir(exist_ok=True)
            index_dir.mkdir(exist_ok=True)
            video_dir.mkdir(exist_ok=True)
            screenshots_dir.mkdir(exist_ok=True)
            
            logger.info(f"Created directory structure: {video_dir}")
            
            return {
                'domain_dir': domain_dir,
                'index_dir': index_dir,
                'video_dir': video_dir,
                'screenshots_dir': screenshots_dir,
                'folder_name': folder_name
            }
            
        except OSError as e:
            logger.error(f"Failed to create directory structure: {e}")
            raise
    
    def save_metadata(self, video_dir: Path, metadata: Dict[str, Any]) -> Path:
        """
        Save video metadata to a text file.
        
        Args:
            video_dir: Directory where the video content is stored
            metadata: Metadata dictionary to save
            
        Returns:
            Path to the saved metadata file
        """
        metadata_file = video_dir / "metadata.txt"
        
        try:
            # Format metadata for human readability
            formatted_metadata = self._format_metadata_for_file(metadata)
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                f.write(formatted_metadata)
            
            logger.info(f"Saved metadata to: {metadata_file}")
            return metadata_file
            
        except IOError as e:
            logger.error(f"Failed to save metadata: {e}")
            raise
    
    def save_metadata_json(self, video_dir: Path, metadata: Dict[str, Any]) -> Path:
        """
        Save video metadata as JSON file for programmatic access.
        
        Args:
            video_dir: Directory where the video content is stored
            metadata: Metadata dictionary to save
            
        Returns:
            Path to the saved JSON metadata file
        """
        metadata_json_file = video_dir / "metadata.json"
        
        try:
            with open(metadata_json_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Saved JSON metadata to: {metadata_json_file}")
            return metadata_json_file
            
        except IOError as e:
            logger.error(f"Failed to save JSON metadata: {e}")
            raise
    
    def download_thumbnail(self, video_dir: Path, thumbnail_url: str) -> Optional[Path]:
        """
        Download and save video thumbnail.
        
        Args:
            video_dir: Directory where the video content is stored
            thumbnail_url: URL of the thumbnail to download
            
        Returns:
            Path to the saved thumbnail file, or None if failed
        """
        if not thumbnail_url:
            logger.warning("No thumbnail URL provided")
            return None
        
        thumbnail_file = video_dir / "thumbnail.jpg"
        
        try:
            # Download thumbnail
            response = requests.get(thumbnail_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # Save original thumbnail
            with open(thumbnail_file, 'wb') as f:
                f.write(response.content)
            
            # Resize thumbnail if needed
            self._resize_thumbnail(thumbnail_file)
            
            logger.info(f"Downloaded thumbnail to: {thumbnail_file}")
            return thumbnail_file
            
        except Exception as e:
            logger.error(f"Failed to download thumbnail from {thumbnail_url}: {e}")
            return None
    
    def organize_screenshots(self, video_dir: Path, screenshot_files: List[Path]) -> List[Path]:
        """
        Organize screenshot files in the screenshots directory.
        
        Args:
            video_dir: Directory where the video content is stored
            screenshot_files: List of screenshot file paths to organize
            
        Returns:
            List of organized screenshot file paths
        """
        screenshots_dir = video_dir / "screenshots"
        screenshots_dir.mkdir(exist_ok=True)
        
        organized_files = []
        
        for i, screenshot_file in enumerate(screenshot_files):
            if not screenshot_file.exists():
                continue
            
            # Create organized filename: frame_XXXXX.jpg
            organized_filename = f"frame_{i+1:05d}.jpg"
            organized_path = screenshots_dir / organized_filename
            
            try:
                # Move or copy the screenshot to organized location
                if screenshot_file.parent != screenshots_dir:
                    shutil.move(str(screenshot_file), str(organized_path))
                else:
                    # Already in the right directory, just rename if needed
                    if screenshot_file.name != organized_filename:
                        screenshot_file.rename(organized_path)
                
                organized_files.append(organized_path)
                
            except Exception as e:
                logger.error(f"Failed to organize screenshot {screenshot_file}: {e}")
        
        logger.info(f"Organized {len(organized_files)} screenshots in: {screenshots_dir}")
        return organized_files
    
    def get_video_directory_info(self, domain: str, video_title: str, video_id: str = None) -> Dict:
        """
        Get information about a video's directory structure without creating it.
        
        Args:
            domain: Domain name
            video_title: Video title
            video_id: Optional video ID
            
        Returns:
            Dictionary with directory information
        """
        clean_domain = self._sanitize_filename(domain)
        clean_title = self._sanitize_filename(video_title)
        
        if video_id:
            clean_id = self._sanitize_filename(video_id)
            folder_name = f"{clean_title}_{clean_id}"
        else:
            folder_name = clean_title
        
        if len(folder_name) > self.max_filename_length:
            folder_name = folder_name[:self.max_filename_length]
        
        video_dir = self.base_data_dir / clean_domain / "index" / folder_name
        
        return {
            'video_dir': video_dir,
            'exists': video_dir.exists(),
            'folder_name': folder_name,
            'domain': clean_domain
        }
    
    def cleanup_empty_directories(self) -> int:
        """
        Remove empty directories in the data structure.
        
        Returns:
            Number of directories removed
        """
        removed_count = 0
        
        try:
            # Walk through all directories and remove empty ones
            for root, dirs, files in os.walk(self.base_data_dir, topdown=False):
                for dir_name in dirs:
                    dir_path = Path(root) / dir_name
                    try:
                        if not any(dir_path.iterdir()):  # Directory is empty
                            dir_path.rmdir()
                            removed_count += 1
                            logger.info(f"Removed empty directory: {dir_path}")
                    except OSError:
                        # Directory not empty or other error
                        pass
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        
        return removed_count
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename by removing or replacing problematic characters.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename safe for filesystem use
        """
        if not filename:
            return "unknown"
        
        # Replace spaces
        sanitized = filename.replace(' ', self.replace_spaces_with)
        
        # Remove or replace special characters if configured
        if self.remove_special_chars:
            # Keep only alphanumeric, hyphens, underscores, and dots
            sanitized = re.sub(r'[^\w\-_.]', '', sanitized)
        
        # Remove multiple consecutive separators
        sanitized = re.sub(f'[{re.escape(self.replace_spaces_with)}]+', self.replace_spaces_with, sanitized)
        
        # Remove leading/trailing separators
        sanitized = sanitized.strip(self.replace_spaces_with + '-_.')
        
        # Ensure it's not empty
        if not sanitized:
            sanitized = "unknown"
        
        return sanitized
    
    def _format_metadata_for_file(self, metadata: Dict[str, Any]) -> str:
        """Format metadata dictionary for human-readable text file."""
        lines = ["VIDEO METADATA", "=" * 50, ""]
        
        # Essential information first
        essential_fields = [
            ('Title', 'title'),
            ('Platform', 'platform'),
            ('Uploader', 'uploader'),
            ('Duration', 'duration_formatted'),
            ('Upload Date', 'upload_date'),
            ('View Count', 'view_count'),
            ('Video ID', 'video_id'),
            ('Original URL', 'original_url'),
        ]
        
        for label, key in essential_fields:
            value = metadata.get(key, 'N/A')
            if value and value != 'N/A':
                lines.append(f"{label}: {value}")
        
        # Description
        description = metadata.get('description', '')
        if description:
            lines.extend(["", "Description:", "-" * 20, description])
        
        # Tags
        tags = metadata.get('tags', [])
        if tags:
            lines.extend(["", "Tags:", "-" * 20, ", ".join(tags)])
        
        # Technical information
        tech_info = []
        if metadata.get('width') and metadata.get('height'):
            tech_info.append(f"Resolution: {metadata['width']}x{metadata['height']}")
        if metadata.get('fps'):
            tech_info.append(f"FPS: {metadata['fps']}")
        if metadata.get('filesize'):
            tech_info.append(f"File Size: {metadata['filesize']} bytes")
        
        if tech_info:
            lines.extend(["", "Technical Information:", "-" * 20] + tech_info)
        
        # Extraction information
        lines.extend([
            "", "Extraction Information:", "-" * 20,
            f"Extraction Date: {metadata.get('extraction_date', 'N/A')}",
            f"Extraction Method: {metadata.get('extraction_method', 'N/A')}",
            f"Success: {metadata.get('success', 'N/A')}"
        ])
        
        if metadata.get('error'):
            lines.append(f"Error: {metadata['error']}")
        
        return "\n".join(lines)
    
    def _resize_thumbnail(self, thumbnail_path: Path) -> None:
        """Resize thumbnail to configured size."""
        try:
            with Image.open(thumbnail_path) as img:
                # Only resize if image is larger than target size
                if img.size[0] > self.thumbnail_size[0] or img.size[1] > self.thumbnail_size[1]:
                    img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                    img.save(thumbnail_path, "JPEG", quality=85, optimize=True)
                    logger.debug(f"Resized thumbnail to {img.size}")
        except Exception as e:
            logger.warning(f"Failed to resize thumbnail: {e}")
