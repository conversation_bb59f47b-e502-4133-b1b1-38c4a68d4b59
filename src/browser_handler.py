"""
Browser Automation Handler for Complex Video Sites

Handles cases where yt-dlp fails due to:
- 18+ age verification modals
- Pre-roll ads that need to be skipped
- Anti-adblock detection
- Complex JavaScript-based video players
- Login-required content
"""

import logging
import asyncio
import time
from typing import Dict, Optional, List, Any
from pathlib import Path
import json

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, TimeoutError as PlaywrightTimeoutError
    PLAYWRIGHT_AVAILABLE = True
    # Import Page type for annotations
    from playwright.async_api import Page
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    # Create dummy Page class for type annotations when Playwright not available
    class Page:
        pass

logger = logging.getLogger(__name__)

class BrowserVideoHandler:
    """
    Handles complex video sites using browser automation.
    
    Features:
    - Automatic 18+ age verification modal handling
    - Pre-roll ad detection and skipping
    - Anti-adblock bypass techniques
    - Cookie persistence for login states
    - Site-specific handling patterns
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize browser handler with configuration."""
        self.config = config or {}
        self.browser = None
        self.context = None
        
        # Cookie storage path
        self.cookie_path = Path('data/browser_cookies.json')
        self.cookie_path.parent.mkdir(exist_ok=True)
        
        # Site-specific handling patterns
        self.site_patterns = {
            # Common adult video sites
            'pornhub.com': {
                'age_modal_selectors': [
                    '[data-age-verification="true"] button',
                    '.age-verification button',
                    'button[data-action="age-verification-accept"]',
                    '.ageVerification button'
                ],
                'ad_skip_selectors': [
                    '.ytp-ad-skip-button',
                    '.skip-ad',
                    '[data-testid="skip-ad"]',
                    '.ad-skip-button'
                ],
                'video_selectors': [
                    'video',
                    '.video-element',
                    '#player video'
                ]
            },
            'xvideos.com': {
                'age_modal_selectors': [
                    '.age-verification button',
                    '[data-age-check] button'
                ],
                'ad_skip_selectors': [
                    '.skip-ad',
                    '.ad-close'
                ],
                'video_selectors': ['video', '#html5video']
            },
            'redtube.com': {
                'age_modal_selectors': [
                    '.age-verification-modal button',
                    '[data-age-verification] button'
                ],
                'ad_skip_selectors': ['.skip-ad', '.close-ad'],
                'video_selectors': ['video']
            },
            # Generic patterns for other sites
            'generic': {
                'age_modal_selectors': [
                    'button:has-text("I am 18")',
                    'button:has-text("Yes")',
                    'button:has-text("Enter")',
                    'button:has-text("Continue")',
                    '.age-verification button',
                    '.modal button:has-text("18")',
                    '[data-age] button',
                    '.confirm-age'
                ],
                'ad_skip_selectors': [
                    'button:has-text("Skip")',
                    'button:has-text("Skip Ad")',
                    '.skip-ad',
                    '.ad-skip',
                    '.ytp-ad-skip-button',
                    '[data-testid="skip-ad"]',
                    '.close-ad',
                    '.ad-close'
                ],
                'video_selectors': [
                    'video',
                    '.video-player video',
                    '#player video',
                    '.player video'
                ]
            }
        }
    
    async def extract_video_info(self, url: str) -> Dict[str, Any]:
        """
        Extract video information using browser automation.
        
        Args:
            url: Video URL to process
            
        Returns:
            Dictionary with extracted video information
        """
        if not PLAYWRIGHT_AVAILABLE:
            return {
                'success': False,
                'error': 'Playwright not available. Install with: pip install playwright'
            }
        
        try:
            async with async_playwright() as p:
                # Launch browser with stealth settings
                browser = await p.chromium.launch(
                    headless=True,  # Set to False for debugging
                    args=[
                        '--no-sandbox',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-dev-shm-usage',
                        '--disable-extensions',
                        '--disable-plugins',
                        '--disable-images',  # Faster loading
                        '--mute-audio',      # No audio needed
                    ]
                )
                
                # Create context with realistic settings
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    java_script_enabled=True,
                    accept_downloads=False,
                    ignore_https_errors=True,
                    extra_http_headers={
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'DNT': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Upgrade-Insecure-Requests': '1'
                    }
                )
                
                # Load saved cookies if available
                await self._load_cookies(context)
                
                page = await context.new_page()
                
                # Navigate to the URL
                logger.info(f"Navigating to: {url}")
                await page.goto(url, wait_until='domcontentloaded', timeout=30000)
                
                # Handle age verification modals
                await self._handle_age_verification(page, url)
                
                # Wait for page to load and handle ads
                await self._handle_ads(page, url)
                
                # Extract video information
                video_info = await self._extract_video_metadata(page, url)
                
                # Save cookies for future use
                await self._save_cookies(context)
                
                await browser.close()
                
                return {
                    'success': True,
                    'video_info': video_info,
                    'method': 'browser_automation'
                }
                
        except Exception as e:
            logger.error(f"Browser automation failed for {url}: {e}")
            return {
                'success': False,
                'error': str(e),
                'method': 'browser_automation'
            }
    
    async def _handle_age_verification(self, page: Page, url: str) -> None:
        """Handle age verification modals."""
        domain = self._get_domain(url)
        patterns = self.site_patterns.get(domain, self.site_patterns['generic'])
        
        logger.info(f"Checking for age verification modals on {domain}")
        
        # Wait a moment for modals to appear
        await page.wait_for_timeout(2000)
        
        for selector in patterns['age_modal_selectors']:
            try:
                # Check if modal exists and is visible
                element = await page.query_selector(selector)
                if element and await element.is_visible():
                    logger.info(f"Found age verification modal, clicking: {selector}")
                    await element.click()
                    await page.wait_for_timeout(1000)  # Wait for modal to close
                    break
            except Exception as e:
                logger.debug(f"Age verification selector failed: {selector} - {e}")
                continue
    
    async def _handle_ads(self, page: Page, url: str) -> None:
        """Handle pre-roll ads and skip buttons."""
        domain = self._get_domain(url)
        patterns = self.site_patterns.get(domain, self.site_patterns['generic'])
        
        logger.info(f"Handling ads on {domain}")
        
        # Wait for video player to load
        await page.wait_for_timeout(3000)
        
        # Look for ads and skip buttons for up to 30 seconds
        max_wait_time = 30
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # Check for skip buttons
            for selector in patterns['ad_skip_selectors']:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible():
                        logger.info(f"Found ad skip button, clicking: {selector}")
                        await element.click()
                        await page.wait_for_timeout(1000)
                        return  # Ad skipped, exit
                except Exception as e:
                    logger.debug(f"Ad skip selector failed: {selector} - {e}")
                    continue
            
            # Wait a bit before checking again
            await page.wait_for_timeout(2000)
        
        logger.info("Finished waiting for ads")
    
    async def _extract_video_metadata(self, page: Page, url: str) -> Dict[str, Any]:
        """Extract video metadata from the page."""
        domain = self._get_domain(url)
        patterns = self.site_patterns.get(domain, self.site_patterns['generic'])
        
        metadata = {
            'url': url,
            'domain': domain,
            'title': None,
            'description': None,
            'duration': None,
            'thumbnail_url': None,
            'video_url': None
        }
        
        try:
            # Extract title
            title_selectors = ['title', 'h1', '.video-title', '.title', '[data-title]']
            for selector in title_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        title = await element.text_content()
                        if title and title.strip():
                            metadata['title'] = title.strip()
                            break
                except:
                    continue
            
            # Extract video URL from video element
            for selector in patterns['video_selectors']:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        video_src = await element.get_attribute('src')
                        if video_src:
                            metadata['video_url'] = video_src
                            break
                except:
                    continue
            
            # Extract thumbnail
            thumbnail_selectors = ['video[poster]', '.video-thumbnail img', '.poster img']
            for selector in thumbnail_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        if 'video' in selector:
                            thumbnail = await element.get_attribute('poster')
                        else:
                            thumbnail = await element.get_attribute('src')
                        if thumbnail:
                            metadata['thumbnail_url'] = thumbnail
                            break
                except:
                    continue
            
            logger.info(f"Extracted metadata: title='{metadata['title']}', video_url='{metadata['video_url']}'")
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {e}")
        
        return metadata
    
    async def _load_cookies(self, context) -> None:
        """Load saved cookies."""
        try:
            if self.cookie_path.exists():
                with open(self.cookie_path, 'r') as f:
                    cookies = json.load(f)
                await context.add_cookies(cookies)
                logger.debug(f"Loaded {len(cookies)} cookies")
        except Exception as e:
            logger.debug(f"Could not load cookies: {e}")
    
    async def _save_cookies(self, context) -> None:
        """Save cookies for future use."""
        try:
            cookies = await context.cookies()
            with open(self.cookie_path, 'w') as f:
                json.dump(cookies, f)
            logger.debug(f"Saved {len(cookies)} cookies")
        except Exception as e:
            logger.debug(f"Could not save cookies: {e}")
    
    def _get_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            from urllib.parse import urlparse
            return urlparse(url).netloc.lower()
        except:
            return 'unknown'
