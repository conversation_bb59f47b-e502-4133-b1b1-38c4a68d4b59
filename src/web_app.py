#!/usr/bin/env python3
"""
Simple Flask Web Interface for Video Search Indexer

Simple interface with just the essential features:
- Add video URLs
- View and manage stored sites
- Search videos with category filters
- Browse categories
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.exceptions import NotFound

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from indexer import VideoIndexer
    from storage import VideoStorage
    from ai_analyzer import CLIPAnalyzer
    from hybrid_search import HybridSearchEngine
except ImportError:
    # Try absolute imports if relative imports fail
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from src.indexer import VideoIndexer
    from src.storage import VideoStorage
    from src.ai_analyzer import CLIPAnalyzer
    from src.hybrid_search import HybridSearchEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleVideoIndexerApp:
    """
    Simple Flask web application for video indexer.

    Essential features only:
    - Add video URLs
    - View/delete stored sites
    - Search videos with category filters
    - Browse categories
    """

    def __init__(self, config_path: str = "config/settings.yaml"):
        """Initialize the web application."""
        self.app = Flask(__name__,
                        template_folder='../templates',
                        static_folder='../static')

        # Configure Flask app
        self.app.secret_key = 'simple-video-indexer-key'

        # Initialize components
        try:
            self.indexer = VideoIndexer(config_path)
            self.storage = VideoStorage()
            self.ai_analyzer = CLIPAnalyzer()

            # Initialize hybrid search engine
            self.hybrid_search = HybridSearchEngine(self.storage, self.ai_analyzer)

            logger.info("Simple web app initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise

        # Register routes
        self._register_routes()
    
    def _register_routes(self):
        """Register simple Flask routes."""

        @self.app.route('/')
        def index():
            """Main page with categories and search."""
            try:
                # Get categories (platforms/domains)
                categories = self._get_categories()

                # Get total video count
                total_videos = len(self.storage.search_videos("", limit=10000))

                # Create stats dictionary for template
                stats = {
                    'total_videos': total_videos,
                    'total_categories': len(categories)
                }

                return render_template('index.html',
                                     categories=categories,
                                     total_videos=total_videos,
                                     stats=stats)
            except Exception as e:
                logger.error(f"Error loading main page: {e}")
                flash(f"Error: {e}", 'error')
                return render_template('index.html', categories=[], total_videos=0, stats={'total_videos': 0, 'total_categories': 0})
        
        @self.app.route('/add-url', methods=['GET', 'POST'])
        def add_url():
            """Add new video URL."""
            if request.method == 'POST':
                try:
                    url = request.form.get('url', '').strip()
                    if not url:
                        flash('URL is required', 'error')
                        return render_template('add_url.html')

                    # Add URL to indexer
                    result = self.indexer.add_url(url)

                    if result['success']:
                        flash(f'Successfully added video from {result["platform"]}', 'success')
                        return redirect(url_for('index'))
                    else:
                        flash(f'Failed to add URL: {result["error"]}', 'error')

                except Exception as e:
                    logger.error(f"Error adding URL: {e}")
                    flash(f'Error adding URL: {e}', 'error')

            return render_template('add_url.html')
        
        @self.app.route('/sites')
        def view_sites():
            """View and manage stored sites."""
            try:
                # Get all videos grouped by domain
                all_videos = self.storage.search_videos("", limit=10000)
                sites = {}

                for video in all_videos:
                    domain = video.get('platform', 'unknown')
                    if domain not in sites:
                        sites[domain] = []
                    sites[domain].append(video)

                return render_template('sites.html', sites=sites)
            except Exception as e:
                logger.error(f"Error loading sites: {e}")
                flash(f"Error loading sites: {e}", 'error')
                return render_template('sites.html', sites={})
        
        @self.app.route('/videos')
        def videos():
            """All videos page with search functionality."""
            try:
                query = request.args.get('q', '').strip()
                category = request.args.get('category', '').strip()

                if query:
                    # Use hybrid search for queries
                    search_result = self.hybrid_search.search(query, limit=100)
                    if search_result['success']:
                        videos = search_result['results']
                        search_metadata = search_result.get('metadata', {})
                    else:
                        videos = []
                        search_metadata = {}
                else:
                    # No query, show all videos or filter by category
                    filters = {'platform': category} if category else None
                    videos = self.storage.search_videos("", filters=filters, limit=100)
                    search_metadata = {}

                # Get categories for filter dropdown
                categories = self._get_categories()

                return render_template('simple_videos.html',
                                     videos=videos,
                                     query=query,
                                     category=category,
                                     categories=categories,
                                     search_metadata=search_metadata)
            except Exception as e:
                logger.error(f"Error loading videos: {e}")
                flash(f"Error loading videos: {e}", 'error')
                return render_template('simple_videos.html', videos=[], query='', category='', categories=[])

        @self.app.route('/search')
        def search():
            """Search page."""
            return render_template('search.html', ai_available=self.ai_analyzer.is_available())
        
        @self.app.route('/api/search', methods=['POST'])
        def api_search():
            """API endpoint for hybrid search (literal + semantic + expanded)."""
            try:
                data = request.get_json()
                query = data.get('query', '').strip()

                if not query:
                    return jsonify({'error': 'Query is required'}), 400

                # Perform hybrid search
                search_result = self.hybrid_search.search(query, limit=50)

                if not search_result['success']:
                    return jsonify({'error': search_result.get('error', 'Search failed')}), 500

                # Format results for web interface
                formatted_results = []
                for video in search_result['results']:
                    # Get a representative screenshot for display
                    screenshots = self._get_video_screenshots(video)
                    screenshot_filename = screenshots[0] if screenshots else 'frame_00001.jpg'

                    formatted_results.append({
                        'video_id': video['id'],
                        'video_title': video['title'],
                        'video_url': video['url'],
                        'screenshot_filename': screenshot_filename,
                        'screenshot_url': url_for('serve_screenshot',
                                                video_id=video['id'],
                                                filename=screenshot_filename),
                        'similarity': video.get('hybrid_score', 0.0),
                        'search_methods': video.get('search_methods', []),
                        'video_detail_url': url_for('video_detail', video_id=video['id']),
                        'ai_tags': video.get('ai_tags', [])
                    })

                return jsonify({
                    'success': True,
                    'query': query,
                    'results': formatted_results,
                    'count': len(formatted_results),
                    'search_metadata': search_result.get('metadata', {})
                })

            except Exception as e:
                logger.error(f"Hybrid search error: {e}")
                return jsonify({'error': str(e)}), 500
        

        @self.app.route('/process', methods=['POST'])
        def process_videos():
            """Process pending videos."""
            try:
                # Process pending URLs
                result = self.indexer.process_pending_urls(max_urls=5)
                
                flash(f'Processed {result["processed"]} videos, {result["failed"]} failed', 
                      'success' if result["failed"] == 0 else 'warning')
                
                return redirect(url_for('index'))
                
            except Exception as e:
                logger.error(f"Error processing videos: {e}")
                flash(f'Error processing videos: {e}', 'error')
                return redirect(url_for('index'))
        
        @self.app.route('/api/stats')
        def api_stats():
            """API endpoint for statistics."""
            try:
                stats = self._get_dashboard_stats()
                return jsonify(stats)
            except Exception as e:
                logger.error(f"Error getting stats: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/thumbnail/<int:video_id>')
        def serve_thumbnail(video_id):
            """Serve video thumbnail."""
            try:
                video = self.storage.get_video_by_id(video_id)
                if not video:
                    raise NotFound()
                
                thumbnail_path = Path(video['video_dir']) / 'thumbnail.jpg'
                if thumbnail_path.exists():
                    return send_file(thumbnail_path)
                else:
                    # Return a default thumbnail
                    raise NotFound()
                    
            except Exception as e:
                logger.error(f"Error serving thumbnail for video {video_id}: {e}")
                raise NotFound()
        
        @self.app.route('/screenshot/<int:video_id>/<filename>')
        def serve_screenshot(video_id, filename):
            """Serve video screenshot."""
            try:
                video = self.storage.get_video_by_id(video_id)
                if not video:
                    raise NotFound()
                
                screenshot_path = Path(video['video_dir']) / 'screenshots' / filename
                if screenshot_path.exists() and screenshot_path.suffix.lower() in ['.jpg', '.jpeg', '.png']:
                    return send_file(screenshot_path)
                else:
                    raise NotFound()
                    
            except Exception as e:
                logger.error(f"Error serving screenshot {filename} for video {video_id}: {e}")
                raise NotFound()
    
    def _get_dashboard_stats(self) -> Dict:
        """Get dashboard statistics."""
        try:
            # Get URL manager stats
            url_stats = self.indexer.url_manager.get_statistics()
            
            # Get recent activity (videos processed in last 24 hours)
            recent_videos = self.storage.search_videos("", limit=100)
            recent_count = len([v for v in recent_videos 
                              if self._is_recent(v.get('extraction_date', ''))])
            
            return {
                'total_videos': url_stats['total_urls'],
                'pending_videos': url_stats['by_status'].get('pending', 0),
                'processed_videos': url_stats['by_status'].get('completed', 0),
                'failed_videos': url_stats['by_status'].get('failed', 0),
                'recent_activity': recent_count,
                'platforms': url_stats['by_platform'],
                'ai_available': self.ai_analyzer.is_available()
            }
        except Exception as e:
            logger.error(f"Error getting dashboard stats: {e}")
            return {}
    
    def _is_recent(self, date_str: str) -> bool:
        """Check if a date string represents a recent date (within 24 hours)."""
        try:
            if not date_str:
                return False
            date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            now = datetime.now(date.tzinfo)
            return (now - date).total_seconds() < 86400  # 24 hours
        except:
            return False
    
    def _get_video_screenshots(self, video: Dict) -> List[Dict]:
        """Get screenshots for a video."""
        try:
            screenshots_dir = Path(video['video_dir']) / 'screenshots'
            if not screenshots_dir.exists():
                return []
            
            screenshots = []
            for screenshot_file in sorted(screenshots_dir.glob('frame_*.jpg')):
                screenshots.append({
                    'filename': screenshot_file.name,
                    'path': str(screenshot_file),
                    'url': url_for('serve_screenshot', 
                                 video_id=video['id'], 
                                 filename=screenshot_file.name)
                })
            
            return screenshots
        except Exception as e:
            logger.error(f"Error getting screenshots for video {video['id']}: {e}")
            return []
    
    def _perform_ai_search(self, query: str, limit: int = 20) -> List[Dict]:
        """Perform AI-powered visual search using database-stored embeddings."""
        try:
            # Generate query embedding using AI analyzer
            query_embedding = self.ai_analyzer.encode_text(query)
            if not query_embedding:
                logger.warning("Failed to generate query embedding")
                return []

            # Search using database-stored embeddings (much faster!)
            video_results = self.storage.search_videos_by_embedding(query_embedding, limit=limit)

            results = []
            for video in video_results:
                # Get a representative screenshot for display
                screenshots = self._get_video_screenshots(video)
                screenshot_filename = screenshots[0] if screenshots else 'frame_00001.jpg'

                results.append({
                    'video_id': video['id'],
                    'video_title': video['title'],
                    'video_url': video['url'],
                    'screenshot_filename': screenshot_filename,
                    'screenshot_url': url_for('serve_screenshot',
                                            video_id=video['id'],
                                            filename=screenshot_filename),
                    'similarity': video.get('similarity', 0.0),
                    'video_detail_url': url_for('video_detail', video_id=video['id']),
                    'ai_tags': video.get('ai_tags', [])  # Include AI-generated tags
                })

            return results

        except Exception as e:
            logger.error(f"Error performing AI search: {e}")
            return []
    
    def _register_filters(self):
        """Register custom Jinja2 filters."""

        @self.app.template_filter('file_exists')
        def file_exists_filter(path):
            """Check if a file exists."""
            try:
                return Path(path).exists()
            except:
                return False

    def run(self, host='127.0.0.1', port=5000, debug=True):
        """Run the Flask application."""
        logger.info(f"Starting Video Indexer Web App on http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


def create_app(config_path: str = "config/settings.yaml") -> Flask:
    """Application factory function."""
    web_app = SimpleVideoIndexerApp(config_path)
    return web_app.app


if __name__ == '__main__':
    print("🚀 Starting Enhanced Video Indexer Web App...")
    print("   - Enhanced ad/modal handling enabled")
    print("   - Adult content support active")
    print("   - Browser automation ready")

    # Create and run the app
    try:
        web_app = SimpleVideoIndexerApp()
        print("✅ Web app initialized successfully")
        print("🌐 Starting server on http://localhost:5001")
        web_app.run(debug=True, host='0.0.0.0', port=5001)
    except Exception as e:
        print(f"❌ Failed to start web app: {e}")
        import traceback
        traceback.print_exc()
