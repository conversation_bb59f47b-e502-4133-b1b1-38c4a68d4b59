"""
Video Processor Component for Video Search Indexer

This module handles video processing including downloading videos temporarily
and extracting screenshots at regular intervals for AI analysis.
"""

import os
import cv2
import logging
import tempfile
import yt_dlp
import time
import json
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
import ffmpeg
from PIL import Image

# Import AI analyzer
try:
    from .ai_analyzer import CLIPA<PERSON><PERSON>zer, get_default_tags
    AI_A<PERSON>ILABLE = True
except ImportError:
    try:
        from ai_analyzer import CLIP<PERSON><PERSON><PERSON><PERSON>, get_default_tags
        AI_AVAILABLE = True
    except ImportError:
        AI_AVAILABLE = False

# Import adult content processor
try:
    from .adult_content_processor import AdultContentProcessor
    ADULT_PROCESSOR_AVAILABLE = True
except ImportError:
    try:
        from adult_content_processor import AdultContentProcessor
        ADULT_PROCESSOR_AVAILABLE = True
    except ImportError:
        ADULT_PROCESSOR_AVAILABLE = False

# Set up logging for this module
logger = logging.getLogger(__name__)


class VideoProcessor:
    """
    Processes videos to extract screenshots at regular intervals for AI analysis.
    
    Features:
    - Downloads videos temporarily using yt-dlp
    - Extracts screenshots at configurable intervals (default: 5-10 seconds)
    - Handles various video formats and sources
    - Optimizes screenshot quality and size
    - Cleans up temporary files automatically
    - Provides progress tracking for long videos
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the video processor.
        
        Args:
            config: Configuration dictionary with processing settings
        """
        self.config = config or {}
        
        # Screenshot extraction settings
        self.screenshot_interval = self.config.get('screenshot_interval', 5)  # seconds
        self.max_video_duration = self.config.get('max_video_duration', 3600)  # 1 hour max
        self.screenshot_size = tuple(self.config.get('screenshot_size', [640, 480]))
        self.screenshot_quality = self.config.get('screenshot_quality', 85)

        # AI analysis settings
        self.enable_ai_analysis = self.config.get('enable_ai_analysis', True)
        self.enable_parallel_ai = self.config.get('enable_parallel_ai', True)
        self.ai_parallel_workers = self.config.get('ai_parallel_workers', None)  # Auto-detect based on system resources if None
        self.ai_analyzer = None

        # Initialize AI analyzer if available and enabled
        if self.enable_ai_analysis and AI_AVAILABLE:
            try:
                self.ai_analyzer = CLIPAnalyzer()
                if self.ai_analyzer.is_available():
                    logger.info("AI analyzer initialized successfully")
                else:
                    logger.warning("AI analyzer failed to initialize")
                    self.ai_analyzer = None
            except Exception as e:
                logger.warning(f"Failed to initialize AI analyzer: {e}")
                self.ai_analyzer = None
        elif not AI_AVAILABLE:
            logger.info("AI analysis disabled - libraries not available")
        
        # yt-dlp options for video downloading
        self.yt_dlp_opts = {
            'format': 'best[height<=720]/best',  # Prefer 720p or lower for faster processing
            'outtmpl': '%(title)s.%(ext)s',
            'quiet': True,
            'no_warnings': True,
            'extractaudio': False,
            'writeinfojson': False,
            'writethumbnail': False,
            'writesubtitles': False,
            'ignoreerrors': True,
        }
        
        # Create temporary directory for video processing
        self.temp_dir = Path(tempfile.mkdtemp(prefix='vid_search_'))
        logger.info(f"Created temporary directory: {self.temp_dir}")

        # Initialize adult content processor if available
        self.adult_processor = None
        if ADULT_PROCESSOR_AVAILABLE:
            try:
                self.adult_processor = AdultContentProcessor(config)
                logger.info("Adult content processor initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize adult content processor: {str(e)}")

        # All platforms now handled by yt-dlp (including adult content)
    
    def extract_screenshots(self, url: str, output_dir: Path, metadata: Dict = None) -> List[Path]:
        """
        Extract screenshots from a video URL at regular intervals.

        Automatically detects adult content platforms and uses specialized processing
        to avoid interfering with standard YouTube-based processing.

        Args:
            url: Video URL to process
            output_dir: Directory to save screenshots
            metadata: Optional metadata containing video duration info

        Returns:
            List of paths to extracted screenshot files
        """
        logger.info(f"Starting screenshot extraction from: {url}")

        # Create output directory
        output_dir.mkdir(parents=True, exist_ok=True)

        # Use yt-dlp for all platforms (it supports adult content sites too)
        logger.info("Using yt-dlp for video processing")
        try:
            # Download video temporarily
            video_path = self._download_video_temp(url)
            if not video_path:
                logger.error("Failed to download video for processing")
                return []

            # Extract screenshots using OpenCV
            screenshots = self._extract_screenshots_opencv(video_path, output_dir)

            # Clean up temporary video file
            self._cleanup_temp_file(video_path)

            logger.info(f"Successfully extracted {len(screenshots)} screenshots")
            return screenshots

        except Exception as e:
            logger.error(f"Error during screenshot extraction: {str(e)}")
            return []

    def _detect_platform(self, url: str) -> str:
        """Detect the platform from URL."""
        url_lower = url.lower()

        if 'pornhub.com' in url_lower:
            return 'pornhub'
        elif 'xvideos.com' in url_lower:
            return 'xvideos'
        elif 'redtube.com' in url_lower:
            return 'redtube'
        elif 'xhamster.com' in url_lower:
            return 'xhamster'
        elif 'youporn.com' in url_lower:
            return 'youporn'
        elif 'tube8.com' in url_lower:
            return 'tube8'
        elif 'spankbang.com' in url_lower:
            return 'spankbang'
        elif 'youtube.com' in url_lower or 'youtu.be' in url_lower:
            return 'youtube'
        elif 'vimeo.com' in url_lower:
            return 'vimeo'
        else:
            return 'unknown'

    def _extract_screenshots_adult_content(self, url: str, output_dir: Path, platform: str, metadata: Dict = None) -> List[Path]:
        """
        Extract screenshots from adult content using specialized browser automation.

        This method is completely separate from standard video processing to avoid
        interference with YouTube and other standard platforms.
        """
        if not self.adult_processor:
            logger.error("Adult content processor not available")
            return []

        logger.info(f"Using aggressive browser automation for {platform} content")

        try:
            # Use asyncio to run the browser-based extraction
            import asyncio

            # Create new event loop if none exists
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Run the browser-based extraction
            screenshots = loop.run_until_complete(
                self.adult_processor.extract_screenshots_browser(url, output_dir, platform, metadata)
            )

            logger.info(f"Adult content processor extracted {len(screenshots)} screenshots")
            return screenshots

        except Exception as e:
            logger.error(f"Error during adult content screenshot extraction: {str(e)}")
            return []
    
    def extract_screenshots_ffmpeg(self, url: str, output_dir: Path, metadata: Dict = None) -> List[Path]:
        """
        Alternative method using FFmpeg for screenshot extraction (more efficient for some formats).
        
        Args:
            url: Video URL to process
            output_dir: Directory to save screenshots
            metadata: Optional metadata containing video duration info
            
        Returns:
            List of paths to extracted screenshot files
        """
        logger.info(f"Starting FFmpeg screenshot extraction from: {url}")
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # Download video temporarily
            video_path = self._download_video_temp(url)
            if not video_path:
                return []
            
            # Extract screenshots using FFmpeg
            screenshots = self._extract_screenshots_ffmpeg(video_path, output_dir)
            
            # Clean up temporary video file
            self._cleanup_temp_file(video_path)
            
            logger.info(f"Successfully extracted {len(screenshots)} screenshots using FFmpeg")
            return screenshots
            
        except Exception as e:
            logger.error(f"Error during FFmpeg screenshot extraction: {str(e)}")
            return []
    
    def get_video_info(self, url: str) -> Optional[Dict]:
        """
        Get video information without downloading (duration, format, etc.).
        
        Args:
            url: Video URL to analyze
            
        Returns:
            Dictionary with video information or None if failed
        """
        try:
            with yt_dlp.YoutubeDL({'quiet': True, 'no_warnings': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                
                if info:
                    return {
                        'duration': info.get('duration', 0),
                        'width': info.get('width', 0),
                        'height': info.get('height', 0),
                        'fps': info.get('fps', 0),
                        'format_id': info.get('format_id', ''),
                        'filesize': info.get('filesize', 0),
                        'title': info.get('title', ''),
                    }
        except Exception as e:
            logger.error(f"Failed to get video info: {str(e)}")
        
        return None
    
    def _download_video_temp(self, url: str) -> Optional[Path]:
        """Download video to temporary location for processing."""
        try:
            # Configure yt-dlp for temporary download
            temp_opts = self.yt_dlp_opts.copy()
            temp_opts['outtmpl'] = str(self.temp_dir / '%(title)s.%(ext)s')
            
            with yt_dlp.YoutubeDL(temp_opts) as ydl:
                # Extract info first to check duration
                info = ydl.extract_info(url, download=False)
                
                if not info:
                    logger.error("Could not extract video information")
                    return None
                
                duration = info.get('duration', 0)
                if duration > self.max_video_duration:
                    logger.warning(f"Video too long ({duration}s), skipping download")
                    return None
                
                # Download the video
                ydl.download([url])
                
                # Find the downloaded file
                for file_path in self.temp_dir.iterdir():
                    if file_path.is_file() and file_path.suffix in ['.mp4', '.webm', '.mkv', '.avi', '.mov']:
                        logger.info(f"Downloaded video to: {file_path}")
                        return file_path
                
                logger.error("Downloaded video file not found")
                return None
                
        except Exception as e:
            logger.error(f"Failed to download video: {str(e)}")
            return None
    
    def _extract_screenshots_opencv(self, video_path: Path, output_dir: Path) -> List[Path]:
        """Extract screenshots using OpenCV."""
        screenshots = []
        
        try:
            # Open video file
            cap = cv2.VideoCapture(str(video_path))
            
            if not cap.isOpened():
                logger.error(f"Could not open video file: {video_path}")
                return []
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0
            
            logger.info(f"Video info: {duration:.1f}s, {fps:.1f} FPS, {total_frames} frames")
            
            # Calculate frame interval for screenshots
            frame_interval = int(fps * self.screenshot_interval) if fps > 0 else 30
            
            frame_count = 0
            screenshot_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Extract screenshot at intervals
                if frame_count % frame_interval == 0:
                    screenshot_path = output_dir / f"screenshot_{screenshot_count:05d}.jpg"
                    
                    # Resize frame if needed
                    if self.screenshot_size != (frame.shape[1], frame.shape[0]):
                        frame = cv2.resize(frame, self.screenshot_size, interpolation=cv2.INTER_LANCZOS4)
                    
                    # Save screenshot
                    success = cv2.imwrite(str(screenshot_path), frame, 
                                        [cv2.IMWRITE_JPEG_QUALITY, self.screenshot_quality])
                    
                    if success:
                        screenshots.append(screenshot_path)
                        screenshot_count += 1
                        logger.debug(f"Extracted screenshot {screenshot_count} at {frame_count/fps:.1f}s")
                
                frame_count += 1
            
            cap.release()
            
        except Exception as e:
            logger.error(f"Error extracting screenshots with OpenCV: {str(e)}")
        
        return screenshots
    
    def _extract_screenshots_ffmpeg(self, video_path: Path, output_dir: Path) -> List[Path]:
        """Extract screenshots using FFmpeg (alternative method)."""
        screenshots = []
        
        try:
            # Get video duration using ffprobe
            probe = ffmpeg.probe(str(video_path))
            duration = float(probe['streams'][0]['duration'])
            
            # Calculate number of screenshots
            num_screenshots = int(duration / self.screenshot_interval)
            
            logger.info(f"Extracting {num_screenshots} screenshots using FFmpeg")
            
            # Extract screenshots at regular intervals
            for i in range(num_screenshots):
                timestamp = i * self.screenshot_interval
                screenshot_path = output_dir / f"screenshot_{i:05d}.jpg"
                
                try:
                    (
                        ffmpeg
                        .input(str(video_path), ss=timestamp)
                        .filter('scale', self.screenshot_size[0], self.screenshot_size[1])
                        .output(str(screenshot_path), vframes=1, format='image2', 
                               **{'q:v': 2})  # High quality
                        .overwrite_output()
                        .run(quiet=True, capture_stdout=True, capture_stderr=True)
                    )
                    
                    if screenshot_path.exists():
                        screenshots.append(screenshot_path)
                        logger.debug(f"Extracted screenshot {i+1} at {timestamp:.1f}s")
                    
                except ffmpeg.Error as e:
                    logger.warning(f"Failed to extract screenshot at {timestamp}s: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error extracting screenshots with FFmpeg: {str(e)}")
        
        return screenshots
    
    def _optimize_screenshots(self, screenshot_paths: List[Path]) -> List[Path]:
        """Optimize screenshot files for size and quality."""
        optimized_paths = []
        
        for screenshot_path in screenshot_paths:
            try:
                with Image.open(screenshot_path) as img:
                    # Convert to RGB if needed
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Resize if needed
                    if img.size != self.screenshot_size:
                        img = img.resize(self.screenshot_size, Image.Resampling.LANCZOS)
                    
                    # Save with optimization
                    img.save(screenshot_path, 'JPEG', 
                            quality=self.screenshot_quality, 
                            optimize=True, 
                            progressive=True)
                    
                    optimized_paths.append(screenshot_path)
                    
            except Exception as e:
                logger.warning(f"Failed to optimize screenshot {screenshot_path}: {e}")
                # Keep original if optimization fails
                optimized_paths.append(screenshot_path)
        
        return optimized_paths

    def analyze_screenshots_with_ai(self, screenshot_dir: Path) -> Dict[str, Any]:
        """
        Analyze screenshots using enhanced AI analysis with CLIP embeddings and BLIP descriptions.

        Args:
            screenshot_dir: Directory containing screenshots to analyze

        Returns:
            Dictionary with comprehensive AI analysis results including descriptions
        """
        if not self.ai_analyzer or not self.ai_analyzer.is_available():
            logger.warning("AI analyzer not available for screenshot analysis")
            return {
                "success": False,
                "error": "AI analyzer not available",
                "embeddings": [],
                "descriptions": {},
                "tags": {},
                "video_summary": {}
            }

        logger.info(f"Starting enhanced AI analysis of screenshots in: {screenshot_dir}")

        # Check if we should use parallel processing based on system resources
        screenshot_files = list(screenshot_dir.glob('frame_*.jpg'))
        use_parallel = self._should_use_parallel_processing(len(screenshot_files))

        try:
            if use_parallel:
                logger.info(f"Using parallel AI processing for {len(screenshot_files)} screenshots")
                try:
                    from parallel_ai_analyzer import ParallelAIAnalyzer
                    parallel_analyzer = ParallelAIAnalyzer(max_workers=self.ai_parallel_workers)
                    parallel_result = parallel_analyzer.analyze_screenshots_parallel(screenshot_dir, enhanced_explicit=None)

                    if parallel_result.get('success'):
                        analysis_results = parallel_result['results']
                        logger.info(f"Parallel processing completed: {parallel_result.get('frames_per_second', 0):.1f} fps")
                    else:
                        logger.warning(f"Parallel processing failed, falling back to serial: {parallel_result.get('error')}")
                        use_parallel = False
                except ImportError:
                    logger.warning("Parallel AI analyzer not available, using serial processing")
                    use_parallel = False

            if not use_parallel:
                logger.info(f"Using serial AI processing for {len(screenshot_files)} screenshots")
                # Analyze all screenshots with both CLIP embeddings and BLIP descriptions
                analysis_results = self.ai_analyzer.analyze_screenshots_batch(screenshot_dir, enhanced_explicit=None)

            # Extract embeddings data for compatibility
            embeddings_data = []
            descriptions = {}
            frame_tags = {}

            for result in analysis_results:
                if result.get('success', False):
                    # Keep embeddings data structure for compatibility
                    embeddings_data.append({
                        'image_path': result['image_path'],
                        'embedding': result['embedding'],
                        'success': True
                    })

                    # Store descriptions per frame
                    image_name = Path(result['image_path']).name
                    if result.get('description'):
                        descriptions[image_name] = result['description']

                    # Store auto-generated tags per frame
                    if result.get('auto_tags'):
                        frame_tags[image_name] = result['auto_tags']
                else:
                    # Handle failed analysis
                    embeddings_data.append({
                        'image_path': result.get('image_path', 'unknown'),
                        'error': result.get('error', 'Analysis failed'),
                        'success': False
                    })

            # Generate video-level summary
            video_summary = self.ai_analyzer.generate_video_summary(analysis_results)

            # Save embeddings data for compatibility
            embeddings_file = screenshot_dir / "ai_embeddings.json"
            self.ai_analyzer.save_embeddings(embeddings_data, embeddings_file)

            # Save enhanced analysis data
            enhanced_analysis_file = screenshot_dir / "ai_analysis_enhanced.json"
            enhanced_data = {
                "analysis_results": analysis_results,
                "video_summary": video_summary,
                "descriptions": descriptions,
                "frame_tags": frame_tags,
                "timestamp": time.time()
            }

            with open(enhanced_analysis_file, 'w') as f:
                json.dump(enhanced_data, f, indent=2, default=str)

            result = {
                "success": True,
                "embeddings_count": len(embeddings_data),
                "embeddings_file": str(embeddings_file),
                "enhanced_analysis_file": str(enhanced_analysis_file),
                "embeddings": embeddings_data,
                "descriptions": descriptions,
                "frame_tags": frame_tags,
                "video_summary": video_summary,
                "analyzer_model": self.ai_analyzer.model_name,
                "captioner_available": self.ai_analyzer.captioner.is_available()
            }

            logger.info(f"Enhanced AI analysis complete: {len(embeddings_data)} screenshots analyzed")
            logger.info(f"Generated {len(descriptions)} descriptions and video summary")
            return result

        except Exception as e:
            logger.error(f"Enhanced AI analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "embeddings": [],
                "descriptions": {},
                "tags": {},
                "video_summary": {}
            }

    def _should_use_parallel_processing(self, frame_count: int) -> bool:
        """
        Determine if parallel processing should be used based on system resources and efficiency.

        Args:
            frame_count: Number of frames to process

        Returns:
            True if parallel processing should be used
        """
        if not self.enable_parallel_ai:
            return False

        try:
            # Try to get system resource information
            try:
                import psutil
                available_memory_gb = psutil.virtual_memory().available / (1024**3)
                cpu_cores = psutil.cpu_count()
            except ImportError:
                # Fallback estimates if psutil not available
                available_memory_gb = 8.0  # Conservative estimate
                cpu_cores = 4

            # Each AI worker needs ~2.2GB RAM
            memory_can_support_workers = int(available_memory_gb / 2.5)  # Conservative
            cpu_can_support_workers = max(1, cpu_cores - 1)  # Leave one core free

            # Maximum practical workers (diminishing returns beyond this)
            max_practical_workers = min(memory_can_support_workers, cpu_can_support_workers, 4)

            # Only use parallel if we can run at least 2 workers
            if max_practical_workers < 2:
                logger.info(f"Insufficient resources for parallel processing: "
                          f"memory supports {memory_can_support_workers} workers, "
                          f"CPU supports {cpu_can_support_workers} workers")
                return False

            # Calculate if parallel processing would be beneficial
            # Parallel has overhead, so only worth it if we have enough work to distribute
            min_frames_per_worker = 5  # Minimum frames to make worker worthwhile
            if frame_count < (max_practical_workers * min_frames_per_worker):
                logger.info(f"Too few frames ({frame_count}) for efficient parallel processing "
                          f"with {max_practical_workers} workers")
                return False

            logger.info(f"Using parallel processing: {frame_count} frames, "
                       f"{max_practical_workers} workers available, "
                       f"{available_memory_gb:.1f}GB RAM, {cpu_cores} CPU cores")
            return True

        except Exception as e:
            logger.warning(f"Error determining parallel processing capability: {e}")
            return False

    def _cleanup_temp_file(self, file_path: Path) -> None:
        """Clean up temporary video file."""
        try:
            if file_path.exists():
                file_path.unlink()
                logger.debug(f"Cleaned up temporary file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary file {file_path}: {e}")
    
    def cleanup_temp_directory(self) -> None:
        """Clean up the entire temporary directory."""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary directory: {e}")

        # Also cleanup adult content processor if it exists
        if self.adult_processor:
            try:
                self.adult_processor.cleanup_temp_directory()
            except Exception as e:
                logger.warning(f"Failed to cleanup adult content processor: {e}")

    def __del__(self):
        """Cleanup when object is destroyed."""
        self.cleanup_temp_directory()
