#!/usr/bin/env python3
"""
Simple Flask Web Interface for Video Search Indexer

Essential features only:
- Add video URLs
- View/delete stored sites  
- Search videos with category filters
- Browse categories
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from indexer import VideoIndexer
    from storage import VideoStorage
    from ai_analyzer import CLIPAnalyzer
except ImportError:
    try:
        # Try absolute imports if relative imports fail
        from src.indexer import VideoIndexer
        from src.storage import VideoStorage
        from src.ai_analyzer import C<PERSON><PERSON><PERSON><PERSON>yzer
    except ImportError:
        # Try adding parent directory to path
        sys.path.insert(0, str(Path(__file__).parent.parent))
        from src.indexer import VideoIndexer
        from src.storage import VideoStorage
        from src.ai_analyzer import CLIPAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleVideoIndexerApp:
    """
    Simple Flask web application for video indexer.
    
    Essential features only:
    - Add video URLs
    - View/delete stored sites  
    - Search videos with category filters
    - Browse categories
    """
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """Initialize the web application."""
        self.app = Flask(__name__, 
                        template_folder='../templates',
                        static_folder='../static')
        
        # Configure Flask app
        self.app.secret_key = 'simple-video-indexer-key'
        
        # Initialize components
        try:
            self.indexer = VideoIndexer(config_path)
            self.storage = VideoStorage()
            self.ai_analyzer = CLIPAnalyzer()
            logger.info("Simple web app initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
        
        # Register routes
        self._register_routes()

    def _register_routes(self):
        """Register simple Flask routes."""
        
        @self.app.route('/')
        def index():
            """Main page with categories and search."""
            try:
                # Get search parameters
                query = request.args.get('q', '')
                selected_category = request.args.get('category', '')

                # Get categories (platforms/domains)
                categories = self._get_categories()

                # Get total video count and stats
                all_videos = self.storage.search_videos("", limit=10000)
                total_videos = len(all_videos)

                # Calculate additional stats
                total_screenshots = sum(video.get('screenshot_count', 0) for video in all_videos)
                ai_analysis_count = sum(1 for video in all_videos if video.get('ai_tags'))

                # Handle search if query or category provided
                videos = []
                if query or selected_category:
                    filters = {}
                    if selected_category:
                        filters['platform'] = selected_category
                    videos = self.storage.search_videos(query, filters=filters, limit=50)

                return render_template('simple_index.html',
                                     categories=categories,
                                     total_videos=total_videos,
                                     total_screenshots=total_screenshots,
                                     ai_analysis_count=ai_analysis_count,
                                     query=query,
                                     selected_category=selected_category,
                                     videos=videos)
            except Exception as e:
                logger.error(f"Error loading main page: {e}")
                flash(f"Error: {e}", 'error')
                return render_template('simple_index.html', categories=[], total_videos=0,
                                     total_screenshots=0, ai_analysis_count=0, videos=[])

        @self.app.route('/add-url', methods=['GET', 'POST'])
        def add_url():
            """Add new video URL."""
            if request.method == 'POST':
                try:
                    url = request.form.get('url', '').strip()
                    if not url:
                        flash('URL is required', 'error')
                        return render_template('simple_add_url.html')
                    
                    # Add URL to indexer
                    result = self.indexer.add_url(url)
                    
                    if result['success']:
                        flash(f'Successfully added video from {result["platform"]}', 'success')
                        return redirect(url_for('index'))
                    else:
                        flash(f'Failed to add URL: {result["error"]}', 'error')
                        
                except Exception as e:
                    logger.error(f"Error adding URL: {e}")
                    flash(f'Error adding URL: {e}', 'error')
            
            return render_template('simple_add_url.html')

        @self.app.route('/sites')
        def view_sites():
            """View and manage stored sites."""
            try:
                # Get all videos grouped by domain
                all_videos = self.storage.search_videos("", limit=10000)
                sites = {}

                for video in all_videos:
                    domain = video.get('platform', 'unknown')
                    if domain not in sites:
                        sites[domain] = {
                            'count': 0,
                            'videos': [],
                            'total_duration': 0,
                            'total_screenshots': 0,
                            'ai_analyzed': 0
                        }
                    sites[domain]['count'] += 1
                    sites[domain]['videos'].append(video)
                    sites[domain]['total_duration'] += video.get('duration_seconds', 0)
                    sites[domain]['total_screenshots'] += video.get('screenshot_count', 0)
                    if video.get('ai_tags'):
                        sites[domain]['ai_analyzed'] += 1

                return render_template('simple_sites.html', sites=sites)
            except Exception as e:
                logger.error(f"Error loading sites: {e}")
                flash(f"Error loading sites: {e}", 'error')
                return render_template('simple_sites.html', sites={})

        @self.app.route('/delete-site', methods=['POST'])
        def delete_site():
            """Delete all videos from a specific platform/site."""
            try:
                platform = request.form.get('platform', '').strip()
                if not platform:
                    flash('Platform is required', 'error')
                    return redirect(url_for('view_sites'))

                # Get all videos from this platform
                all_videos = self.storage.search_videos("", limit=10000)
                platform_videos = [v for v in all_videos if v.get('platform') == platform]

                if not platform_videos:
                    flash(f'No videos found for platform: {platform}', 'warning')
                    return redirect(url_for('view_sites'))

                # Delete videos from storage
                deleted_count = 0
                for video in platform_videos:
                    try:
                        # Delete from database
                        self.storage.delete_video(video['id'])
                        deleted_count += 1
                    except Exception as e:
                        logger.error(f"Error deleting video {video['id']}: {e}")

                flash(f'Successfully deleted {deleted_count} videos from {platform}', 'success')

            except Exception as e:
                logger.error(f"Error deleting site: {e}")
                flash(f'Error deleting site: {e}', 'error')

            return redirect(url_for('view_sites'))

        @self.app.route('/delete-video/<int:video_id>', methods=['POST'])
        def delete_video(video_id):
            """Delete a video."""
            try:
                # Delete video from storage
                self.storage.delete_video(video_id)
                flash('Video deleted successfully', 'success')
            except Exception as e:
                logger.error(f"Error deleting video {video_id}: {e}")
                flash(f'Error deleting video: {e}', 'error')
            
            return redirect(url_for('view_sites'))



        @self.app.route('/videos')
        def view_videos():
            """View all processed videos with full details."""
            try:
                # Get all videos with full metadata
                videos = self.storage.search_videos("", limit=1000)

                # Get processing statistics
                stats = self.indexer.get_statistics()
                url_stats = self.indexer.url_manager.get_statistics()

                return render_template('simple_videos.html',
                                     videos=videos,
                                     stats=stats,
                                     url_stats=url_stats)
            except Exception as e:
                logger.error(f"Error loading videos: {e}")
                flash(f"Error loading videos: {e}", 'error')
                return render_template('simple_videos.html', videos=[], stats={}, url_stats={})

        @self.app.route('/progress')
        def view_progress():
            """View processing progress and status."""
            try:
                # Get URL manager statistics
                url_stats = self.indexer.url_manager.get_statistics()

                # Get pending URLs
                pending_urls = self.indexer.url_manager.get_pending_urls()

                # Get processing URLs (if available)
                processing_urls = []
                try:
                    processing_urls = self.indexer.url_manager.get_processing_urls()
                except AttributeError:
                    # Method doesn't exist, that's ok
                    pass

                # Get recent processing results (sorted by extraction date)
                all_videos = self.storage.search_videos("", limit=1000)
                recent_videos = sorted(
                    [v for v in all_videos if v.get('extraction_date')],
                    key=lambda x: x.get('extraction_date', ''),
                    reverse=True
                )[:20]

                return render_template('simple_progress.html',
                                     url_stats=url_stats,
                                     pending_urls=pending_urls,
                                     processing_urls=processing_urls,
                                     recent_videos=recent_videos)
            except Exception as e:
                logger.error(f"Error loading progress: {e}")
                flash(f"Error loading progress: {e}", 'error')
                return render_template('simple_progress.html', url_stats={}, pending_urls=[],
                                     processing_urls=[], recent_videos=[])

        @self.app.route('/process', methods=['POST'])
        def process_videos():
            """Process pending videos."""
            try:
                # Process pending URLs
                result = self.indexer.process_pending_urls(max_urls=5)

                if result['processed'] > 0:
                    flash(f'Processed {result["processed"]} videos successfully', 'success')
                else:
                    flash('No videos to process', 'info')

            except Exception as e:
                logger.error(f"Error processing videos: {e}")
                flash(f'Error processing videos: {e}', 'error')

            return redirect(url_for('view_progress'))

    def _get_categories(self) -> List[Dict]:
        """Get available categories (platforms)."""
        try:
            all_videos = self.storage.search_videos("", limit=10000)
            categories = {}
            
            for video in all_videos:
                platform = video.get('platform', 'unknown')
                if platform not in categories:
                    categories[platform] = 0
                categories[platform] += 1
            
            # Convert to list of dicts
            return [{'name': name, 'count': count} 
                   for name, count in sorted(categories.items())]
        except Exception as e:
            logger.error(f"Error getting categories: {e}")
            return []

    def run(self, host='127.0.0.1', port=5000, debug=False):
        """Run the Flask application."""
        logger.info(f"Starting Simple Video Indexer Web App on http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


# For backwards compatibility
VideoIndexerWebApp = SimpleVideoIndexerApp


if __name__ == '__main__':
    app = SimpleVideoIndexerApp()
    logger.info("Starting Simple Video Indexer Web App on http://127.0.0.1:5001")
    app.run(debug=True, port=5001)
