"""
Main Indexer Component for Video Search Indexer

This module orchestrates all components to provide a complete video indexing solution.
It coordinates URL processing, metadata extraction, file organization, and video processing.
"""

import logging
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import argparse
import sys

# Import our custom components
from .url_manager import URLManager
from .metadata_extractor import MetadataExtractor
from .file_organizer import FileOrganizer
from .video_processor import VideoProcessor
from .storage import VideoStorage

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/indexer.log')
    ]
)
logger = logging.getLogger(__name__)


class VideoIndexer:
    """
    Main video indexer that coordinates all components for complete video processing.
    
    Features:
    - Orchestrates URL management, metadata extraction, file organization, and video processing
    - Provides high-level interface for adding and processing video URLs
    - Handles configuration management and logging
    - Supports batch processing and progress tracking
    - Manages error handling and recovery
    """
    
    def __init__(self, config_path: str = "config/settings.yaml"):
        """
        Initialize the video indexer with all components.
        
        Args:
            config_path: Path to configuration file
        """
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Initialize components
        self.url_manager = URLManager(
            storage_path=self.config.get('storage', {}).get('data_directory', 'data') + '/urls.json'
        )
        
        self.metadata_extractor = MetadataExtractor(
            config=self.config.get('scraping', {})
        )
        
        self.file_organizer = FileOrganizer(
            base_data_dir=self.config.get('storage', {}).get('data_directory', 'data'),
            config=self.config.get('naming', {})
        )
        
        self.video_processor = VideoProcessor(
            config=self.config.get('video', {})
        )

        # Initialize storage component
        self.storage = VideoStorage(
            db_path=self.config.get('storage', {}).get('data_directory', 'data') + '/video_index.db'
        )

        logger.info("Video indexer initialized successfully")
    
    def add_url(self, url: str, extract_screenshots: bool = True) -> Dict[str, Any]:
        """
        Add a new video URL to the indexer.
        
        Args:
            url: Video URL to add
            extract_screenshots: Whether to extract screenshots during processing
            
        Returns:
            Dictionary with operation results
        """
        logger.info(f"Adding URL: {url}")
        
        # Add URL to manager
        result = self.url_manager.add_url(url)
        
        if not result['success']:
            logger.error(f"Failed to add URL: {result['error']}")
            return result
        
        # Store screenshot preference in metadata
        if 'metadata' not in result['entry']:
            result['entry']['metadata'] = {}
        result['entry']['metadata']['extract_screenshots'] = extract_screenshots
        
        logger.info(f"Successfully added URL: {url}")
        return result
    
    def process_pending_urls(self, max_urls: Optional[int] = None) -> Dict[str, Any]:
        """
        Process all pending URLs in the queue.
        
        Args:
            max_urls: Maximum number of URLs to process (None for all)
            
        Returns:
            Dictionary with processing results and statistics
        """
        pending_urls = self.url_manager.get_pending_urls()
        
        if not pending_urls:
            logger.info("No pending URLs to process")
            return {
                'success': True,
                'processed': 0,
                'failed': 0,
                'skipped': 0,
                'results': []
            }
        
        # Limit number of URLs if specified
        if max_urls:
            pending_urls = pending_urls[:max_urls]
        
        logger.info(f"Processing {len(pending_urls)} pending URLs")
        
        results = {
            'success': True,
            'processed': 0,
            'failed': 0,
            'skipped': 0,
            'results': []
        }
        
        for url_entry in pending_urls:
            url = url_entry['normalized_url']
            
            try:
                # Process individual URL
                process_result = self.process_url(url)
                results['results'].append(process_result)
                
                if process_result['success']:
                    results['processed'] += 1
                else:
                    results['failed'] += 1
                    
            except Exception as e:
                logger.error(f"Unexpected error processing {url}: {str(e)}")
                results['failed'] += 1
                results['results'].append({
                    'url': url,
                    'success': False,
                    'error': f"Unexpected error: {str(e)}"
                })
        
        logger.info(f"Processing complete: {results['processed']} processed, {results['failed']} failed")
        return results

    def process_url(self, url: str) -> Dict[str, Any]:
        """
        Process a single URL through the complete indexing pipeline.

        Args:
            url: URL to process

        Returns:
            Dictionary with processing results
        """
        logger.info(f"Processing URL: {url}")

        # Update status to processing
        self.url_manager.update_url_status(url, 'processing')

        try:
            # Get URL entry for platform information
            url_entry = self.url_manager.urls_db.get(url)
            if not url_entry:
                error = "URL not found in database"
                logger.error(error)
                return {'url': url, 'success': False, 'error': error}

            platform = url_entry['platform']
            domain = url_entry['domain']

            # Step 1: Extract metadata
            logger.info(f"Extracting metadata for {url}")
            metadata = self.metadata_extractor.extract_metadata(url, platform)

            if not metadata.get('success', False):
                error = f"Metadata extraction failed: {metadata.get('error', 'Unknown error')}"
                logger.error(error)
                self.url_manager.update_url_status(url, 'failed', error)
                return {'url': url, 'success': False, 'error': error}

            # Step 2: Create directory structure
            logger.info(f"Creating directory structure for: {metadata['title']}")
            dir_info = self.file_organizer.create_video_directory(
                domain=domain,
                video_title=metadata['title'],
                video_id=metadata.get('video_id')
            )

            video_dir = dir_info['video_dir']

            # Step 3: Save metadata
            logger.info(f"Saving metadata to: {video_dir}")
            self.file_organizer.save_metadata(video_dir, metadata)
            self.file_organizer.save_metadata_json(video_dir, metadata)

            # Step 4: Download thumbnail
            thumbnail_url = metadata.get('thumbnail_url')
            if thumbnail_url:
                logger.info("Downloading thumbnail")
                self.file_organizer.download_thumbnail(video_dir, thumbnail_url)

            # Step 5: Extract screenshots (if enabled)
            extract_screenshots = url_entry.get('metadata', {}).get('extract_screenshots', True)
            screenshots = []
            ai_analysis = {}

            if extract_screenshots:
                logger.info("Extracting video screenshots")
                screenshots_dir = video_dir / "screenshots"

                try:
                    screenshots = self.video_processor.extract_screenshots(
                        url, screenshots_dir, metadata
                    )

                    if screenshots:
                        # Organize screenshots
                        organized_screenshots = self.file_organizer.organize_screenshots(
                            video_dir, screenshots
                        )
                        logger.info(f"Extracted {len(organized_screenshots)} screenshots")

                        # Step 6: AI Analysis of screenshots (if available)
                        logger.info("Starting AI analysis of screenshots")
                        ai_analysis = self.video_processor.analyze_screenshots_with_ai(screenshots_dir)

                        if ai_analysis.get('success', False):
                            logger.info(f"AI analysis complete: {ai_analysis['embeddings_count']} screenshots analyzed")

                            # Save AI analysis results to metadata
                            ai_metadata_file = video_dir / "ai_analysis.json"
                            import json
                            with open(ai_metadata_file, 'w') as f:
                                json.dump(ai_analysis, f, indent=2)
                            logger.info(f"Saved AI analysis to: {ai_metadata_file}")
                        else:
                            logger.warning(f"AI analysis failed: {ai_analysis.get('error', 'Unknown error')}")
                    else:
                        logger.warning("No screenshots were extracted")

                except Exception as e:
                    logger.warning(f"Screenshot extraction failed: {str(e)}")
                    # Don't fail the entire process if screenshots fail

            # Step 7: Store video in database
            logger.info(f"Storing video in database: {metadata['title']}")
            try:
                # Add AI analysis tags to metadata if available
                if ai_analysis.get('success') and ai_analysis.get('tags'):
                    existing_tags = metadata.get('tags', [])
                    ai_tags = list(ai_analysis.get('tags', {}).keys())
                    # Combine existing tags with AI-generated tags
                    metadata['tags'] = existing_tags + ai_tags

                # Store video in database
                video_db_id = self.storage.store_video(
                    metadata=metadata,
                    video_dir=str(video_dir),
                    screenshot_count=len(screenshots)
                )
                logger.info(f"Successfully stored video in database with ID: {video_db_id}")

            except Exception as e:
                logger.error(f"Failed to store video in database: {str(e)}")
                # Don't fail the entire process if database storage fails
                video_db_id = None

            # Step 8: Update status to completed
            self.url_manager.update_url_status(url, 'completed')

            result = {
                'url': url,
                'success': True,
                'metadata': metadata,
                'video_dir': str(video_dir),
                'screenshots_count': len(screenshots),
                'database_id': video_db_id,
                'ai_analysis': {
                    'enabled': ai_analysis.get('success', False),
                    'embeddings_count': ai_analysis.get('embeddings_count', 0),
                    'model_used': ai_analysis.get('analyzer_model', 'none'),
                    'tags_extracted': len(ai_analysis.get('tags', {}))
                },
                'processing_time': datetime.now().isoformat()
            }

            logger.info(f"Successfully processed: {metadata['title']}")
            return result

        except Exception as e:
            error = f"Processing failed: {str(e)}"
            logger.error(f"Error processing {url}: {error}")
            self.url_manager.update_url_status(url, 'failed', error)

            return {
                'url': url,
                'success': False,
                'error': error
            }

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the indexer."""
        url_stats = self.url_manager.get_statistics()

        # Add file system statistics
        data_dir = Path(self.config.get('storage', {}).get('data_directory', 'data'))

        total_size = 0
        total_files = 0
        domain_stats = {}

        if data_dir.exists():
            for domain_dir in data_dir.iterdir():
                if domain_dir.is_dir() and domain_dir.name != 'urls.json':
                    domain_size = 0
                    domain_files = 0

                    for file_path in domain_dir.rglob('*'):
                        if file_path.is_file():
                            file_size = file_path.stat().st_size
                            domain_size += file_size
                            total_size += file_size
                            domain_files += 1
                            total_files += 1

                    domain_stats[domain_dir.name] = {
                        'size_bytes': domain_size,
                        'size_mb': round(domain_size / (1024 * 1024), 2),
                        'files': domain_files
                    }

        return {
            'urls': url_stats,
            'storage': {
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'total_files': total_files,
                'by_domain': domain_stats
            },
            'timestamp': datetime.now().isoformat()
        }

    def list_indexed_content(self, domain: Optional[str] = None) -> List[Dict]:
        """List all indexed content, optionally filtered by domain."""
        content = []

        data_dir = Path(self.config.get('storage', {}).get('data_directory', 'data'))

        if not data_dir.exists():
            return content

        # Get domains to search
        domains_to_search = [domain] if domain else [d.name for d in data_dir.iterdir() if d.is_dir()]

        for domain_name in domains_to_search:
            domain_dir = data_dir / domain_name
            index_dir = domain_dir / "index"

            if not index_dir.exists():
                continue

            for video_dir in index_dir.iterdir():
                if not video_dir.is_dir():
                    continue

                # Load metadata if available
                metadata_file = video_dir / "metadata.json"
                metadata = {}

                if metadata_file.exists():
                    try:
                        import json
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                    except:
                        pass

                # Count screenshots
                screenshots_dir = video_dir / "screenshots"
                screenshot_count = 0
                if screenshots_dir.exists():
                    screenshot_count = len([f for f in screenshots_dir.iterdir() if f.is_file()])

                content.append({
                    'domain': domain_name,
                    'folder_name': video_dir.name,
                    'title': metadata.get('title', video_dir.name),
                    'video_dir': str(video_dir),
                    'has_thumbnail': (video_dir / "thumbnail.jpg").exists(),
                    'has_metadata': metadata_file.exists(),
                    'screenshot_count': screenshot_count,
                    'metadata': metadata
                })

        return content

    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        config_file = Path(config_path)

        if not config_file.exists():
            logger.warning(f"Config file not found: {config_path}, using defaults")
            return {}

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"Loaded configuration from: {config_path}")
                return config or {}
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}

    def cleanup(self):
        """Clean up resources and temporary files."""
        try:
            self.video_processor.cleanup_temp_directory()
            self.file_organizer.cleanup_empty_directories()
            logger.info("Cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


def main():
    """Command-line interface for the video indexer."""
    parser = argparse.ArgumentParser(description='Video Search Indexer')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Add URL command
    add_parser = subparsers.add_parser('add-url', help='Add a video URL to index')
    add_parser.add_argument('url', help='Video URL to add')
    add_parser.add_argument('--no-screenshots', action='store_true',
                           help='Skip screenshot extraction')

    # Process command
    process_parser = subparsers.add_parser('process', help='Process pending URLs')
    process_parser.add_argument('--max-urls', type=int,
                               help='Maximum number of URLs to process')

    # List command
    list_parser = subparsers.add_parser('list', help='List indexed content')
    list_parser.add_argument('--domain', help='Filter by domain')

    # Stats command
    subparsers.add_parser('stats', help='Show indexer statistics')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # Initialize indexer
    try:
        indexer = VideoIndexer()
    except Exception as e:
        print(f"Failed to initialize indexer: {e}")
        sys.exit(1)

    try:
        if args.command == 'add-url':
            result = indexer.add_url(args.url, extract_screenshots=not args.no_screenshots)
            if result['success']:
                print(f"✓ Added URL: {args.url}")
                print(f"  Platform: {result['platform']}")
                print(f"  Domain: {result['domain']}")
            else:
                print(f"✗ Failed to add URL: {result['error']}")
                sys.exit(1)

        elif args.command == 'process':
            print("Processing pending URLs...")
            result = indexer.process_pending_urls(max_urls=args.max_urls)
            print(f"✓ Processed: {result['processed']}")
            print(f"✗ Failed: {result['failed']}")

            if result['failed'] > 0:
                print("\nFailed URLs:")
                for res in result['results']:
                    if not res['success']:
                        print(f"  {res['url']}: {res['error']}")

        elif args.command == 'list':
            content = indexer.list_indexed_content(domain=args.domain)
            if not content:
                print("No indexed content found")
            else:
                print(f"Found {len(content)} indexed videos:")
                for item in content:
                    print(f"  [{item['domain']}] {item['title']}")
                    print(f"    Screenshots: {item['screenshot_count']}")
                    print(f"    Path: {item['video_dir']}")
                    print()

        elif args.command == 'stats':
            stats = indexer.get_statistics()
            print("Indexer Statistics:")
            print(f"  Total URLs: {stats['urls']['total_urls']}")
            print(f"  By Status: {stats['urls']['by_status']}")
            print(f"  Storage: {stats['storage']['total_size_mb']} MB")
            print(f"  Total Files: {stats['storage']['total_files']}")

    finally:
        indexer.cleanup()


if __name__ == '__main__':
    main()
