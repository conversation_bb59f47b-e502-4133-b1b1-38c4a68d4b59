"""
AI Analysis Component for Video Search Indexer

This module provides CLIP-based image analysis for video screenshots,
enabling semantic search and automatic content understanding.
"""

import logging
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image
import json

# Set up logging
logger = logging.getLogger(__name__)

try:
    import open_clip
    CLIP_AVAILABLE = True
    logger.info("CLIP libraries loaded successfully")
except ImportError as e:
    CLIP_AVAILABLE = False
    logger.warning(f"CLIP libraries not available: {e}")
    logger.warning("Install with: pip install torch open-clip-torch")

# Import transformers for BLIP image captioning
try:
    from transformers import BlipProcessor, BlipForConditionalGeneration
    BLIP_AVAILABLE = True
    logger.info("BLIP libraries loaded successfully")
except ImportError as e:
    BLIP_AVAILABLE = False
    logger.warning(f"BLIP libraries not available: {e}")
    logger.warning("Install with: pip install transformers")


class BLIPCaptioner:
    """
    BLIP-based image captioning for generating descriptive text for video frames.
    """

    def __init__(self, model_name: str = "Salesforce/blip-image-captioning-base"):
        """Initialize BLIP captioning model."""
        self.model_name = model_name
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.processor = None
        self.model = None
        self.is_loaded = False

        if BLIP_AVAILABLE:
            try:
                self._load_model()
            except Exception as e:
                logger.error(f"Failed to load BLIP model: {e}")
                self.is_loaded = False
        else:
            logger.warning("BLIP not available - image captioning disabled")

    def _load_model(self):
        """Load the BLIP model and processor."""
        logger.info(f"Loading BLIP model: {self.model_name}")

        self.processor = BlipProcessor.from_pretrained(self.model_name)
        self.model = BlipForConditionalGeneration.from_pretrained(self.model_name)
        self.model.to(self.device)
        self.model.eval()

        self.is_loaded = True
        logger.info(f"BLIP model loaded successfully on {self.device}")

    def is_available(self) -> bool:
        """Check if BLIP captioning is available."""
        return BLIP_AVAILABLE and self.is_loaded

    def generate_caption(self, image_path: Path, max_length: int = 50) -> str:
        """
        Generate a descriptive caption for an image.

        Args:
            image_path: Path to the image file
            max_length: Maximum length of generated caption

        Returns:
            Generated caption text
        """
        if not self.is_available():
            return "Caption generation not available"

        try:
            # Load and preprocess image
            image = Image.open(image_path).convert('RGB')
            inputs = self.processor(image, return_tensors="pt").to(self.device)

            # Generate caption
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    max_length=max_length,
                    num_beams=5,
                    early_stopping=True,
                    do_sample=False
                )

            # Decode the generated caption
            caption = self.processor.decode(generated_ids[0], skip_special_tokens=True)
            return caption.strip()

        except Exception as e:
            logger.error(f"Failed to generate caption for {image_path}: {e}")
            return f"Error generating caption: {str(e)}"

    def generate_captions_batch(self, image_paths: List[Path], max_length: int = 50) -> List[str]:
        """
        Generate captions for multiple images in batch.

        Args:
            image_paths: List of image file paths
            max_length: Maximum length of generated captions

        Returns:
            List of generated captions
        """
        if not self.is_available():
            return ["Caption generation not available"] * len(image_paths)

        captions = []
        batch_size = 4  # Process in small batches to avoid memory issues

        for i in range(0, len(image_paths), batch_size):
            batch_paths = image_paths[i:i + batch_size]
            batch_captions = []

            try:
                # Load and preprocess batch of images
                images = []
                for path in batch_paths:
                    try:
                        image = Image.open(path).convert('RGB')
                        images.append(image)
                    except Exception as e:
                        logger.error(f"Failed to load image {path}: {e}")
                        images.append(None)

                # Filter out failed images
                valid_images = [img for img in images if img is not None]
                if not valid_images:
                    batch_captions = ["Failed to load image"] * len(batch_paths)
                else:
                    # Process batch
                    inputs = self.processor(valid_images, return_tensors="pt", padding=True).to(self.device)

                    # Generate captions
                    with torch.no_grad():
                        generated_ids = self.model.generate(
                            **inputs,
                            max_length=max_length,
                            num_beams=5,
                            early_stopping=True,
                            do_sample=False
                        )

                    # Decode captions
                    batch_captions = self.processor.batch_decode(generated_ids, skip_special_tokens=True)
                    batch_captions = [caption.strip() for caption in batch_captions]

                    # Handle failed images
                    caption_idx = 0
                    final_captions = []
                    for img in images:
                        if img is not None:
                            final_captions.append(batch_captions[caption_idx])
                            caption_idx += 1
                        else:
                            final_captions.append("Failed to load image")
                    batch_captions = final_captions

            except Exception as e:
                logger.error(f"Failed to process batch: {e}")
                batch_captions = [f"Error: {str(e)}"] * len(batch_paths)

            captions.extend(batch_captions)

        return captions


class CLIPAnalyzer:
    """
    CLIP-based image analyzer for video screenshots.
    
    Features:
    - Generate semantic embeddings for images
    - Search images using natural language queries
    - Batch processing for efficiency
    - Automatic content tagging
    - Similarity search between images
    """
    
    def __init__(self, model_name: str = "ViT-B-32", pretrained: str = "openai"):
        """
        Initialize the CLIP analyzer.

        Args:
            model_name: CLIP model architecture (ViT-B-32, ViT-L-14, etc.)
            pretrained: Pretrained weights to use (openai, laion2b_s34b_b79k, etc.)
        """
        self.model_name = model_name
        self.pretrained = pretrained
        self.model = None
        self.preprocess = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # Initialize BLIP captioner for generating descriptions
        self.captioner = BLIPCaptioner()

        if not CLIP_AVAILABLE:
            logger.error("CLIP libraries not available. Please install: pip install torch open-clip-torch")
            return

        self._load_model()
    
    def _load_model(self):
        """Load the CLIP model and preprocessing functions."""
        try:
            logger.info(f"Loading CLIP model: {self.model_name} ({self.pretrained})")
            
            # Load model, tokenizer, and preprocessing
            self.model, _, self.preprocess = open_clip.create_model_and_transforms(
                self.model_name, 
                pretrained=self.pretrained,
                device=self.device
            )
            
            self.tokenizer = open_clip.get_tokenizer(self.model_name)
            
            # Set model to evaluation mode
            self.model.eval()
            
            logger.info(f"CLIP model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load CLIP model: {e}")
            self.model = None
    
    def is_available(self) -> bool:
        """Check if the analyzer is ready to use."""
        return CLIP_AVAILABLE and self.model is not None
    
    def analyze_image(self, image_path: Path) -> Dict[str, Any]:
        """
        Analyze a single image and return embeddings and metadata.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary with embeddings and analysis results
        """
        if not self.is_available():
            return {"error": "CLIP analyzer not available"}
        
        try:
            # Load and preprocess image
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.preprocess(image).unsqueeze(0).to(self.device)
            
            # Generate image embedding
            with torch.no_grad():
                image_features = self.model.encode_image(image_tensor)
                image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            
            # Convert to numpy for storage
            embedding = image_features.cpu().numpy().flatten()
            
            # Get basic image info
            width, height = image.size
            
            result = {
                "image_path": str(image_path),
                "embedding": embedding.tolist(),  # Convert to list for JSON serialization
                "embedding_dim": len(embedding),
                "model_name": self.model_name,
                "image_size": [width, height],
                "success": True
            }
            
            logger.debug(f"Analyzed image: {image_path.name}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze image {image_path}: {e}")
            return {
                "image_path": str(image_path),
                "error": str(e),
                "success": False
            }
    
    def analyze_screenshots_batch(self, screenshot_dir: Path, enhanced_explicit: bool = None) -> List[Dict[str, Any]]:
        """
        Analyze all screenshots in a directory with both CLIP embeddings and BLIP descriptions.
        Now includes content-aware analysis that automatically detects adult vs regular content.

        Args:
            screenshot_dir: Directory containing screenshot files
            enhanced_explicit: Whether to generate enhanced explicit descriptions (None = auto-detect)

        Returns:
            List of analysis results for each screenshot including descriptions
        """
        if not self.is_available():
            return [{"error": "CLIP analyzer not available"}]

        if not screenshot_dir.exists():
            logger.warning(f"Screenshot directory not found: {screenshot_dir}")
            return []

        # Find all image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_files = [
            f for f in screenshot_dir.iterdir()
            if f.is_file() and f.suffix.lower() in image_extensions
        ]

        if not image_files:
            logger.warning(f"No image files found in: {screenshot_dir}")
            return []

        # Sort files for consistent ordering
        image_files.sort()

        # Auto-detect content type if not specified
        if enhanced_explicit is None:
            enhanced_explicit = self._detect_adult_content(screenshot_dir)
            content_type = "adult content" if enhanced_explicit else "regular content"
            logger.info(f"Auto-detected {content_type} based on directory path")

        logger.info(f"Analyzing {len(image_files)} screenshots with CLIP embeddings and BLIP descriptions")
        if enhanced_explicit:
            logger.info("Using enhanced explicit analysis for adult content")
        else:
            logger.info("Using standard analysis for regular content")

        # Generate CLIP embeddings for all images
        results = []
        for image_file in image_files:
            result = self.analyze_image(image_file)
            results.append(result)

        # Generate BLIP descriptions in batch for efficiency
        if self.captioner.is_available():
            logger.info("Generating descriptive captions for all screenshots...")
            captions = self.captioner.generate_captions_batch(image_files, max_length=75)

            # Add captions to results
            for i, caption in enumerate(captions):
                if i < len(results) and results[i].get('success', False):
                    results[i]['description'] = caption
                    results[i]['has_description'] = True
                else:
                    # Handle case where CLIP analysis failed but we have a caption
                    if i < len(results):
                        results[i]['description'] = caption
                        results[i]['has_description'] = True
        else:
            logger.warning("BLIP captioner not available - no descriptions generated")
            for result in results:
                if result.get('success', False):
                    result['description'] = "Description generation not available"
                    result['has_description'] = False

        # Generate automatic tags and enhanced descriptions
        total_frames = len(results)
        for i, result in enumerate(results):
            if result.get('success', False) and result.get('has_description', False):
                # Extract meaningful tags from the description
                description = result['description'].lower()
                auto_tags = self._extract_tags_from_description(description)
                result['auto_tags'] = auto_tags

                # Generate enhanced explicit description if enabled
                if enhanced_explicit:
                    enhanced_desc = self._enhance_description_explicit(
                        result['description'],
                        i + 1,  # Frame number (1-based)
                        total_frames
                    )
                    result['enhanced_description'] = enhanced_desc

        logger.info(f"Completed enhanced analysis of {len(results)} screenshots")
        return results

    def _extract_tags_from_description(self, description: str) -> List[str]:
        """
        Extract meaningful tags from a BLIP-generated description.

        Args:
            description: Generated description text

        Returns:
            List of extracted tags
        """
        import re

        # Common words to filter out
        stop_words = {
            'a', 'an', 'the', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'of', 'in', 'on', 'at', 'to',
            'for', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'and', 'or',
            'but', 'if', 'then', 'else', 'when', 'where', 'why', 'how', 'all',
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
            'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
        }

        # Extract meaningful words (nouns, adjectives, verbs)
        words = re.findall(r'\b[a-zA-Z]+\b', description.lower())

        # Filter out stop words and short words
        meaningful_words = [
            word for word in words
            if len(word) > 2 and word not in stop_words
        ]

        # Remove duplicates while preserving order
        tags = []
        seen = set()
        for word in meaningful_words:
            if word not in seen:
                tags.append(word)
                seen.add(word)

        # Limit to most relevant tags
        return tags[:10]

    def _enhance_description_explicit(self, basic_description: str, frame_number: int, total_frames: int) -> str:
        """
        Enhance a basic description with explicit adult content details.

        Args:
            basic_description: Basic BLIP-generated description
            frame_number: Current frame number
            total_frames: Total frames for context

        Returns:
            Enhanced explicit description
        """
        if not basic_description or basic_description == "Error generating caption":
            return f"Frame {frame_number}: Explicit sexual activity in progress"

        # Adult content vocabulary replacements
        replacements = {
            'having sex': 'fucking hard',
            'making love': 'fucking passionately',
            'intimate': 'fucking intensely',
            'together': 'fucking each other',
            'close': 'fucking deep',
            'touching': 'groping and fondling',
            'kissing': 'making out and touching',
            'embracing': 'grinding against each other',
            'lying': 'positioned for fucking',
            'sitting': 'riding and bouncing',
            'standing': 'fucking standing up',
            'on top': 'riding cock',
            'underneath': 'getting pounded',
            'behind': 'getting fucked from behind',
            'in front': 'sucking cock',
            'between': 'getting fucked by multiple cocks',
            'with': 'getting fucked by',
            'and': 'while getting fucked by',
            'woman': 'horny slut',
            'girl': 'sexy babe',
            'man': 'hung stud',
            'guy': 'big cock',
            'black': 'big black',
            'blonde': 'hot blonde'
        }

        # Convert to lowercase for processing
        enhanced = basic_description.lower()

        # Replace euphemisms with explicit terms
        for old, new in replacements.items():
            enhanced = enhanced.replace(old, new)

        # Add position and intensity based on frame progression
        progress = frame_number / total_frames

        if progress < 0.2:  # Beginning - setup/foreplay
            position_context = 'in the opening scene'
        elif progress < 0.5:  # Early action
            position_context = 'as the action heats up'
        elif progress < 0.8:  # Peak action
            position_context = 'in the intense main action'
        else:  # Climax/ending
            position_context = 'approaching the climactic finale'

        # Add explicit details based on common adult video patterns
        if 'fucking' in enhanced and 'hard' not in enhanced:
            enhanced += ' with deep, powerful thrusts'
        if 'sucking' in enhanced:
            enhanced += ' taking it deep in her throat'
        if 'riding' in enhanced:
            enhanced += ' bouncing up and down on the thick shaft'
        if 'cock' in enhanced:
            enhanced += ' stretching her tight holes'
        if 'pussy' in enhanced:
            enhanced += ' getting pounded relentlessly'

        # Check for creampie/cumshot content in final frames
        if progress > 0.85:  # Final 15% of video
            if any(term in enhanced for term in ['cum', 'orgasm', 'finish', 'climax']):
                enhanced += ' with explosive cumshot and creampie finish'
            elif 'pussy' in enhanced or 'hole' in enhanced:
                enhanced += ' leading to intense creampie climax'

        # Add contextual enhancement
        enhanced = f"{enhanced} {position_context}"

        return enhanced.capitalize()

    def _detect_adult_content(self, screenshot_dir: Path) -> bool:
        """
        Detect if content is adult-oriented based on directory path and context.

        Args:
            screenshot_dir: Directory containing screenshots

        Returns:
            True if adult content detected, False for regular content
        """
        # Convert path to string for analysis
        path_str = str(screenshot_dir).lower()

        # Adult content site indicators
        adult_sites = [
            'xvideos', 'pornhub', 'xhamster', 'redtube', 'youporn', 'tube8',
            'spankbang', 'eporner', 'txxx', 'hqporner', 'porn', 'xxx',
            'adult', 'sex', 'nsfw', 'erotic'
        ]

        # Check if path contains adult site indicators
        for site in adult_sites:
            if site in path_str:
                return True

        # Regular content site indicators
        regular_sites = [
            'youtube', 'vimeo', 'dailymotion', 'twitch', 'tiktok',
            'instagram', 'facebook', 'twitter', 'news', 'educational'
        ]

        # Check if path contains regular site indicators
        for site in regular_sites:
            if site in path_str:
                return False

        # Default to regular content if uncertain
        return False

    def generate_video_summary(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a comprehensive video summary from all frame analysis results.

        Args:
            analysis_results: List of frame analysis results

        Returns:
            Dictionary containing video-level summary
        """
        if not analysis_results:
            return {
                "summary": "No analysis data available",
                "key_themes": [],
                "dominant_tags": [],
                "frame_count": 0
            }

        # Collect all descriptions and tags
        descriptions = []
        all_tags = []
        successful_frames = 0

        for result in analysis_results:
            if result.get('success', False):
                successful_frames += 1
                if result.get('description'):
                    descriptions.append(result['description'])
                if result.get('auto_tags'):
                    all_tags.extend(result['auto_tags'])

        # Generate video summary from descriptions
        if descriptions:
            # Create a comprehensive summary by analyzing common themes
            summary_parts = []

            # Find common themes in descriptions
            theme_words = {}
            for desc in descriptions:
                words = desc.lower().split()
                for word in words:
                    if len(word) > 3:  # Only consider meaningful words
                        theme_words[word] = theme_words.get(word, 0) + 1

            # Get most common themes
            sorted_themes = sorted(theme_words.items(), key=lambda x: x[1], reverse=True)
            key_themes = [theme for theme, count in sorted_themes[:10] if count > 1]

            # Create summary based on most common descriptions
            if len(descriptions) <= 3:
                summary = " | ".join(descriptions)
            else:
                # Sample key descriptions
                sample_descriptions = descriptions[::max(1, len(descriptions)//3)][:3]
                summary = " | ".join(sample_descriptions)
                if len(descriptions) > 3:
                    summary += f" | (and {len(descriptions)-3} more scenes)"
        else:
            summary = "Video analysis completed but no descriptions available"
            key_themes = []

        # Count tag frequencies
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1

        # Get most dominant tags
        dominant_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:15]
        dominant_tags = [tag for tag, count in dominant_tags]

        return {
            "summary": summary,
            "key_themes": key_themes,
            "dominant_tags": dominant_tags,
            "frame_count": len(analysis_results),
            "successful_frames": successful_frames,
            "description_coverage": f"{len(descriptions)}/{successful_frames}" if successful_frames > 0 else "0/0"
        }
    
    def encode_text(self, text: str) -> List[float]:
        """
        Encode text into CLIP embedding vector.

        Args:
            text: Text to encode

        Returns:
            List of float values representing the text embedding
        """
        if not self.is_available():
            logger.error("CLIP model not available for text encoding")
            return []

        try:
            # Tokenize and encode text
            text_tokens = self.tokenizer([text])

            with torch.no_grad():
                text_features = self.model.encode_text(text_tokens)
                text_features = text_features / text_features.norm(dim=-1, keepdim=True)  # Normalize

            # Convert to list of floats
            embedding = text_features.cpu().numpy().flatten().tolist()
            logger.debug(f"Generated text embedding for: '{text}' (dim: {len(embedding)})")
            return embedding

        except Exception as e:
            logger.error(f"Failed to encode text '{text}': {e}")
            return []

    def search_images(self, query: str, embeddings_data: List[Dict], top_k: int = 10) -> List[Dict]:
        """
        Search images using a text query.
        
        Args:
            query: Natural language search query
            embeddings_data: List of image data with embeddings
            top_k: Number of top results to return
            
        Returns:
            List of matching images with similarity scores
        """
        if not self.is_available():
            return [{"error": "CLIP analyzer not available"}]
        
        try:
            # Encode the text query
            text_tokens = self.tokenizer([query]).to(self.device)
            
            with torch.no_grad():
                text_features = self.model.encode_text(text_tokens)
                text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            text_embedding = text_features.cpu().numpy().flatten()
            
            # Calculate similarities with all images
            similarities = []
            for img_data in embeddings_data:
                if 'embedding' not in img_data or not img_data.get('success', False):
                    continue
                
                img_embedding = np.array(img_data['embedding'])
                
                # Calculate cosine similarity
                similarity = np.dot(text_embedding, img_embedding)
                
                similarities.append({
                    'image_path': img_data['image_path'],
                    'similarity': float(similarity),
                    'image_data': img_data
                })
            
            # Sort by similarity (highest first)
            similarities.sort(key=lambda x: x['similarity'], reverse=True)
            
            # Return top k results
            results = similarities[:top_k]
            
            logger.info(f"Found {len(results)} matches for query: '{query}'")
            return results
            
        except Exception as e:
            logger.error(f"Search failed for query '{query}': {e}")
            return [{"error": str(e)}]
    
    def get_image_tags(self, image_path: Path, candidate_tags: List[str]) -> Dict[str, float]:
        """
        Get relevance scores for candidate tags for an image.
        
        Args:
            image_path: Path to the image
            candidate_tags: List of potential tags/labels
            
        Returns:
            Dictionary mapping tags to relevance scores
        """
        if not self.is_available():
            return {"error": "CLIP analyzer not available"}
        
        try:
            # Load and preprocess image
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.preprocess(image).unsqueeze(0).to(self.device)
            
            # Encode image
            with torch.no_grad():
                image_features = self.model.encode_image(image_tensor)
                image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            
            # Encode all candidate tags
            text_tokens = self.tokenizer(candidate_tags).to(self.device)
            
            with torch.no_grad():
                text_features = self.model.encode_text(text_tokens)
                text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Calculate similarities
            similarities = (image_features @ text_features.T).squeeze(0)
            
            # Convert to dictionary
            tag_scores = {}
            for tag, score in zip(candidate_tags, similarities):
                tag_scores[tag] = float(score)
            
            return tag_scores
            
        except Exception as e:
            logger.error(f"Failed to get tags for {image_path}: {e}")
            return {"error": str(e)}
    
    def save_embeddings(self, embeddings_data: List[Dict], output_path: Path):
        """Save embeddings data to a JSON file."""
        try:
            with open(output_path, 'w') as f:
                json.dump(embeddings_data, f, indent=2)
            logger.info(f"Saved embeddings to: {output_path}")
        except Exception as e:
            logger.error(f"Failed to save embeddings: {e}")
    
    def load_embeddings(self, input_path: Path) -> List[Dict]:
        """Load embeddings data from a JSON file."""
        try:
            with open(input_path, 'r') as f:
                data = json.load(f)
            logger.info(f"Loaded embeddings from: {input_path}")
            return data
        except Exception as e:
            logger.error(f"Failed to load embeddings: {e}")
            return []


def get_default_tags() -> List[str]:
    """Get a list of common tags for automatic image tagging."""
    return [
        # People and actions
        "person", "people", "man", "woman", "child", "face", "portrait",
        "dancing", "singing", "walking", "running", "sitting", "standing",
        "talking", "laughing", "smiling", "performing",
        
        # Objects and scenes
        "indoor", "outdoor", "stage", "microphone", "instrument", "guitar",
        "piano", "drums", "crowd", "audience", "concert", "performance",
        "building", "street", "car", "nature", "sky", "water",
        
        # Visual elements
        "close-up", "wide shot", "colorful", "black and white", "bright",
        "dark", "text", "logo", "animation", "cartoon",
        
        # Emotions and mood
        "happy", "sad", "excited", "calm", "energetic", "romantic",
        "dramatic", "funny", "serious"
    ]
