"""
Command Line Interface for Video Search Indexer

This module provides an enhanced CLI with interactive features,
progress tracking, and advanced configuration management.
"""

import argparse
import sys
import json
from pathlib import Path
from typing import Dict, List, Optional
import logging
from datetime import datetime

# Import our components
from .indexer import VideoIndexer
from .storage import VideoStorage

# Set up logging
logger = logging.getLogger(__name__)


class VideoIndexerCLI:
    """
    Enhanced command-line interface for the video indexer.
    
    Features:
    - Interactive mode for batch operations
    - Progress tracking with visual indicators
    - Advanced search and filtering
    - Configuration management
    - Export and import functionality
    """
    
    def __init__(self):
        """Initialize the CLI with indexer and storage components."""
        try:
            self.indexer = VideoIndexer()
            self.storage = VideoStorage()
        except Exception as e:
            print(f"❌ Failed to initialize indexer: {e}")
            sys.exit(1)
    
    def add_url_command(self, args):
        """Handle add-url command."""
        print(f"📥 Adding URL: {args.url}")
        
        result = self.indexer.add_url(args.url, extract_screenshots=not args.no_screenshots)
        
        if result['success']:
            print(f"✅ Successfully added URL")
            print(f"   Platform: {result['platform']}")
            print(f"   Domain: {result['domain']}")
            print(f"   Screenshots: {'Disabled' if args.no_screenshots else 'Enabled'}")
        else:
            print(f"❌ Failed to add URL: {result['error']}")
            sys.exit(1)
    
    def process_command(self, args):
        """Handle process command with progress tracking."""
        pending_urls = self.indexer.url_manager.get_pending_urls()
        
        if not pending_urls:
            print("ℹ️  No pending URLs to process")
            return
        
        total_urls = len(pending_urls)
        max_urls = args.max_urls or total_urls
        
        print(f"🔄 Processing {min(max_urls, total_urls)} of {total_urls} pending URLs...")
        
        # Process with progress tracking
        results = self.indexer.process_pending_urls(max_urls=max_urls)
        
        # Display results
        print(f"\n📊 Processing Results:")
        print(f"   ✅ Processed: {results['processed']}")
        print(f"   ❌ Failed: {results['failed']}")
        
        if results['failed'] > 0:
            print(f"\n❌ Failed URLs:")
            for result in results['results']:
                if not result['success']:
                    print(f"   • {result['url']}")
                    print(f"     Error: {result['error']}")
    
    def search_command(self, args):
        """Handle search command."""
        print(f"🔍 Searching for: '{args.query}'")

        # Build filters
        filters = {}
        if args.platform:
            filters['platform'] = args.platform
        if args.domain:
            filters['domain'] = args.domain
        if args.min_duration:
            filters['min_duration'] = args.min_duration
        if args.max_duration:
            filters['max_duration'] = args.max_duration

        # Perform search
        results = self.storage.search_videos(args.query, filters=filters, limit=args.limit)

        if not results:
            print("❌ No videos found matching your search criteria")
            return

        print(f"📋 Found {len(results)} videos:")
        print()

        for i, video in enumerate(results, 1):
            duration = self._format_duration(video.get('duration_seconds', 0))
            print(f"{i:2d}. {video['title']}")
            print(f"    Platform: {video['platform']} | Duration: {duration}")
            print(f"    Uploader: {video.get('uploader', 'Unknown')}")
            print(f"    Screenshots: {video.get('screenshot_count', 0)}")
            print(f"    Path: {video['video_dir']}")

            if video.get('tags'):
                print(f"    Tags: {', '.join(video['tags'][:5])}")
            print()

    def ai_search_command(self, args):
        """Handle AI-powered visual search command."""
        print(f"🤖 AI Visual Search for: '{args.query}'")

        # Import AI analyzer
        try:
            from .ai_analyzer import CLIPAnalyzer
            analyzer = CLIPAnalyzer()

            if not analyzer.is_available():
                print("❌ AI analyzer not available. Install with: pip install torch open-clip-torch")
                return

        except ImportError:
            print("❌ AI libraries not installed. Install with: pip install torch open-clip-torch")
            return

        # Find all videos with AI embeddings
        content = self.indexer.list_indexed_content()
        matching_screenshots = []

        for video in content:
            video_dir = Path(video['video_dir'])
            embeddings_file = video_dir / "screenshots" / "ai_embeddings.json"

            if embeddings_file.exists():
                try:
                    embeddings_data = analyzer.load_embeddings(embeddings_file)

                    # Search within this video's screenshots
                    matches = analyzer.search_images(args.query, embeddings_data, top_k=3)

                    for match in matches:
                        if match.get('similarity', 0) > 0.25:  # Threshold for relevance
                            matching_screenshots.append({
                                'video_title': video['title'],
                                'video_dir': video['video_dir'],
                                'screenshot_path': match['image_path'],
                                'similarity': match['similarity'],
                                'video_metadata': video
                            })

                except Exception as e:
                    print(f"⚠️  Error processing {video['title']}: {e}")

        if not matching_screenshots:
            print("❌ No matching screenshots found")
            print("💡 Make sure videos have been processed with AI analysis enabled")
            return

        # Sort by similarity
        matching_screenshots.sort(key=lambda x: x['similarity'], reverse=True)

        # Display results
        print(f"🎯 Found {len(matching_screenshots)} matching screenshots:")
        print()

        for i, match in enumerate(matching_screenshots[:args.limit], 1):
            similarity_percent = match['similarity'] * 100
            print(f"{i:2d}. {match['video_title']}")
            print(f"    Similarity: {similarity_percent:.1f}%")
            print(f"    Screenshot: {Path(match['screenshot_path']).name}")
            print(f"    Video Path: {match['video_dir']}")
            print()
    
    def list_command(self, args):
        """Handle list command."""
        content = self.indexer.list_indexed_content(domain=args.domain)
        
        if not content:
            domain_text = f" for domain '{args.domain}'" if args.domain else ""
            print(f"ℹ️  No indexed content found{domain_text}")
            return
        
        print(f"📋 Found {len(content)} indexed videos:")
        print()
        
        # Group by domain if not filtering
        if not args.domain:
            by_domain = {}
            for item in content:
                domain = item['domain']
                if domain not in by_domain:
                    by_domain[domain] = []
                by_domain[domain].append(item)
            
            for domain, videos in by_domain.items():
                print(f"📁 {domain.upper()} ({len(videos)} videos)")
                for video in videos[:5]:  # Show first 5 per domain
                    print(f"   • {video['title']}")
                    print(f"     Screenshots: {video['screenshot_count']} | Path: {video['video_dir']}")
                
                if len(videos) > 5:
                    print(f"   ... and {len(videos) - 5} more")
                print()
        else:
            # Show detailed list for single domain
            for i, item in enumerate(content, 1):
                print(f"{i:2d}. {item['title']}")
                print(f"    Screenshots: {item['screenshot_count']}")
                print(f"    Thumbnail: {'✅' if item['has_thumbnail'] else '❌'}")
                print(f"    Path: {item['video_dir']}")
                print()
    
    def stats_command(self, args):
        """Handle stats command."""
        print("📊 Indexer Statistics")
        print("=" * 50)
        
        # Get URL manager stats
        url_stats = self.indexer.url_manager.get_statistics()
        print(f"URLs:")
        print(f"  Total: {url_stats['total_urls']}")
        for status, count in url_stats['by_status'].items():
            print(f"  {status.title()}: {count}")
        
        print(f"\nPlatforms:")
        for platform, count in url_stats['by_platform'].items():
            print(f"  {platform.title()}: {count}")
        
        # Get storage stats
        storage_stats = self.storage.get_statistics()
        print(f"\nDatabase:")
        print(f"  Videos: {storage_stats['total_videos']}")
        print(f"  Screenshots: {storage_stats['total_screenshots']}")
        
        if 'duration' in storage_stats:
            avg_duration = self._format_duration(storage_stats['duration']['average_seconds'])
            max_duration = self._format_duration(storage_stats['duration']['max_seconds'])
            print(f"  Average Duration: {avg_duration}")
            print(f"  Longest Video: {max_duration}")
        
        # Get file system stats
        indexer_stats = self.indexer.get_statistics()
        storage_info = indexer_stats['storage']
        print(f"\nStorage:")
        print(f"  Total Size: {storage_info['total_size_mb']} MB")
        print(f"  Total Files: {storage_info['total_files']}")
        
        if storage_stats.get('top_tags'):
            print(f"\nTop Tags:")
            for tag, count in list(storage_stats['top_tags'].items())[:5]:
                print(f"  {tag}: {count}")
    
    def export_command(self, args):
        """Handle export command."""
        print(f"📤 Exporting data to: {args.output}")
        
        # Get all videos from storage
        all_videos = self.storage.search_videos("", limit=10000)  # Large limit to get all
        
        export_data = {
            'export_date': datetime.now().isoformat(),
            'total_videos': len(all_videos),
            'videos': all_videos
        }
        
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ Exported {len(all_videos)} videos to {args.output}")
            
        except Exception as e:
            print(f"❌ Export failed: {e}")
            sys.exit(1)
    
    def config_command(self, args):
        """Handle config command."""
        config_path = Path("config/settings.yaml")
        
        if args.show:
            if config_path.exists():
                with open(config_path, 'r') as f:
                    print(f.read())
            else:
                print("❌ Configuration file not found")
        
        elif args.edit:
            import subprocess
            editor = args.editor or 'nano'
            try:
                subprocess.run([editor, str(config_path)])
            except FileNotFoundError:
                print(f"❌ Editor '{editor}' not found")
                print("Try: python -m src.cli config --edit --editor vim")
    
    def cleanup_command(self, args):
        """Handle cleanup command."""
        print("🧹 Cleaning up...")

        # Cleanup indexer
        self.indexer.cleanup()

        # Optimize database
        if args.optimize_db:
            print("🔧 Optimizing database...")
            self.storage.optimize_database()

        print("✅ Cleanup completed")

    def web_command(self, args):
        """Handle web interface command."""
        print("🌐 Starting Video Search Indexer Web Interface...")
        print(f"📍 URL: http://{args.host}:{args.port}")
        print("🔧 Press Ctrl+C to stop the server")
        print()

        try:
            # Import web app components
            import sys
            from pathlib import Path

            # Add src to path for web app imports
            src_path = Path(__file__).parent
            if str(src_path) not in sys.path:
                sys.path.insert(0, str(src_path))

            from simple_web_app import SimpleVideoIndexerApp

            # Create and run web app
            web_app = SimpleVideoIndexerApp()

            # Open browser if requested
            if not args.no_browser and args.host in ['127.0.0.1', 'localhost']:
                import webbrowser
                url = f"http://{args.host}:{args.port}"
                print(f"🌐 Opening browser to {url}")
                webbrowser.open(url)

            # Run the web application
            web_app.run(host=args.host, port=args.port, debug=args.debug)

        except ImportError as e:
            print(f"❌ Web interface dependencies not available: {e}")
            print("Please install Flask: pip install Flask>=2.3.0")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Error starting web interface: {e}")
            sys.exit(1)
    
    def _format_duration(self, seconds: int) -> str:
        """Format duration from seconds to readable format."""
        if not seconds:
            return "0:00"
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"


def create_parser():
    """Create the argument parser with all commands."""
    parser = argparse.ArgumentParser(
        description='Video Search Indexer - Index and search video content locally',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s add-url "https://youtube.com/watch?v=example"
  %(prog)s process --max-urls 5
  %(prog)s search "machine learning" --platform youtube
  %(prog)s list --domain youtube
  %(prog)s stats
  %(prog)s export data.json
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Add URL command
    add_parser = subparsers.add_parser('add-url', help='Add a video URL to index')
    add_parser.add_argument('url', help='Video URL to add')
    add_parser.add_argument('--no-screenshots', action='store_true', 
                           help='Skip screenshot extraction')
    
    # Process command
    process_parser = subparsers.add_parser('process', help='Process pending URLs')
    process_parser.add_argument('--max-urls', type=int, 
                               help='Maximum number of URLs to process')
    
    # Search command
    search_parser = subparsers.add_parser('search', help='Search indexed videos')
    search_parser.add_argument('query', help='Search query')
    search_parser.add_argument('--platform', help='Filter by platform')
    search_parser.add_argument('--domain', help='Filter by domain')
    search_parser.add_argument('--min-duration', type=int, help='Minimum duration in seconds')
    search_parser.add_argument('--max-duration', type=int, help='Maximum duration in seconds')
    search_parser.add_argument('--limit', type=int, default=20, help='Maximum results')

    # AI Visual Search command
    ai_search_parser = subparsers.add_parser('ai-search', help='AI-powered visual search of screenshots')
    ai_search_parser.add_argument('query', help='Visual search query (e.g., "person dancing", "outdoor scene")')
    ai_search_parser.add_argument('--limit', type=int, default=10, help='Maximum results')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List indexed content')
    list_parser.add_argument('--domain', help='Filter by domain')
    
    # Stats command
    subparsers.add_parser('stats', help='Show indexer statistics')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export indexed data')
    export_parser.add_argument('output', help='Output file path (JSON format)')
    
    # Config command
    config_parser = subparsers.add_parser('config', help='Manage configuration')
    config_parser.add_argument('--show', action='store_true', help='Show current configuration')
    config_parser.add_argument('--edit', action='store_true', help='Edit configuration file')
    config_parser.add_argument('--editor', help='Editor to use (default: nano)')
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up temporary files and optimize')
    cleanup_parser.add_argument('--optimize-db', action='store_true', help='Optimize database')

    # Web interface command
    web_parser = subparsers.add_parser('web', help='Start the web interface')
    web_parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    web_parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    web_parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    web_parser.add_argument('--no-browser', action='store_true', help='Don\'t open browser')

    return parser


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize CLI
    cli = VideoIndexerCLI()
    
    # Route to appropriate command handler
    command_handlers = {
        'add-url': cli.add_url_command,
        'process': cli.process_command,
        'search': cli.search_command,
        'ai-search': cli.ai_search_command,
        'list': cli.list_command,
        'stats': cli.stats_command,
        'export': cli.export_command,
        'config': cli.config_command,
        'cleanup': cli.cleanup_command,
        'web': cli.web_command,
    }
    
    handler = command_handlers.get(args.command)
    if handler:
        try:
            handler(args)
        except KeyboardInterrupt:
            print("\n⚠️  Operation cancelled by user")
        except Exception as e:
            print(f"❌ Error: {e}")
            logger.exception("CLI command failed")
            sys.exit(1)
        finally:
            cli.indexer.cleanup()
    else:
        print(f"❌ Unknown command: {args.command}")
        parser.print_help()
        sys.exit(1)


if __name__ == '__main__':
    main()
