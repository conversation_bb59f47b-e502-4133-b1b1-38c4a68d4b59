#!/usr/bin/env python3
"""
Script to reprocess all existing videos with enhanced AI analysis
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from video_processor import VideoProcessor
from storage import VideoStorage

def reprocess_all_videos():
    """Reprocess all existing videos with enhanced AI analysis."""
    
    print("=== Reprocessing All Videos with Enhanced AI Analysis ===")
    
    # Initialize components
    storage = VideoStorage()
    processor = VideoProcessor()
    
    # Get all video directories
    video_base_dir = Path('data/youtube/index')
    if not video_base_dir.exists():
        print(f"Video directory not found: {video_base_dir}")
        return
    
    video_dirs = [d for d in video_base_dir.iterdir() if d.is_dir()]
    print(f"Found {len(video_dirs)} video directories")
    
    for i, video_dir in enumerate(video_dirs, 1):
        print(f"\n[{i}/{len(video_dirs)}] Processing: {video_dir.name}")
        
        # Check if screenshots exist
        screenshots_dir = video_dir / 'screenshots'
        if not screenshots_dir.exists():
            print(f"   ⚠️  No screenshots directory found, skipping")
            continue
        
        screenshot_files = list(screenshots_dir.glob('frame_*.jpg'))
        if len(screenshot_files) == 0:
            print(f"   ⚠️  No screenshots found, skipping")
            continue
        
        print(f"   📸 Found {len(screenshot_files)} screenshots")
        
        # Check if metadata exists
        metadata_file = video_dir / 'metadata.json'
        if not metadata_file.exists():
            print(f"   ⚠️  No metadata file found, skipping")
            continue
        
        try:
            # Load metadata
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            print(f"   🎬 Title: {metadata.get('title', 'Unknown')[:50]}...")
            
            # Run enhanced AI analysis
            print(f"   🤖 Running enhanced AI analysis...")
            ai_result = processor.analyze_screenshots_with_ai(screenshots_dir)
            
            if ai_result.get('success'):
                print(f"   ✅ AI analysis successful!")
                print(f"      - Embeddings: {ai_result.get('embeddings_count', 0)}")
                print(f"      - Descriptions: {len(ai_result.get('descriptions', {}))}")
                print(f"      - Frame tags: {len(ai_result.get('frame_tags', {}))}")
                
                # Show sample description
                descriptions = ai_result.get('descriptions', {})
                if descriptions:
                    sample_desc = list(descriptions.values())[0]
                    print(f"      - Sample description: {sample_desc[:60]}...")
                
                # Show video summary
                video_summary = ai_result.get('video_summary', {})
                if video_summary.get('summary'):
                    print(f"      - Video summary: {video_summary['summary'][:60]}...")
                
                # Update database
                print(f"   💾 Updating database...")
                video_id = storage.store_video(metadata, str(video_dir), len(screenshot_files))
                print(f"   ✅ Database updated! Video ID: {video_id}")
                
            else:
                print(f"   ❌ AI analysis failed: {ai_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   ❌ Error processing video: {e}")
            continue
    
    print(f"\n🎉 Reprocessing complete!")
    
    # Show final stats
    videos = storage.search_videos('', limit=100)
    enhanced_videos = [v for v in videos if v.get('video_summary')]
    
    print(f"\n📊 Final Statistics:")
    print(f"   - Total videos: {len(videos)}")
    print(f"   - Videos with enhanced AI: {len(enhanced_videos)}")
    
    if enhanced_videos:
        print(f"\n🎬 Enhanced Videos:")
        for video in enhanced_videos[:5]:
            print(f"   - {video['title'][:50]}...")
            if video.get('video_summary'):
                print(f"     Summary: {video['video_summary'][:80]}...")

if __name__ == '__main__':
    reprocess_all_videos()
