#!/usr/bin/env python3
"""
Test script to demonstrate resource-based parallel processing decisions
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from video_processor import VideoProcessor
from parallel_ai_analyzer import ParallelAIAnalyzer

def test_resource_based_decisions():
    """Test how the system makes resource-based processing decisions."""
    
    print("=== Resource-Based Processing Decision Testing ===")
    
    # Get system information
    try:
        import psutil
        total_memory_gb = psutil.virtual_memory().total / (1024**3)
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        cpu_cores = psutil.cpu_count()
        
        print(f"💻 System Resources:")
        print(f"   - Total RAM: {total_memory_gb:.1f} GB")
        print(f"   - Available RAM: {available_memory_gb:.1f} GB")
        print(f"   - CPU Cores: {cpu_cores}")
        
        # Calculate theoretical limits
        memory_workers = int(available_memory_gb / 2.5)  # Each worker needs ~2.2GB
        cpu_workers = max(1, cpu_cores - 1)
        optimal_workers = min(memory_workers, cpu_workers, 6)
        
        print(f"\n🧮 Resource Analysis:")
        print(f"   - Workers limited by memory: {memory_workers}")
        print(f"   - Workers limited by CPU: {cpu_workers}")
        print(f"   - Optimal workers: {optimal_workers}")
        
    except ImportError:
        print("⚠️  Install 'psutil' for detailed system analysis")
        optimal_workers = 2
    
    # Test decision making with different frame counts
    processor = VideoProcessor()
    
    print(f"\n🎯 Processing Decision Tests:")
    
    test_cases = [
        (5, "Very small video"),
        (10, "Small video"),
        (25, "Medium-small video"),
        (50, "Medium video"),
        (100, "Large video"),
        (200, "Very large video"),
        (500, "Huge video")
    ]
    
    for frame_count, description in test_cases:
        should_parallel = processor._should_use_parallel_processing(frame_count)
        decision = "PARALLEL" if should_parallel else "SERIAL"
        
        print(f"   - {frame_count:3d} frames ({description:15s}): {decision}")
    
    # Test actual parallel analyzer initialization
    print(f"\n⚡ Parallel Analyzer Initialization:")
    
    try:
        parallel_analyzer = ParallelAIAnalyzer()
        print(f"   ✅ Initialized with {parallel_analyzer.max_workers} workers")
        print(f"   📦 Frames per batch: {parallel_analyzer.frames_per_batch}")
        
        # Test with different worker counts
        print(f"\n🔧 Testing Different Worker Configurations:")
        
        for workers in [1, 2, 3, 4, 6, 8]:
            try:
                test_analyzer = ParallelAIAnalyzer(max_workers=workers)
                actual_workers = test_analyzer.max_workers
                
                if actual_workers == workers:
                    status = "✅ Accepted"
                else:
                    status = f"⚠️  Limited to {actual_workers}"
                
                print(f"   - Requested {workers} workers: {status}")
                
            except Exception as e:
                print(f"   - Requested {workers} workers: ❌ Failed ({e})")
        
    except Exception as e:
        print(f"   ❌ Failed to initialize parallel analyzer: {e}")
    
    # Resource efficiency analysis
    print(f"\n📊 Resource Efficiency Analysis:")
    
    if optimal_workers > 1:
        print(f"   - With {optimal_workers} workers:")
        print(f"     • Memory usage: ~{optimal_workers * 2.2:.1f} GB")
        print(f"     • CPU utilization: ~{optimal_workers}/{cpu_cores if 'cpu_cores' in locals() else 'N/A'} cores")
        print(f"     • Theoretical speedup: {min(optimal_workers, 4):.1f}x")
        
        # Processing time estimates
        frame_processing_time = 2.2  # seconds per frame serial
        parallel_time = frame_processing_time / min(optimal_workers, 4)  # Diminishing returns
        
        print(f"\n⏱️  Processing Time Estimates:")
        for frames in [50, 100, 200, 500]:
            serial_time = frames * frame_processing_time
            parallel_time_est = frames * parallel_time
            speedup = serial_time / parallel_time_est
            
            print(f"   - {frames:3d} frames: {serial_time/60:.1f}min → {parallel_time_est/60:.1f}min ({speedup:.1f}x speedup)")
    
    else:
        print(f"   ⚠️  System resources insufficient for parallel processing")
        print(f"   💡 Consider:")
        print(f"      - Closing other applications to free RAM")
        print(f"      - Using a machine with more memory")
        print(f"      - Processing smaller batches of videos")
    
    # Configuration recommendations
    print(f"\n⚙️  Configuration Recommendations:")
    print(f"   - enable_parallel_ai: True (system will auto-decide)")
    print(f"   - ai_parallel_workers: None (auto-detect optimal)")
    print(f"   - System automatically chooses based on:")
    print(f"     • Available RAM (need ~2.2GB per worker)")
    print(f"     • CPU cores (leave 1 core free)")
    print(f"     • Frame count (need minimum work per worker)")
    print(f"     • Processing efficiency (avoid overhead for small jobs)")

if __name__ == '__main__':
    test_resource_based_decisions()
