#!/usr/bin/env python3
"""
Test script for the updated adult content processor with real-world intelligence.
Tests the simplified approach focusing on free content.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.adult_content_processor import AdultContentProcessor
from src.indexer import VideoIndexer

def test_updated_adult_processor():
    """Test the updated adult content processor with free xvideos content."""
    
    # Free xvideos URL (not premium "RED" content)
    test_url = "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie"
    
    print(f"🧪 Testing updated adult content processor with: {test_url}")
    print("=" * 80)
    
    try:
        # Initialize the indexer
        indexer = VideoIndexer()
        
        print("📋 Processing URL with updated adult content handling...")
        result = indexer.process_url(test_url)
        
        if result and result.get('success'):
            print("✅ SUCCESS: Adult content processing completed!")
            print(f"📊 Video ID: {result.get('video_id')}")
            print(f"📁 Screenshots: {result.get('screenshot_count', 0)}")
            print(f"🎯 Platform: {result.get('platform')}")
            print(f"📝 Title: {result.get('title', 'N/A')}")
            
            # Check if screenshots were actually created
            video_id = result.get('video_id')
            if video_id:
                screenshot_dir = Path(f"data/screenshots/{video_id}")
                if screenshot_dir.exists():
                    screenshots = list(screenshot_dir.glob("*.jpg"))
                    print(f"🖼️  Found {len(screenshots)} screenshot files on disk")
                    for i, screenshot in enumerate(screenshots[:3]):  # Show first 3
                        print(f"   📸 {screenshot.name}")
                    if len(screenshots) > 3:
                        print(f"   ... and {len(screenshots) - 3} more")
                else:
                    print("⚠️  Screenshot directory not found")
            
            return True
        else:
            print("❌ FAILED: Adult content processing failed")
            if result:
                print(f"Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"💥 EXCEPTION: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_adult_processor():
    """Test the adult content processor directly."""
    
    test_url = "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie"
    
    print(f"\n🔧 Testing AdultContentProcessor directly with: {test_url}")
    print("=" * 80)
    
    try:
        processor = AdultContentProcessor()
        output_dir = Path("data/test_screenshots")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print("🚀 Starting direct adult content processing...")
        
        # Run the async method
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        screenshots = loop.run_until_complete(
            processor.extract_screenshots_browser(
                url=test_url,
                output_dir=output_dir,
                platform='xvideos',
                metadata={}
            )
        )
        
        loop.close()
        
        print(f"📸 Direct processing result: {len(screenshots)} screenshots")
        for screenshot in screenshots:
            print(f"   🖼️  {screenshot}")
            
        return len(screenshots) > 0
        
    except Exception as e:
        print(f"💥 DIRECT TEST EXCEPTION: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎬 Updated Adult Content Processor Test")
    print("Testing with real-world intelligence from browser exploration")
    print("Focus: Free content with simple age verification")
    print()
    
    # Test 1: Full indexer pipeline
    success1 = test_updated_adult_processor()
    
    # Test 2: Direct processor test
    success2 = test_direct_adult_processor()
    
    print("\n" + "=" * 80)
    print("📊 FINAL RESULTS:")
    print(f"   Full Pipeline Test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Direct Processor Test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 or success2:
        print("\n🎉 At least one test passed! The updated processor is working.")
    else:
        print("\n😞 Both tests failed. Need to investigate further.")
