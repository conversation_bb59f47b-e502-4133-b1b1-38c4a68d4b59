#!/usr/bin/env python3
"""
Enhance existing AI analysis with more detailed, explicit descriptions.
Takes the existing BLIP descriptions and enhances them with explicit adult content details.
"""

import json
import logging
from pathlib import Path
from typing import List, Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExistingAnalysisEnhancer:
    """Enhance existing AI analysis with explicit adult content descriptions."""
    
    def __init__(self):
        """Initialize the enhancer."""
        
        # Adult content vocabulary for enhanced descriptions
        self.adult_vocabulary = {
            'actions': [
                'fucking', 'sucking', 'riding', 'penetrating', 'thrusting', 'pounding', 
                'drilling', 'ramming', 'slamming', 'banging', 'screwing', 'nailing',
                'deepthroating', 'gagging', 'choking', 'swallowing', 'cumming', 'orgasming',
                'moaning', 'screaming', 'begging', 'submitting', 'dominating', 'controlling',
                'spanking', 'slapping', 'grabbing', 'squeezing', 'pinching', 'biting',
                'licking', 'kissing', 'caressing', 'fondling', 'groping', 'stroking'
            ],
            'body_parts': [
                'cock', 'dick', 'penis', 'shaft', 'head', 'balls', 'testicles',
                'pussy', 'cunt', 'vagina', 'clit', 'lips', 'hole', 'slit',
                'ass', 'asshole', 'anus', 'butt', 'cheeks', 'crack',
                'tits', 'boobs', 'breasts', 'nipples', 'areolas',
                'mouth', 'throat', 'tongue', 'lips', 'face',
                'legs', 'thighs', 'hips', 'waist', 'stomach', 'back'
            ],
            'positions': [
                'doggy style', 'missionary', 'cowgirl', 'reverse cowgirl', 'sideways',
                'standing', 'bent over', 'on top', 'underneath', 'from behind',
                'face down', 'legs up', 'spread eagle', 'on knees', 'squatting'
            ],
            'descriptors': [
                'hard', 'deep', 'rough', 'gentle', 'slow', 'fast', 'intense',
                'passionate', 'wild', 'aggressive', 'dominant', 'submissive',
                'wet', 'tight', 'loose', 'stretched', 'filled', 'stuffed',
                'big', 'huge', 'massive', 'thick', 'long', 'small', 'tiny',
                'hot', 'steamy', 'sweaty', 'dirty', 'nasty', 'kinky', 'perverted'
            ],
            'clothing': [
                'naked', 'nude', 'topless', 'bottomless', 'lingerie', 'underwear',
                'bra', 'panties', 'thong', 'stockings', 'fishnets', 'garters',
                'dress', 'skirt', 'shirt', 'pants', 'shorts', 'heels', 'boots'
            ]
        }
        
        # Euphemism replacements for explicit content
        self.replacements = {
            'having sex': 'fucking hard',
            'making love': 'fucking passionately',
            'intimate': 'fucking intensely',
            'together': 'fucking each other',
            'close': 'fucking deep',
            'touching': 'groping and fondling',
            'kissing': 'making out and touching',
            'embracing': 'grinding against each other',
            'lying': 'positioned for fucking',
            'sitting': 'riding and bouncing',
            'standing': 'fucking standing up',
            'on top': 'riding cock',
            'underneath': 'getting pounded',
            'behind': 'getting fucked from behind',
            'in front': 'sucking cock',
            'between': 'getting fucked by multiple cocks',
            'with': 'getting fucked by',
            'and': 'while getting fucked by',
            'woman': 'horny slut',
            'girl': 'sexy babe',
            'man': 'hung stud',
            'guy': 'big cock',
            'black': 'big black',
            'blonde': 'hot blonde'
        }
    
    def enhance_description(self, basic_description: str, frame_number: int, total_frames: int) -> str:
        """
        Enhance a basic description with explicit adult content details.
        
        Args:
            basic_description: Basic BLIP-generated description
            frame_number: Current frame number
            total_frames: Total frames for context
            
        Returns:
            Enhanced explicit description
        """
        if not basic_description or basic_description == "Error generating caption":
            return f"Frame {frame_number}: Explicit sexual activity in progress"
        
        # Convert to lowercase for processing
        enhanced = basic_description.lower()
        
        # Replace euphemisms with explicit terms
        for old, new in self.replacements.items():
            enhanced = enhanced.replace(old, new)
        
        # Add position and intensity based on frame progression
        progress = frame_number / total_frames
        
        if progress < 0.2:  # Beginning - setup/foreplay
            intensity_words = ['starting to', 'beginning to', 'getting ready for']
            position_context = 'in the opening scene'
        elif progress < 0.5:  # Early action
            intensity_words = ['actively', 'intensely', 'passionately']
            position_context = 'as the action heats up'
        elif progress < 0.8:  # Peak action
            intensity_words = ['aggressively', 'roughly', 'wildly', 'frantically']
            position_context = 'in the intense main action'
        else:  # Climax/ending
            intensity_words = ['desperately', 'climactically', 'explosively']
            position_context = 'approaching the climactic finale'
        
        # Add explicit details based on common adult video patterns
        if 'fucking' in enhanced and 'hard' not in enhanced:
            enhanced += ' with deep, powerful thrusts'
        if 'sucking' in enhanced:
            enhanced += ' taking it deep in her throat'
        if 'riding' in enhanced:
            enhanced += ' bouncing up and down on the thick shaft'
        if 'cock' in enhanced:
            enhanced += ' stretching her tight holes'
        if 'pussy' in enhanced:
            enhanced += ' getting pounded relentlessly'
        
        # Add contextual enhancement
        enhanced = f"{enhanced} {position_context}"
        
        return enhanced.capitalize()
    
    def extract_explicit_tags(self, enhanced_description: str, basic_description: str) -> List[str]:
        """
        Extract explicit tags from both enhanced and basic descriptions.
        
        Args:
            enhanced_description: Enhanced explicit description
            basic_description: Basic BLIP description
            
        Returns:
            List of explicit tags
        """
        tags = set()
        
        # Extract from both descriptions
        all_text = f"{enhanced_description} {basic_description}".lower()
        
        # Add vocabulary-based tags
        for category, words in self.adult_vocabulary.items():
            for word in words:
                if word in all_text:
                    tags.add(word)
        
        # Add basic descriptive tags
        basic_tags = all_text.replace(',', ' ').replace('.', ' ').split()
        for tag in basic_tags:
            if len(tag) > 2 and tag.isalpha():  # Only alphabetic tags longer than 2 chars
                tags.add(tag)
        
        # Remove common stop words but keep adult-relevant terms
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an'}
        
        filtered_tags = [tag for tag in tags if tag not in stop_words and len(tag) > 2]
        
        return sorted(list(set(filtered_tags)))
    
    def generate_narrative_context(self, frame_number: int, total_frames: int, description: str) -> str:
        """
        Generate narrative context for the frame within the video story.
        
        Args:
            frame_number: Current frame number
            total_frames: Total frames
            description: Frame description
            
        Returns:
            Narrative context string
        """
        progress = frame_number / total_frames
        timestamp = (frame_number - 1) * 10  # Assuming 10-second intervals
        
        if progress < 0.1:
            context = f"Opening scene ({timestamp}s): {description}"
        elif progress < 0.3:
            context = f"Building intensity ({timestamp}s): {description}"
        elif progress < 0.7:
            context = f"Peak action ({timestamp}s): {description}"
        elif progress < 0.9:
            context = f"Climactic moments ({timestamp}s): {description}"
        else:
            context = f"Finale ({timestamp}s): {description}"
        
        return context
    
    def enhance_existing_analysis(self, ai_embeddings_file: Path) -> Dict[str, Any]:
        """
        Enhance existing AI analysis with explicit descriptions.
        
        Args:
            ai_embeddings_file: Path to existing ai_embeddings.json file
            
        Returns:
            Enhanced analysis results
        """
        # Load existing analysis
        try:
            with open(ai_embeddings_file, 'r') as f:
                existing_data = json.load(f)
        except Exception as e:
            logger.error(f"Failed to load existing analysis: {e}")
            return {}
        
        if not existing_data:
            logger.error("No existing analysis data found")
            return {}
        
        total_frames = len(existing_data)
        logger.info(f"Enhancing {total_frames} existing frame analyses...")
        
        # Enhance each frame
        enhanced_frames = []
        for i, frame_data in enumerate(existing_data, 1):
            basic_description = frame_data.get('description', '')
            
            # Generate enhanced description
            enhanced_description = self.enhance_description(basic_description, i, total_frames)
            
            # Extract explicit tags
            explicit_tags = self.extract_explicit_tags(enhanced_description, basic_description)
            
            # Generate narrative context
            narrative_context = self.generate_narrative_context(i, total_frames, enhanced_description)
            
            # Create enhanced frame data
            enhanced_frame = {
                'frame_number': i,
                'timestamp': f"{(i-1) * 10}s",
                'basic_description': basic_description,
                'enhanced_description': enhanced_description,
                'narrative_context': narrative_context,
                'explicit_tags': explicit_tags,
                'auto_tags': frame_data.get('auto_tags', []),
                'embedding': frame_data.get('embedding', []),
                'success': frame_data.get('success', True)
            }
            
            enhanced_frames.append(enhanced_frame)
            
            if i % 50 == 0:
                logger.info(f"Enhanced {i}/{total_frames} frames...")
        
        # Generate comprehensive video narrative
        video_narrative = self._generate_video_narrative(enhanced_frames)
        
        # Generate detailed summary
        detailed_summary = self._generate_detailed_summary(enhanced_frames)
        
        return {
            'enhanced_frames': enhanced_frames,
            'video_narrative': video_narrative,
            'detailed_summary': detailed_summary,
            'total_frames': total_frames,
            'successful_frames': sum(1 for f in enhanced_frames if f.get('success', False))
        }
    
    def _generate_video_narrative(self, enhanced_frames: List[Dict]) -> str:
        """Generate a comprehensive narrative of the entire video."""
        successful_frames = [f for f in enhanced_frames if f.get('success', False)]
        
        if not successful_frames:
            return "No successful frame analyses to generate narrative."
        
        # Group frames by story progression
        total_frames = len(successful_frames)
        
        opening = successful_frames[:total_frames//4] if total_frames > 4 else successful_frames[:1]
        building = successful_frames[total_frames//4:total_frames//2] if total_frames > 4 else []
        peak = successful_frames[total_frames//2:3*total_frames//4] if total_frames > 4 else []
        climax = successful_frames[3*total_frames//4:] if total_frames > 4 else []
        
        narrative_parts = []
        
        if opening:
            narrative_parts.append(f"OPENING: {opening[0].get('enhanced_description', 'Scene begins')}")
        
        if building:
            mid_building = building[len(building)//2]
            narrative_parts.append(f"BUILDING INTENSITY: {mid_building.get('enhanced_description', 'Action intensifies')}")
        
        if peak:
            mid_peak = peak[len(peak)//2]
            narrative_parts.append(f"PEAK ACTION: {mid_peak.get('enhanced_description', 'Intense action')}")
        
        if climax:
            narrative_parts.append(f"CLIMAX: {climax[-1].get('enhanced_description', 'Scene concludes')}")
        
        return " | ".join(narrative_parts)
    
    def _generate_detailed_summary(self, enhanced_frames: List[Dict]) -> Dict[str, Any]:
        """Generate detailed summary with explicit content analysis."""
        successful_frames = [f for f in enhanced_frames if f.get('success', False)]
        
        # Collect all explicit tags
        all_explicit_tags = []
        for frame in successful_frames:
            all_explicit_tags.extend(frame.get('explicit_tags', []))
        
        # Count tag frequency
        tag_counts = {}
        for tag in all_explicit_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # Sort by frequency
        dominant_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)
        
        # Generate explicit summary
        explicit_summary = self._generate_explicit_summary(successful_frames, dominant_tags)
        
        return {
            'explicit_summary': explicit_summary,
            'dominant_explicit_tags': [tag for tag, count in dominant_tags[:20]],
            'tag_frequencies': dict(dominant_tags[:20]),
            'total_unique_tags': len(tag_counts),
            'frame_count': len(successful_frames),
            'narrative_flow': [f.get('narrative_context', '') for f in successful_frames[:10]]  # First 10 for overview
        }
    
    def _generate_explicit_summary(self, frames: List[Dict], dominant_tags: List[tuple]) -> str:
        """Generate explicit summary of video content."""
        if not frames:
            return "No content to summarize."
        
        # Get most common explicit terms
        explicit_actions = [tag for tag, count in dominant_tags if tag in self.adult_vocabulary['actions']][:5]
        body_parts = [tag for tag, count in dominant_tags if tag in self.adult_vocabulary['body_parts']][:5]
        positions = [tag for tag, count in dominant_tags if tag in self.adult_vocabulary['positions']][:3]
        
        # Build explicit summary
        summary_parts = []
        
        if explicit_actions:
            summary_parts.append(f"Primary sexual activities: {', '.join(explicit_actions)}")
        
        if body_parts:
            summary_parts.append(f"Focus on: {', '.join(body_parts)}")
        
        if positions:
            summary_parts.append(f"Positions featured: {', '.join(positions)}")
        
        # Add narrative flow
        if len(frames) > 10:
            beginning = frames[0].get('enhanced_description', '')
            middle = frames[len(frames)//2].get('enhanced_description', '')
            end = frames[-1].get('enhanced_description', '')
            
            summary_parts.append(f"Story progression: Begins with {beginning[:100]}..., intensifies to {middle[:100]}..., concludes with {end[:100]}...")
        
        return " | ".join(summary_parts)

def main():
    """Main function to enhance existing adult content analysis."""
    
    # Default video directory
    video_dir = Path("data/xvideos/index/White_girl_Creampie_ubhotuk9e37")
    ai_embeddings_file = video_dir / "screenshots" / "ai_embeddings.json"
    
    if not ai_embeddings_file.exists():
        logger.error(f"AI embeddings file not found: {ai_embeddings_file}")
        return 1
    
    logger.info(f"Enhancing existing analysis for: {video_dir}")
    
    # Initialize enhancer
    enhancer = ExistingAnalysisEnhancer()
    
    # Enhance existing analysis
    results = enhancer.enhance_existing_analysis(ai_embeddings_file)
    
    if not results:
        logger.error("Enhancement failed")
        return 1
    
    # Save enhanced results
    enhanced_results_file = video_dir / "enhanced_explicit_analysis.json"
    try:
        with open(enhanced_results_file, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Enhanced analysis saved to: {enhanced_results_file}")
    except Exception as e:
        logger.error(f"Failed to save enhanced results: {e}")
        return 1
    
    # Print summary
    summary = results.get('detailed_summary', {})
    logger.info(f"Enhancement complete:")
    logger.info(f"  Total frames enhanced: {results.get('total_frames', 0)}")
    logger.info(f"  Successful enhancements: {results.get('successful_frames', 0)}")
    logger.info(f"  Unique explicit tags: {summary.get('total_unique_tags', 0)}")
    logger.info(f"  Explicit summary: {summary.get('explicit_summary', 'N/A')[:200]}...")
    
    return 0

if __name__ == "__main__":
    exit(main())
