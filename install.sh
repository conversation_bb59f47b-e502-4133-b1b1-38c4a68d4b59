#!/bin/bash

# Video Search Indexer - One-Click Installation Script
# This script sets up the entire video indexing system with AI capabilities

set -e  # Exit on any error

echo "🚀 Video Search Indexer - One-Click Installation"
echo "=================================================="

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    echo "Please install Python 3.8+ and try again"
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is required but not installed"
    echo "Please install pip and try again"
    exit 1
fi

echo "✅ pip3 found"

# Run the Python setup script
echo ""
echo "🔧 Running automated setup..."
python3 setup.py

echo ""
echo "🎉 Installation complete!"
echo ""
echo "📖 Quick Start Guide:"
echo "1. Add a video URL:"
echo "   python3 -m src.cli add-url 'https://youtube.com/watch?v=dQw4w9WgXcQ'"
echo ""
echo "2. Process the video (extracts screenshots + AI analysis):"
echo "   python3 -m src.cli process"
echo ""
echo "3. Search with AI:"
echo "   python3 -m src.cli ai-search 'person dancing'"
echo ""
echo "4. View all indexed videos:"
echo "   python3 -m src.cli list"
echo ""
echo "📚 For more information, see README.md"
