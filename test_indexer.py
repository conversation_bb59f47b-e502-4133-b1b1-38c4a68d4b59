"""
Simple test script for the Video Search Indexer

This script tests basic functionality without requiring actual video downloads.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.url_manager import URLManager
from src.metadata_extractor import MetadataExtractor
from src.file_organizer import FileOrganizer


def test_url_manager():
    """Test URL manager functionality."""
    print("🧪 Testing URL Manager...")
    
    # Create temporary URL manager
    url_manager = URLManager(storage_path="test_urls.json")
    
    # Test URL validation
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://vimeo.com/123456789",
        "https://invalid-url",
        "https://unsupported-site.com/video"
    ]
    
    for url in test_urls:
        result = url_manager.validate_url(url)
        print(f"   {url}: {'✅' if result['valid'] else '❌'}")
        if not result['valid']:
            print(f"      Error: {result['error']}")
    
    # Test adding valid URL
    result = url_manager.add_url("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    if result['success']:
        print("   ✅ Successfully added YouTube URL")
        print(f"      Platform: {result['platform']}")
        print(f"      Domain: {result['domain']}")
    else:
        print(f"   ❌ Failed to add URL: {result['error']}")
    
    # Test statistics
    stats = url_manager.get_statistics()
    print(f"   📊 Total URLs: {stats['total_urls']}")
    
    # Cleanup
    Path("test_urls.json").unlink(missing_ok=True)
    print("✅ URL Manager tests completed")


def test_file_organizer():
    """Test file organizer functionality."""
    print("\n🧪 Testing File Organizer...")
    
    file_organizer = FileOrganizer(base_data_dir="test_data")
    
    # Test directory creation
    dir_info = file_organizer.create_video_directory(
        domain="youtube",
        video_title="Test Video Title!@#$%",
        video_id="test123"
    )
    
    print(f"   📁 Created directory: {dir_info['video_dir']}")
    print(f"   📁 Folder name: {dir_info['folder_name']}")
    
    # Test metadata saving
    test_metadata = {
        'title': 'Test Video',
        'description': 'This is a test video',
        'platform': 'youtube',
        'duration_seconds': 180,
        'uploader': 'Test Channel'
    }
    
    metadata_file = file_organizer.save_metadata(dir_info['video_dir'], test_metadata)
    print(f"   💾 Saved metadata: {metadata_file}")
    
    # Test directory info
    info = file_organizer.get_video_directory_info("youtube", "Test Video Title!@#$%", "test123")
    print(f"   ℹ️  Directory exists: {info['exists']}")
    
    # Cleanup
    import shutil
    shutil.rmtree("test_data", ignore_errors=True)
    print("✅ File Organizer tests completed")


def test_domain_extraction():
    """Test domain extraction functionality."""
    print("\n🧪 Testing Domain Extraction...")
    
    url_manager = URLManager()
    
    test_cases = [
        ("https://www.youtube.com/watch?v=123", "youtube"),
        ("https://vimeo.com/123456", "vimeo"),
        ("https://player.vimeo.com/video/123", "vimeo"),
        ("https://dai.ly/abc123", "dailymotion"),
    ]
    
    for url, expected_domain in test_cases:
        actual_domain = url_manager.extract_domain(url)
        status = "✅" if actual_domain == expected_domain else "❌"
        print(f"   {status} {url} -> {actual_domain} (expected: {expected_domain})")


def main():
    """Run all tests."""
    print("🚀 Running Video Search Indexer Tests")
    print("=" * 50)
    
    try:
        test_url_manager()
        test_file_organizer()
        test_domain_extraction()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("\nThe indexer components are working correctly.")
        print("You can now try adding real video URLs:")
        print("  python -m src add-url 'https://youtube.com/watch?v=example'")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
