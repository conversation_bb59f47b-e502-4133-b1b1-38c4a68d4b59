#!/usr/bin/env python3
"""
Test script to compare serial vs parallel AI processing performance
"""

import sys
import os
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from parallel_ai_analyzer import ParallelAIAnalyzer
from video_processor import VideoProcessor

def test_parallel_vs_serial():
    """Compare parallel vs serial AI processing performance."""
    
    print("=== Parallel vs Serial AI Processing Comparison ===")
    
    # Find a video with many screenshots for testing
    video_base_dir = Path('data/youtube/index')
    test_video = None
    max_screenshots = 0
    
    for video_dir in video_base_dir.iterdir():
        if video_dir.is_dir():
            screenshots_dir = video_dir / 'screenshots'
            if screenshots_dir.exists():
                screenshot_count = len(list(screenshots_dir.glob('frame_*.jpg')))
                if screenshot_count > max_screenshots:
                    max_screenshots = screenshot_count
                    test_video = video_dir
    
    if not test_video:
        print("❌ No test videos found")
        return
    
    screenshots_dir = test_video / 'screenshots'
    screenshot_files = list(screenshots_dir.glob('frame_*.jpg'))
    
    print(f"📹 Test video: {test_video.name}")
    print(f"📸 Screenshots: {len(screenshot_files)}")
    
    # Limit to reasonable number for testing
    if len(screenshot_files) > 50:
        screenshot_files = screenshot_files[:50]
        print(f"📸 Limited to: {len(screenshot_files)} screenshots for testing")
    
    # Test 1: Serial Processing (current method)
    print(f"\n🔄 Testing Serial Processing...")
    processor = VideoProcessor()
    
    start_time = time.time()
    serial_result = processor.analyze_screenshots_with_ai(screenshots_dir)
    serial_time = time.time() - start_time
    
    if serial_result.get('success'):
        print(f"✅ Serial processing completed in {serial_time:.2f}s")
        print(f"   - Rate: {len(screenshot_files) / serial_time:.2f} frames/second")
        print(f"   - Descriptions: {len(serial_result.get('descriptions', {}))}")
    else:
        print(f"❌ Serial processing failed: {serial_result.get('error')}")
        return
    
    # Test 2: Parallel Processing
    print(f"\n⚡ Testing Parallel Processing...")
    parallel_analyzer = ParallelAIAnalyzer(max_workers=2)  # Conservative for testing
    
    start_time = time.time()
    parallel_result = parallel_analyzer.analyze_screenshots_parallel(screenshots_dir)
    parallel_time = time.time() - start_time
    
    if parallel_result.get('success'):
        print(f"✅ Parallel processing completed in {parallel_time:.2f}s")
        print(f"   - Rate: {parallel_result.get('frames_per_second', 0):.2f} frames/second")
        print(f"   - Workers used: {parallel_result.get('workers_used', 0)}")
        print(f"   - Results: {len(parallel_result.get('results', []))}")
    else:
        print(f"❌ Parallel processing failed: {parallel_result.get('error')}")
        return
    
    # Performance Comparison
    print(f"\n📊 Performance Comparison:")
    print(f"   - Serial time: {serial_time:.2f}s")
    print(f"   - Parallel time: {parallel_time:.2f}s")
    
    if parallel_time > 0:
        speedup = serial_time / parallel_time
        print(f"   - Speedup: {speedup:.2f}x")
        
        if speedup > 1.2:
            print(f"   🚀 Parallel processing is {speedup:.1f}x faster!")
        elif speedup > 0.8:
            print(f"   ⚖️  Similar performance (overhead vs parallelism)")
        else:
            print(f"   🐌 Serial processing is faster (parallel overhead too high)")
    
    # Resource Usage Recommendations
    print(f"\n💡 Optimization Recommendations:")
    
    # Calculate optimal configuration
    try:
        import psutil
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        cpu_cores = psutil.cpu_count()
        
        # Each AI instance needs ~2.2GB
        max_memory_workers = int(available_memory_gb / 2.5)
        max_cpu_workers = max(1, cpu_cores - 1)
        
        optimal_workers = min(max_memory_workers, max_cpu_workers, 4)
        
        print(f"   - System: {available_memory_gb:.1f}GB RAM, {cpu_cores} CPU cores")
        print(f"   - Max workers by memory: {max_memory_workers}")
        print(f"   - Max workers by CPU: {max_cpu_workers}")
        print(f"   - Recommended workers: {optimal_workers}")
        
        # Estimate processing capacity
        if parallel_result.get('frames_per_second', 0) > 0:
            estimated_rate = parallel_result['frames_per_second'] * (optimal_workers / parallel_result.get('workers_used', 1))
            print(f"   - Estimated rate with {optimal_workers} workers: {estimated_rate:.1f} frames/second")
            
            # Time estimates for different video lengths
            print(f"\n⏱️  Processing Time Estimates (with {optimal_workers} workers):")
            for minutes in [5, 10, 30, 60]:
                frames = minutes * 60 / 5  # Assuming 5-second intervals
                processing_time = frames / estimated_rate
                print(f"   - {minutes}-minute video (~{frames:.0f} frames): {processing_time:.1f} seconds")
        
    except ImportError:
        print(f"   - Install 'psutil' for detailed system analysis")
    
    # Configuration recommendations
    print(f"\n⚙️  Configuration Recommendations:")
    print(f"   - For videos < 100 frames: Use serial processing (less overhead)")
    print(f"   - For videos > 100 frames: Use parallel processing")
    print(f"   - Optimal batch size: 20-50 frames per worker")
    print(f"   - Memory per worker: ~2.2GB")
    print(f"   - Consider GPU acceleration for even better performance")

if __name__ == '__main__':
    test_parallel_ai()
