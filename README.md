# Video Search Indexer 🎬🤖

A local video indexing tool with **AI-powered visual search** that extracts metadata, thumbnails, and screenshots from video URLs for intelligent content discovery.

## ✨ Features

- **🔗 URL Management**: Add and organize video URLs by domain
- **📊 Metadata Extraction**: Extract titles, descriptions, thumbnails, and other video metadata
- **📁 Organized Storage**: Automatic folder structure creation by domain and video
- **📸 Screenshot Capture**: Extract video frames at regular intervals for AI analysis
- **🤖 AI-Powered Search**: CLIP-based semantic search using natural language queries
- **🏠 Local Processing**: Runs entirely on your local machine - no cloud dependencies
- **🔍 Visual Content Discovery**: Find specific moments in videos using descriptions like "person dancing" or "microphone"

## 🚀 Quick Start (One-Click Install)

### Option 1: Bash <PERSON>ript (Linux/macOS)
```bash
git clone <repository-url>
cd vid_search
./install.sh
```

### Option 2: Python Setup (All Platforms)
```bash
git clone <repository-url>
cd vid_search
python3 setup.py
```

Both methods will:
- ✅ Install all Python dependencies (including AI libraries ~605MB)
- ✅ Check for FFmpeg installation
- ✅ Test AI functionality (CLIP model)
- ✅ Create necessary directories
- ✅ Verify everything is working

### Manual FFmpeg Installation (if needed):
- **macOS**: `brew install ffmpeg`
- **Ubuntu/Debian**: `sudo apt install ffmpeg`
- **Windows**: Download from https://ffmpeg.org/download.html

### Test the Installation:
```bash
python3 -m src.cli add-url "https://youtube.com/watch?v=dQw4w9WgXcQ"
python3 -m src.cli process
python3 -m src.cli ai-search "person dancing"
```

## 💻 Usage

### 🌐 Web Interface (Recommended)

Start the modern web interface for easy visual management:

```bash
# Start web interface (opens browser automatically)
python run_web.py

# Or use the CLI command
python -m src.cli web

# Custom host/port
python run_web.py --host 0.0.0.0 --port 8080

# Debug mode
python run_web.py --debug
```

The web interface provides:
- 📊 **Dashboard** with statistics and recent videos
- 🎬 **Video Gallery** with search and filtering
- 🤖 **AI Visual Search** for finding scenes by description
- ➕ **Add Videos** through a simple form
- 🔍 **Detailed Video Views** with screenshot galleries

### 💻 Command Line Interface

### Basic Commands
```bash
# Add a video URL to index
python -m src.cli add-url "https://youtube.com/watch?v=dQw4w9WgXcQ"

# Process all pending URLs (extracts screenshots + runs AI analysis)
python -m src.cli process

# View indexed content
python -m src.cli list

# Get statistics
python -m src.cli stats
```

### 🤖 AI-Powered Search
```bash
# Search for visual content using natural language
python -m src.cli ai-search "person dancing"
python -m src.cli ai-search "man singing"
python -m src.cli ai-search "microphone"
python -m src.cli ai-search "outdoor scene"
python -m src.cli ai-search "close-up face"

# Traditional text search (searches metadata)
python -m src.cli search "Rick Astley"
```

### 🔍 Search Examples
The AI search can find specific visual content:
- **"person dancing"** → Finds frames with people dancing
- **"microphone"** → Finds frames with microphones visible
- **"outdoor scene"** → Finds outdoor/nature scenes
- **"close-up"** → Finds close-up shots
- **"crowd"** → Finds scenes with audiences/crowds

## 📁 Data Structure

The indexer organizes content in the following structure:
```
data/
├── youtube/
│   └── index/
│       ├── video_title_1/
│       │   ├── thumbnail.jpg
│       │   ├── metadata.txt
│       │   ├── metadata.json
│       │   ├── ai_analysis.json          # 🆕 AI analysis results
│       │   └── screenshots/
│       │       ├── frame_00005.jpg
│       │       ├── frame_00010.jpg
│       │       ├── ai_embeddings.json    # 🆕 CLIP embeddings
│       │       └── ...
│       └── video_title_2/
├── vimeo/
└── ...
```

## ⚙️ Configuration

Edit `config/settings.yaml` to customize:
- Screenshot interval (default: 5 seconds)
- AI analysis settings (`enable_ai_analysis: true`)
- Supported video platforms
- Storage locations
- Processing options

## 🤖 AI Technology

### Current AI Features (Built-in)
- **✅ CLIP (OpenAI ViT-B-32)**: For semantic image-text similarity and visual search
- **✅ Automatic Content Tagging**: Extracts semantic tags from screenshots
- **✅ Natural Language Search**: Find content using descriptive queries
- **✅ Local Processing**: All AI runs on your machine, no cloud APIs needed

### Technical Details
- **Model Size**: ~605MB download (cached locally)
- **Performance**: Processes ~43 screenshots in seconds on CPU
- **Storage**: Embeddings saved as JSON alongside screenshots
- **Search**: Cosine similarity matching with configurable thresholds

### For Existing Videos
If you have videos indexed before AI was enabled:
```bash
python analyze_existing_screenshots.py
```

## 📋 Requirements

- **Python 3.8+**
- **FFmpeg** (for video processing)
- **2GB+ free disk space** (varies by content)
- **Internet connection** for initial video download and AI model download
- **Optional**: CUDA-compatible GPU for faster AI processing (CPU works fine)
