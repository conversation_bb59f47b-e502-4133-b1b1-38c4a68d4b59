Defaulting to user installation because normal site-packages is not writeable
Collecting Flask
  Downloading flask-3.1.1-py3-none-any.whl (103 kB)
Collecting Werkzeug
  Downloading werkzeug-3.1.3-py3-none-any.whl (224 kB)
Collecting itsdangerous>=2.2.0
  Downloading itsdangerous-2.2.0-py3-none-any.whl (16 kB)
Requirement already satisfied: jinja2>=3.1.2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from Flask) (3.1.6)
Requirement already satisfied: importlib-metadata>=3.6.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from Flask) (8.7.0)
Requirement already satisfied: markupsafe>=2.1.1 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from Flask) (3.0.2)
Collecting blinker>=1.9.0
  Downloading blinker-1.9.0-py3-none-any.whl (8.5 kB)
Requirement already satisfied: click>=8.1.3 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from Flask) (8.1.8)
Requirement already satisfied: zipp>=3.20 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from importlib-metadata>=3.6.0->Flask) (3.23.0)
Installing collected packages: Werkzeug, itsdangerous, blinker, Flask
Successfully installed Flask-3.1.1 Werkzeug-3.1.3 blinker-1.9.0 itsdangerous-2.2.0
