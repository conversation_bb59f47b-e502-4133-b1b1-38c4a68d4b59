#!/usr/bin/env python3
"""
Test script to demonstrate hybrid search capabilities including adult content handling
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from storage import VideoStorage
from ai_analyzer import CLIPAnalyzer
from hybrid_search import HybridSearchEngine, QueryExpander

def test_query_expansion():
    """Test query expansion with various types of content."""
    
    print("=== Query Expansion Testing ===")
    
    expander = QueryExpander()
    
    # Test queries including adult content (uncensored)
    test_queries = [
        "dancing woman",
        "naked person",
        "sex scene", 
        "beautiful girl",
        "man running",
        "hot bikini",
        "kissing couple",
        "bedroom scene",
        "shower video",
        "party dancing",
        "workout fitness",
        "cooking food",
        "funny cat",
        "car driving"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        expansion = expander.expand_query(query)
        
        print(f"   📝 Original terms: {expansion['original_terms']}")
        print(f"   🔄 Expanded terms: {expansion['expanded_terms'][:10]}")  # Show first 10
        print(f"   📋 Expanded queries:")
        for i, expanded_query in enumerate(expansion['expanded_queries'][:5], 1):
            print(f"      {i}. {expanded_query}")

def test_hybrid_search():
    """Test hybrid search functionality."""
    
    print("\n=== Hybrid Search Testing ===")
    
    try:
        # Initialize components
        storage = VideoStorage()
        ai_analyzer = CLIPAnalyzer()
        hybrid_search = HybridSearchEngine(storage, ai_analyzer)
        
        print(f"✅ Hybrid search engine initialized")
        print(f"   - AI available: {ai_analyzer.is_available()}")
        
        # Test queries (including adult content)
        test_queries = [
            "person dancing",
            "woman singing", 
            "man playing guitar",
            "people laughing",
            "beautiful scenery",
            "hot performance",
            "sexy dance",
            "intimate scene",
            "naked body",
            "adult content",
            "funny moment",
            "exciting action",
            "romantic kiss",
            "party atmosphere"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing: '{query}'")
            
            try:
                result = hybrid_search.search(query, limit=5)
                
                if result['success']:
                    print(f"   ✅ Found {len(result['results'])} results")
                    
                    # Show search metadata
                    metadata = result.get('metadata', {})
                    methods_used = metadata.get('search_methods_used', [])
                    print(f"   🔧 Search methods used: {', '.join(methods_used)}")
                    
                    # Show expansion info
                    expansion_data = metadata.get('expansion_data', {})
                    if expansion_data.get('expanded_terms'):
                        expanded_terms = expansion_data['expanded_terms'][:5]  # First 5
                        print(f"   🔄 Expanded with: {', '.join(expanded_terms)}")
                    
                    # Show top results
                    for i, video in enumerate(result['results'][:3], 1):
                        score = video.get('hybrid_score', 0)
                        methods = video.get('search_methods', [])
                        title = video.get('title', 'Unknown')[:50]
                        print(f"   {i}. {title} (score: {score:.3f}, methods: {', '.join(methods)})")
                        
                else:
                    print(f"   ❌ Search failed: {result.get('error')}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
    except Exception as e:
        print(f"❌ Failed to initialize hybrid search: {e}")

def test_adult_content_handling():
    """Test that the system handles adult content without filtering."""
    
    print("\n=== Adult Content Handling Test ===")
    
    expander = QueryExpander()
    
    # Explicit adult content queries (uncensored)
    adult_queries = [
        "naked woman",
        "sex video", 
        "porn scene",
        "fucking couple",
        "masturbation video",
        "orgasm scene",
        "blowjob video",
        "anal sex",
        "lesbian action",
        "gay sex",
        "BDSM scene",
        "fetish content"
    ]
    
    print("Testing that adult content queries are processed without censorship:")
    
    for query in adult_queries:
        expansion = expander.expand_query(query)
        
        # Check that terms are expanded, not filtered
        has_expansions = len(expansion['expanded_terms']) > 0
        has_expanded_queries = len(expansion['expanded_queries']) > 1
        
        status = "✅ PROCESSED" if (has_expansions or has_expanded_queries) else "⚠️  LIMITED"
        
        print(f"   '{query}': {status}")
        if has_expansions:
            print(f"      → Expanded to: {', '.join(expansion['expanded_terms'][:5])}")
    
    print(f"\n💡 Key Points:")
    print(f"   - No content filtering or censorship applied")
    print(f"   - Adult terms are expanded like any other content")
    print(f"   - System treats all content types equally")
    print(f"   - Suitable for indexing any type of video content")

def test_search_comparison():
    """Compare literal vs hybrid search results."""
    
    print("\n=== Search Method Comparison ===")
    
    try:
        storage = VideoStorage()
        ai_analyzer = CLIPAnalyzer()
        hybrid_search = HybridSearchEngine(storage, ai_analyzer)
        
        test_query = "person dancing"
        
        print(f"Comparing search methods for: '{test_query}'")
        
        # 1. Literal search only
        literal_results = storage.search_videos(test_query, limit=10)
        print(f"\n📝 Literal search: {len(literal_results)} results")
        for i, video in enumerate(literal_results[:3], 1):
            print(f"   {i}. {video.get('title', 'Unknown')[:50]}")
        
        # 2. Semantic search only (if available)
        if ai_analyzer.is_available():
            query_embedding = ai_analyzer.encode_text(test_query)
            if query_embedding:
                semantic_results = storage.search_videos_by_embedding(query_embedding, limit=10)
                print(f"\n🧠 Semantic search: {len(semantic_results)} results")
                for i, video in enumerate(semantic_results[:3], 1):
                    similarity = video.get('similarity', 0)
                    print(f"   {i}. {video.get('title', 'Unknown')[:50]} (sim: {similarity:.3f})")
        
        # 3. Hybrid search
        hybrid_result = hybrid_search.search(test_query, limit=10)
        if hybrid_result['success']:
            print(f"\n🔄 Hybrid search: {len(hybrid_result['results'])} results")
            for i, video in enumerate(hybrid_result['results'][:3], 1):
                score = video.get('hybrid_score', 0)
                methods = video.get('search_methods', [])
                print(f"   {i}. {video.get('title', 'Unknown')[:50]} (score: {score:.3f}, methods: {', '.join(methods)})")
        
        print(f"\n💡 Hybrid search combines:")
        print(f"   - Literal text matching (exact terms)")
        print(f"   - Expanded query matching (related terms)")
        print(f"   - Semantic similarity (conceptual matching)")
        print(f"   - Intelligent result ranking and deduplication")
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")

if __name__ == '__main__':
    print("🚀 Testing Hybrid Search System")
    print("=" * 50)
    
    test_query_expansion()
    test_hybrid_search()
    test_adult_content_handling()
    test_search_comparison()
    
    print(f"\n✅ Testing complete!")
    print(f"The hybrid search system provides:")
    print(f"   - Literal + semantic + expanded search")
    print(f"   - No content filtering or censorship")
    print(f"   - Intelligent result ranking")
    print(f"   - Suitable for all content types including adult material")
