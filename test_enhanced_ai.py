#!/usr/bin/env python3
"""
Test script to reprocess a video with enhanced AI analysis (BLIP descriptions + CLIP embeddings)
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from video_processor import VideoProcessor
from storage import VideoStorage
from ai_analyzer import CLIPAnalyzer

def test_enhanced_ai_analysis():
    """Test enhanced AI analysis on an existing video."""
    
    # Initialize components
    storage = VideoStorage()
    processor = VideoProcessor()
    
    # Get the first video from database
    videos = storage.search_videos('', limit=1)
    if not videos:
        print("No videos found in database")
        return
    
    video = videos[0]
    video_dir = Path(video['video_dir'])
    screenshots_dir = video_dir / 'screenshots'
    
    print(f"Testing enhanced AI analysis on: {video['title']}")
    print(f"Video directory: {video_dir}")
    print(f"Screenshots directory: {screenshots_dir}")
    
    if not screenshots_dir.exists():
        print(f"Screenshots directory not found: {screenshots_dir}")
        return
    
    # Count screenshots
    screenshot_files = list(screenshots_dir.glob('frame_*.jpg'))
    print(f"Found {len(screenshot_files)} screenshots")
    
    if len(screenshot_files) == 0:
        print("No screenshots found")
        return
    
    # Test enhanced AI analysis
    print("\n=== Testing Enhanced AI Analysis ===")
    
    try:
        # Run enhanced AI analysis
        ai_result = processor.analyze_screenshots_with_ai(screenshots_dir)
        
        if ai_result.get('success'):
            print(f"✅ Enhanced AI analysis successful!")
            print(f"   - Embeddings: {ai_result.get('embeddings_count', 0)}")
            print(f"   - Descriptions: {len(ai_result.get('descriptions', {}))}")
            print(f"   - Frame tags: {len(ai_result.get('frame_tags', {}))}")
            print(f"   - Captioner available: {ai_result.get('captioner_available', False)}")
            
            # Show video summary
            video_summary = ai_result.get('video_summary', {})
            if video_summary:
                print(f"\n📝 Video Summary:")
                print(f"   - Summary: {video_summary.get('summary', 'N/A')[:100]}...")
                print(f"   - Key themes: {video_summary.get('key_themes', [])[:5]}")
                print(f"   - Dominant tags: {video_summary.get('dominant_tags', [])[:10]}")
                print(f"   - Frame coverage: {video_summary.get('description_coverage', 'N/A')}")
            
            # Show sample descriptions
            descriptions = ai_result.get('descriptions', {})
            if descriptions:
                print(f"\n🖼️  Sample Frame Descriptions:")
                for i, (frame_name, desc) in enumerate(list(descriptions.items())[:3]):
                    print(f"   - {frame_name}: {desc}")
            
            # Show sample frame tags
            frame_tags = ai_result.get('frame_tags', {})
            if frame_tags:
                print(f"\n🏷️  Sample Frame Tags:")
                for i, (frame_name, tags) in enumerate(list(frame_tags.items())[:3]):
                    print(f"   - {frame_name}: {tags[:5]}")
            
            # Update database with enhanced data
            print(f"\n💾 Updating database with enhanced AI data...")
            
            # Get video metadata
            metadata_file = video_dir / 'metadata.json'
            if metadata_file.exists():
                import json
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # Store updated video with enhanced AI data
                video_id = storage.store_video(metadata, str(video_dir), len(screenshot_files))
                print(f"✅ Database updated successfully! Video ID: {video_id}")
                
                # Test search with enhanced data
                print(f"\n🔍 Testing search with enhanced data...")
                search_results = storage.search_videos('dancing', limit=3)
                for result in search_results:
                    print(f"   - {result['title'][:50]}...")
                    if result.get('video_summary'):
                        print(f"     Summary: {result['video_summary'][:80]}...")
                    if result.get('video_key_themes'):
                        print(f"     Themes: {result['video_key_themes'][:3]}")
            
        else:
            print(f"❌ Enhanced AI analysis failed: {ai_result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error during enhanced AI analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_enhanced_ai_analysis()
