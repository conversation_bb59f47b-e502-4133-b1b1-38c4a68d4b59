#!/usr/bin/env python3
"""
Test script to verify AI-powered search functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.storage import VideoStorage
from src.ai_analyzer import CL<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_ai_search():
    """Test AI-powered search using database-stored embeddings."""

    print("🔍 Testing AI-Powered Search")
    print("=" * 40)

    # Initialize components
    storage = VideoStorage("data/video_index.db")
    ai_analyzer = CLIPAnalyzer()
    
    if not ai_analyzer.is_available():
        print("❌ AI analyzer not available. Please install required dependencies.")
        return
    
    print("✅ AI analyzer initialized")
    
    # Test queries
    test_queries = [
        "music video",
        "person dancing", 
        "colorful scene",
        "outdoor setting",
        "performance"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing query: '{query}'")
        
        try:
            # Generate query embedding
            query_embedding = ai_analyzer.encode_text(query)
            if not query_embedding:
                print(f"   ❌ Failed to generate embedding for '{query}'")
                continue
            
            print(f"   ✅ Generated embedding (dim: {len(query_embedding)})")
            
            # Search using embeddings
            results = storage.search_videos_by_embedding(query_embedding, limit=3)
            print(f"   📊 Found {len(results)} results")
            
            for i, result in enumerate(results, 1):
                similarity = result.get('similarity', 0.0)
                print(f"   {i}. {result['title']} (similarity: {similarity:.3f})")
                
        except Exception as e:
            print(f"   ❌ Error searching for '{query}': {e}")
    
    # Test text search vs AI search comparison
    print(f"\n📊 Comparison: Text Search vs AI Search")
    print("-" * 50)
    
    comparison_query = "music"
    
    # Text search
    text_results = storage.search_videos(comparison_query, limit=3)
    print(f"Text search for '{comparison_query}': {len(text_results)} results")
    for result in text_results:
        print(f"   - {result['title']}")
    
    # AI search
    try:
        query_embedding = ai_analyzer.encode_text(comparison_query)
        if query_embedding:
            ai_results = storage.search_videos_by_embedding(query_embedding, limit=3)
            print(f"AI search for '{comparison_query}': {len(ai_results)} results")
            for result in ai_results:
                similarity = result.get('similarity', 0.0)
                print(f"   - {result['title']} (similarity: {similarity:.3f})")
    except Exception as e:
        print(f"AI search error: {e}")
    
    print("\n✅ AI Search Test Complete!")

if __name__ == "__main__":
    test_ai_search()
