#!/usr/bin/env python3
"""
<PERSON><PERSON>t to run AI analysis on existing screenshots.
This is useful for adding AI analysis to videos that were processed before AI was enabled.
"""

import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_analyzer import CLIPAnalyzer
from video_processor import VideoProcessor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_existing_screenshots():
    """Analyze existing screenshots with AI."""
    
    # Initialize AI analyzer
    analyzer = CLIPAnalyzer()
    if not analyzer.is_available():
        print("❌ AI analyzer not available. Install with: pip install torch open-clip-torch")
        return
    
    # Find all video directories with screenshots
    data_dir = Path("data")
    screenshot_dirs = []
    
    for domain_dir in data_dir.iterdir():
        if domain_dir.is_dir():
            index_dir = domain_dir / "index"
            if index_dir.exists():
                for video_dir in index_dir.iterdir():
                    if video_dir.is_dir():
                        screenshots_dir = video_dir / "screenshots"
                        if screenshots_dir.exists() and any(screenshots_dir.iterdir()):
                            screenshot_dirs.append(screenshots_dir)
    
    if not screenshot_dirs:
        print("❌ No screenshot directories found")
        return
    
    print(f"🔍 Found {len(screenshot_dirs)} video(s) with screenshots")
    
    # Process each directory
    for screenshots_dir in screenshot_dirs:
        video_name = screenshots_dir.parent.name
        print(f"\n🎬 Processing: {video_name}")
        
        # Check if AI analysis already exists
        embeddings_file = screenshots_dir / "ai_embeddings.json"
        if embeddings_file.exists():
            print(f"   ✅ AI analysis already exists, skipping")
            continue
        
        try:
            # Run AI analysis
            print(f"   🤖 Running AI analysis...")
            embeddings_data = analyzer.analyze_screenshots_batch(screenshots_dir)
            
            if not embeddings_data:
                print(f"   ❌ No screenshots found to analyze")
                continue
            
            # Get automatic tags for first few screenshots
            sample_screenshots = embeddings_data[:5]
            all_tags = {}
            
            for img_data in sample_screenshots:
                if img_data.get('success', False):
                    image_path = Path(img_data['image_path'])
                    
                    # Get relevance scores for common tags
                    from ai_analyzer import get_default_tags
                    tag_scores = analyzer.get_image_tags(image_path, get_default_tags())
                    
                    if not isinstance(tag_scores, dict) or 'error' in tag_scores:
                        continue
                    
                    # Keep tags with high relevance (>0.3)
                    relevant_tags = {
                        tag: score for tag, score in tag_scores.items() 
                        if score > 0.3
                    }
                    
                    all_tags[image_path.name] = relevant_tags
            
            # Save embeddings
            analyzer.save_embeddings(embeddings_data, embeddings_file)
            
            # Save AI analysis results
            ai_analysis = {
                "success": True,
                "embeddings_count": len(embeddings_data),
                "embeddings_file": str(embeddings_file),
                "embeddings": embeddings_data,
                "tags": all_tags,
                "analyzer_model": analyzer.model_name
            }
            
            ai_metadata_file = screenshots_dir.parent / "ai_analysis.json"
            import json
            with open(ai_metadata_file, 'w') as f:
                json.dump(ai_analysis, f, indent=2)
            
            print(f"   ✅ Analyzed {len(embeddings_data)} screenshots")
            print(f"   📁 Saved to: {embeddings_file}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            logger.error(f"Failed to analyze {video_name}: {e}")
    
    print(f"\n🎉 AI analysis complete!")

if __name__ == "__main__":
    analyze_existing_screenshots()
