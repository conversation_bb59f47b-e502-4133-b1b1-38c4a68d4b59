#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix AI analysis for already extracted screenshots.
This will reprocess the AI analysis with the corrected tag extraction code.
"""

import sys
import os
from pathlib import Path
import json
import logging

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_analyzer import CLIPAnalyzer
from storage import VideoStorage

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def reprocess_ai_analysis(video_dir: Path):
    """
    Reprocess AI analysis for a video directory with existing screenshots.
    
    Args:
        video_dir: Path to the video directory containing screenshots
    """
    screenshots_dir = video_dir / "screenshots"
    
    if not screenshots_dir.exists():
        logger.error(f"Screenshots directory not found: {screenshots_dir}")
        return False
    
    # Find all screenshot files
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_files = [
        f for f in screenshots_dir.iterdir()
        if f.is_file() and f.suffix.lower() in image_extensions
    ]
    
    if not image_files:
        logger.error(f"No image files found in: {screenshots_dir}")
        return False
    
    # Sort files for consistent ordering
    image_files.sort()
    logger.info(f"Found {len(image_files)} screenshots to process")
    
    # Initialize AI analyzer
    logger.info("Initializing AI analyzer...")
    analyzer = CLIPAnalyzer()
    
    if not analyzer.is_available():
        logger.error("AI analyzer not available")
        return False
    
    # Process all screenshots with enhanced analysis
    logger.info("Starting AI analysis with fixed tag extraction...")
    results = analyzer.analyze_screenshots_batch(screenshots_dir)
    
    if not results:
        logger.error("AI analysis failed")
        return False
    
    # Save results
    embeddings_file = screenshots_dir / "ai_embeddings.json"
    logger.info(f"Saving AI analysis results to: {embeddings_file}")
    
    try:
        with open(embeddings_file, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Successfully saved {len(results)} analysis results")
    except Exception as e:
        logger.error(f"Failed to save results: {e}")
        return False
    
    # Generate video summary
    logger.info("Generating video summary...")
    summary = analyzer.generate_video_summary(results)
    
    # Save summary
    summary_file = video_dir / "ai_summary.json"
    try:
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        logger.info(f"Saved video summary to: {summary_file}")
    except Exception as e:
        logger.error(f"Failed to save summary: {e}")
    
    # Print summary stats
    successful_frames = sum(1 for r in results if r.get('success', False))
    frames_with_descriptions = sum(1 for r in results if r.get('has_description', False))
    frames_with_tags = sum(1 for r in results if r.get('auto_tags'))
    
    logger.info(f"Analysis complete:")
    logger.info(f"  Total frames: {len(results)}")
    logger.info(f"  Successful: {successful_frames}")
    logger.info(f"  With descriptions: {frames_with_descriptions}")
    logger.info(f"  With auto tags: {frames_with_tags}")
    
    if summary:
        logger.info(f"  Video summary: {summary.get('summary', 'N/A')}")
        logger.info(f"  Dominant tags: {', '.join(summary.get('dominant_tags', [])[:5])}")
    
    return True

def main():
    """Main function to run the AI analysis fix."""
    
    # Default video directory (the xvideos video we're processing)
    video_dir = Path("data/xvideos/index/White_girl_Creampie_ubhotuk9e37")
    
    # Allow command line override
    if len(sys.argv) > 1:
        video_dir = Path(sys.argv[1])
    
    if not video_dir.exists():
        logger.error(f"Video directory not found: {video_dir}")
        return 1
    
    logger.info(f"Reprocessing AI analysis for: {video_dir}")
    
    success = reprocess_ai_analysis(video_dir)
    
    if success:
        logger.info("AI analysis reprocessing completed successfully!")
        
        # Try to store in database if storage is available
        try:
            logger.info("Attempting to store results in database...")
            storage = VideoStorage()
            
            # Load metadata
            metadata_file = video_dir / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # Load AI results
                embeddings_file = video_dir / "screenshots" / "ai_embeddings.json"
                with open(embeddings_file, 'r') as f:
                    ai_results = json.load(f)
                
                # Store in database
                video_id = storage.store_video_metadata(
                    url=metadata.get('url', ''),
                    title=metadata.get('title', ''),
                    description=metadata.get('description', ''),
                    duration=metadata.get('duration', 0),
                    platform=metadata.get('platform', ''),
                    channel=metadata.get('channel', ''),
                    upload_date=metadata.get('upload_date', ''),
                    view_count=metadata.get('view_count', 0),
                    thumbnail_path=str(video_dir / "thumbnail.jpg"),
                    video_path=str(video_dir),
                    ai_results=ai_results
                )
                
                logger.info(f"Successfully stored video in database with ID: {video_id}")
            else:
                logger.warning("Metadata file not found, skipping database storage")
                
        except Exception as e:
            logger.warning(f"Database storage failed (this is optional): {e}")
        
        return 0
    else:
        logger.error("AI analysis reprocessing failed!")
        return 1

if __name__ == "__main__":
    exit(main())
