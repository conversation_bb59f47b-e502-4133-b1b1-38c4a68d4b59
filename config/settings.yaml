# Video Search Indexer Configuration

# Storage settings
storage:
  data_directory: "data"
  max_storage_gb: 50  # Maximum storage usage in GB
  cleanup_old_files: true
  retention_days: 365

# Video processing settings
video:
  screenshot_interval: 5  # Seconds between screenshots
  max_video_duration: 3600  # Maximum video length to process (seconds)
  thumbnail_size: [320, 240]  # Width, height for thumbnails
  screenshot_size: [640, 480]  # Width, height for screenshots
  supported_formats: ["mp4", "webm", "mkv", "avi", "mov"]
  enable_ai_analysis: true  # Enable CLIP-based AI analysis of screenshots

# Scraping settings
scraping:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  request_timeout: 30  # Seconds
  retry_attempts: 3
  delay_between_requests: 1  # Seconds
  respect_robots_txt: true

# Supported platforms
platforms:
  youtube:
    enabled: true
    extract_chapters: true
    extract_subtitles: false
  vimeo:
    enabled: true
  dailymotion:
    enabled: true
  twitch:
    enabled: false  # Requires additional setup

# Metadata extraction
metadata:
  extract_description: true
  extract_tags: true
  extract_duration: true
  extract_upload_date: true
  extract_view_count: true
  extract_like_count: false  # May require authentication

# File naming
naming:
  sanitize_filenames: true
  max_filename_length: 100
  replace_spaces_with: "_"
  remove_special_chars: true

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  log_to_file: true
  log_file: "logs/indexer.log"
  max_log_size_mb: 10

# Performance
performance:
  max_concurrent_downloads: 3
  max_concurrent_processing: 2
  chunk_size: 8192  # Bytes for file operations
