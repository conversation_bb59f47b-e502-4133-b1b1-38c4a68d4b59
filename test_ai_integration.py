#!/usr/bin/env python3
"""
Test script to verify AI analysis integration with database storage.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.storage import VideoStorage
from pathlib import Path
import json

def test_ai_integration():
    """Test if AI analysis data is properly integrated into the database."""
    
    # Initialize storage
    storage = VideoStorage("data/video_index.db")
    
    print("🔍 Testing AI Analysis Integration")
    print("=" * 50)
    
    # Check if AI tables exist
    print("\n1. Checking database schema...")
    import sqlite3
    with sqlite3.connect("data/video_index.db") as conn:
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        ai_tables = ['ai_tags', 'frame_analysis', 'frame_embeddings']
        for table in ai_tables:
            if table in tables:
                print(f"   ✅ {table} table exists")
            else:
                print(f"   ❌ {table} table missing")
    
    # Check existing videos
    print("\n2. Checking existing videos...")
    videos = storage.search_videos("", limit=5)
    print(f"   📊 Found {len(videos)} videos in database")
    
    if videos:
        video = videos[0]
        print(f"   📹 Sample video: {video['title']}")
        print(f"   📁 Video directory: {video['video_dir']}")
        
        # Check if AI analysis files exist
        video_path = Path(video['video_dir'])
        ai_analysis_file = video_path / "ai_analysis.json"
        ai_embeddings_file = video_path / "screenshots" / "ai_embeddings.json"
        
        print(f"   🔍 AI analysis file exists: {ai_analysis_file.exists()}")
        print(f"   🔍 AI embeddings file exists: {ai_embeddings_file.exists()}")
        
        # Check if AI data is in database
        with sqlite3.connect("data/video_index.db") as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM ai_tags WHERE video_id = ?", (video['id'],))
            ai_tag_count = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM frame_analysis WHERE video_id = ?", (video['id'],))
            frame_count = cursor.fetchone()[0]
            
            print(f"   🏷️  AI tags in database: {ai_tag_count}")
            print(f"   🖼️  Frame analysis records: {frame_count}")
            
            if ai_tag_count > 0:
                cursor = conn.execute("SELECT tag FROM ai_tags WHERE video_id = ? LIMIT 5", (video['id'],))
                tags = [row[0] for row in cursor.fetchall()]
                print(f"   🏷️  Sample AI tags: {tags}")
    
    # Test search functionality
    print("\n3. Testing search functionality...")
    search_results = storage.search_videos("video", limit=3)
    print(f"   🔍 Search results for 'video': {len(search_results)} videos")
    
    if search_results:
        result = search_results[0]
        print(f"   📹 Result: {result['title']}")
        print(f"   🏷️  AI tags: {result.get('ai_tags', [])}")
    
    print("\n✅ AI Integration Test Complete!")

if __name__ == "__main__":
    test_ai_integration()
