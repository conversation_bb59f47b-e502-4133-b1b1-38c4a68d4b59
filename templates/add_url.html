{% extends "base.html" %}

{% block title %}Add Video URL - Video Search Indexer{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <!-- Page Header -->
        <div class="text-center mb-4">
            <h1 class="h2 mb-3">
                <i class="bi bi-plus-circle"></i> Add Video URL
            </h1>
            <p class="text-muted">
                Add a video URL to index and analyze. Supported platforms include YouTube, Vimeo, Dailymotion, and more.
            </p>
        </div>
        
        <!-- Add URL Form -->
        <div class="card">
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_url') }}">
                    <div class="mb-4">
                        <label for="url" class="form-label">Video URL</label>
                        <input type="url" class="form-control form-control-lg" id="url" name="url" 
                               placeholder="https://www.youtube.com/watch?v=..." 
                               required>
                        <div class="form-text">
                            Paste the full URL of the video you want to index and analyze.
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-plus-circle"></i> Add Video
                        </button>
                        <a href="{{ url_for('videos') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Videos
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Supported Platforms -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-check-circle"></i> Supported Platforms
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 col-md-3 mb-3">
                        <i class="bi bi-youtube text-danger fs-1"></i>
                        <div class="small mt-1">YouTube</div>
                    </div>
                    <div class="col-6 col-md-3 mb-3">
                        <i class="bi bi-vimeo text-primary fs-1"></i>
                        <div class="small mt-1">Vimeo</div>
                    </div>
                    <div class="col-6 col-md-3 mb-3">
                        <i class="bi bi-play-circle text-info fs-1"></i>
                        <div class="small mt-1">Dailymotion</div>
                    </div>
                    <div class="col-6 col-md-3 mb-3">
                        <i class="bi bi-globe text-success fs-1"></i>
                        <div class="small mt-1">Others</div>
                    </div>
                </div>
                <div class="text-center">
                    <small class="text-muted">
                        And many more platforms supported by yt-dlp
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Processing Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> What Happens Next?
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 32px; height: 32px;">
                                    <small>1</small>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">URL Added</h6>
                                <small class="text-muted">Video URL is validated and queued for processing</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 32px; height: 32px;">
                                    <small>2</small>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Metadata Extracted</h6>
                                <small class="text-muted">Title, description, duration, and other details are collected</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 32px; height: 32px;">
                                    <small>3</small>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Screenshots Captured</h6>
                                <small class="text-muted">Video frames are extracted every 5 seconds for analysis</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 32px; height: 32px;">
                                    <small>4</small>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">AI Analysis</h6>
                                <small class="text-muted">Screenshots are analyzed for visual search capabilities</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Example URLs -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightbulb"></i> Example URLs
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>YouTube:</strong>
                    <code class="ms-2">https://www.youtube.com/watch?v=dQw4w9WgXcQ</code>
                </div>
                <div class="mb-2">
                    <strong>Vimeo:</strong>
                    <code class="ms-2">https://vimeo.com/123456789</code>
                </div>
                <div class="mb-2">
                    <strong>Dailymotion:</strong>
                    <code class="ms-2">https://www.dailymotion.com/video/x123456</code>
                </div>
                <small class="text-muted">
                    Most video platforms are supported. If a platform isn't supported, you'll receive an error message.
                </small>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('url');
    const form = document.querySelector('form');
    
    // Auto-focus on URL input
    urlInput.focus();
    
    // URL validation feedback
    urlInput.addEventListener('input', function() {
        const url = this.value.trim();
        if (url && !isValidUrl(url)) {
            this.setCustomValidity('Please enter a valid URL');
        } else {
            this.setCustomValidity('');
        }
    });
    
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Adding Video...';
        
        // Re-enable button after a delay (in case of server error)
        setTimeout(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 10000);
    });
});
</script>
{% endblock %}
