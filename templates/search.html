{% extends "base.html" %}

{% block title %}AI Visual Search - Video Search Indexer{% endblock %}

{% block extra_head %}
<style>
.search-result-item {
    transition: transform 0.2s ease-in-out;
}
.search-result-item:hover {
    transform: translateY(-2px);
}
.similarity-bar {
    height: 4px;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    border-radius: 2px;
}
.loading-spinner {
    display: none;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Page Header -->
    <div class="col-12 mb-4">
        <h1 class="h2 mb-3">
            <i class="bi bi-robot"></i> AI Visual Search
        </h1>
        <p class="text-muted">
            Search through video screenshots using natural language. Describe what you're looking for and AI will find matching scenes.
        </p>
    </div>
    
    <!-- Search Interface -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                {% if ai_available %}
                    <form id="aiSearchForm">
                        <div class="row g-3">
                            <div class="col-md-10">
                                <label for="searchQuery" class="form-label">Search Query</label>
                                <input type="text" class="form-control form-control-lg" id="searchQuery" 
                                       placeholder="e.g., 'person dancing', 'red car', 'sunset landscape', 'people talking'..."
                                       required>
                                <div class="form-text">
                                    Describe what you want to find in the video screenshots. Be specific for better results.
                                </div>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="bi bi-search"></i>
                                    <span class="search-btn-text">Search</span>
                                    <div class="loading-spinner spinner-border spinner-border-sm ms-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- Example Queries -->
                    <div class="mt-3">
                        <small class="text-muted">Try these examples:</small>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 example-query" data-query="person singing">
                                person singing
                            </button>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 example-query" data-query="people dancing">
                                people dancing
                            </button>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 example-query" data-query="microphone">
                                microphone
                            </button>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 example-query" data-query="crowd of people">
                                crowd of people
                            </button>
                            <button class="btn btn-sm btn-outline-secondary me-2 mb-1 example-query" data-query="musical instrument">
                                musical instrument
                            </button>
                        </div>
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>AI Search Not Available</strong><br>
                        AI visual search requires additional dependencies. Please run the setup script to install AI components.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Search Results -->
    <div class="col-12">
        <div id="searchResults" style="display: none;">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-image"></i> Search Results
                        <span id="resultCount" class="badge bg-primary ms-2"></span>
                    </h5>
                    <small class="text-muted">
                        Query: "<span id="searchedQuery"></span>"
                    </small>
                </div>
                <div class="card-body">
                    <div id="resultsContainer" class="row g-4">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- No Results -->
        <div id="noResults" style="display: none;">
            <div class="text-center py-5">
                <i class="bi bi-search fs-1 text-muted"></i>
                <h5 class="mt-3 text-muted">No matching scenes found</h5>
                <p class="text-muted">Try different search terms or make sure videos have been processed with AI analysis.</p>
            </div>
        </div>
        
        <!-- Error Message -->
        <div id="searchError" style="display: none;">
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>Search Error:</strong>
                <span id="errorMessage"></span>
            </div>
        </div>
    </div>
</div>

{% if ai_available %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('aiSearchForm');
    const searchQuery = document.getElementById('searchQuery');
    const searchBtn = searchForm.querySelector('button[type="submit"]');
    const searchBtnText = searchBtn.querySelector('.search-btn-text');
    const loadingSpinner = searchBtn.querySelector('.loading-spinner');
    const searchResults = document.getElementById('searchResults');
    const noResults = document.getElementById('noResults');
    const searchError = document.getElementById('searchError');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultCount = document.getElementById('resultCount');
    const searchedQuery = document.getElementById('searchedQuery');
    const errorMessage = document.getElementById('errorMessage');
    
    // Example query buttons
    document.querySelectorAll('.example-query').forEach(btn => {
        btn.addEventListener('click', function() {
            searchQuery.value = this.dataset.query;
            searchForm.dispatchEvent(new Event('submit'));
        });
    });
    
    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const query = searchQuery.value.trim();
        if (!query) return;
        
        // Show loading state
        searchBtn.disabled = true;
        searchBtnText.textContent = 'Searching...';
        loadingSpinner.style.display = 'inline-block';
        
        // Hide previous results
        searchResults.style.display = 'none';
        noResults.style.display = 'none';
        searchError.style.display = 'none';
        
        // Perform search
        fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query: query })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayResults(data.results, data.query, data.count);
            } else {
                showError(data.error || 'Search failed');
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            showError('Network error occurred');
        })
        .finally(() => {
            // Reset loading state
            searchBtn.disabled = false;
            searchBtnText.textContent = 'Search';
            loadingSpinner.style.display = 'none';
        });
    });
    
    function displayResults(results, query, count) {
        searchedQuery.textContent = query;
        resultCount.textContent = count;
        
        if (results.length === 0) {
            noResults.style.display = 'block';
            return;
        }
        
        // Clear previous results
        resultsContainer.innerHTML = '';
        
        // Add results
        results.forEach(result => {
            const resultItem = createResultItem(result);
            resultsContainer.appendChild(resultItem);
        });
        
        searchResults.style.display = 'block';
    }
    
    function createResultItem(result) {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6';
        
        const similarityPercent = Math.round(result.similarity * 100);
        const similarityColor = getSimilarityColor(result.similarity);
        
        col.innerHTML = `
            <div class="card search-result-item h-100 shadow-sm">
                <div class="position-relative">
                    <img src="${result.screenshot_url}" class="card-img-top" alt="Screenshot" 
                         style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge" style="background-color: ${similarityColor}">
                            ${similarityPercent}% match
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title text-truncate" title="${result.video_title}">
                        ${result.video_title}
                    </h6>
                    <div class="similarity-bar mb-2" style="width: ${similarityPercent}%; background-color: ${similarityColor}"></div>
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="${result.video_detail_url}" class="btn btn-sm btn-primary">
                            <i class="bi bi-eye"></i> View Video
                        </a>
                        <a href="${result.video_url}" target="_blank" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        return col;
    }
    
    function getSimilarityColor(similarity) {
        if (similarity >= 0.7) return '#28a745'; // Green
        if (similarity >= 0.5) return '#ffc107'; // Yellow
        if (similarity >= 0.3) return '#fd7e14'; // Orange
        return '#dc3545'; // Red
    }
    
    function showError(message) {
        errorMessage.textContent = message;
        searchError.style.display = 'block';
    }
});
</script>
{% endif %}
{% endblock %}

{% block extra_scripts %}
{% if ai_available %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('aiSearchForm');
    const searchQuery = document.getElementById('searchQuery');
    const searchBtn = searchForm.querySelector('button[type="submit"]');
    const searchBtnText = searchBtn.querySelector('.search-btn-text');
    const loadingSpinner = searchBtn.querySelector('.loading-spinner');
    const searchResults = document.getElementById('searchResults');
    const noResults = document.getElementById('noResults');
    const searchError = document.getElementById('searchError');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultCount = document.getElementById('resultCount');
    const searchedQuery = document.getElementById('searchedQuery');
    const errorMessage = document.getElementById('errorMessage');

    // Example query buttons
    document.querySelectorAll('.example-query').forEach(btn => {
        btn.addEventListener('click', function() {
            searchQuery.value = this.dataset.query;
            searchForm.dispatchEvent(new Event('submit'));
        });
    });

    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const query = searchQuery.value.trim();
        if (!query) return;

        // Show loading state
        searchBtn.disabled = true;
        searchBtnText.textContent = 'Searching...';
        loadingSpinner.style.display = 'inline-block';

        // Hide previous results
        searchResults.style.display = 'none';
        noResults.style.display = 'none';
        searchError.style.display = 'none';

        // Perform search
        fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query: query })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayResults(data.results, data.query, data.count);
            } else {
                showError(data.error || 'Search failed');
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            showError('Network error occurred');
        })
        .finally(() => {
            // Reset loading state
            searchBtn.disabled = false;
            searchBtnText.textContent = 'Search';
            loadingSpinner.style.display = 'none';
        });
    });

    function displayResults(results, query, count) {
        searchedQuery.textContent = query;
        resultCount.textContent = count;

        if (results.length === 0) {
            noResults.style.display = 'block';
            return;
        }

        // Clear previous results
        resultsContainer.innerHTML = '';

        // Add results
        results.forEach(result => {
            const resultItem = createResultItem(result);
            resultsContainer.appendChild(resultItem);
        });

        searchResults.style.display = 'block';
    }

    function createResultItem(result) {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6';

        const similarityPercent = Math.round(result.similarity * 100);
        const similarityColor = getSimilarityColor(result.similarity);

        col.innerHTML = `
            <div class="card search-result-item h-100 shadow-sm">
                <div class="position-relative">
                    <img src="${result.screenshot_url}" class="card-img-top" alt="Screenshot"
                         style="height: 200px; object-fit: cover;">
                    <div class="position-absolute top-0 end-0 m-2">
                        <span class="badge" style="background-color: ${similarityColor}">
                            ${similarityPercent}% match
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <h6 class="card-title text-truncate" title="${result.video_title}">
                        ${result.video_title}
                    </h6>
                    <div class="similarity-bar mb-2" style="width: ${similarityPercent}%; background-color: ${similarityColor}"></div>
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="${result.video_detail_url}" class="btn btn-sm btn-primary">
                            <i class="bi bi-eye"></i> View Video
                        </a>
                        <a href="${result.video_url}" target="_blank" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-box-arrow-up-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        `;

        return col;
    }

    function getSimilarityColor(similarity) {
        if (similarity >= 0.7) return '#28a745'; // Green
        if (similarity >= 0.5) return '#ffc107'; // Yellow
        if (similarity >= 0.3) return '#fd7e14'; // Orange
        return '#dc3545'; // Red
    }

    function showError(message) {
        errorMessage.textContent = message;
        searchError.style.display = 'block';
    }
});
</script>
{% endif %}
