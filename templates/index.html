{% extends "base.html" %}

{% block title %}Dashboard - Video Search Indexer{% endblock %}

{% block content %}
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-12 mb-4">
        <h1 class="h2 mb-4">
            <i class="bi bi-speedometer2"></i> Dashboard
        </h1>
        
        <div class="row g-3">
            <!-- Total Videos -->
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Total Videos</h5>
                                <h2 class="mb-0">{{ stats.get('total_videos', 0) }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-collection-play fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Processed Videos -->
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Processed</h5>
                                <h2 class="mb-0">{{ stats.get('processed_videos', 0) }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-check-circle fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pending Videos -->
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Pending</h5>
                                <h2 class="mb-0">{{ stats.get('pending_videos', 0) }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-clock fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Recent (24h)</h5>
                                <h2 class="mb-0">{{ stats.get('recent_activity', 0) }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-activity fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="{{ url_for('add_url') }}" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-plus-circle"></i>
                            Add New Video URL
                        </a>
                    </div>
                    <div class="col-md-6">
                        {% if stats.get('pending_videos', 0) > 0 %}
                            <form method="POST" action="{{ url_for('process_videos') }}" class="w-100">
                                <button type="submit" class="btn btn-success btn-lg w-100">
                                    <i class="bi bi-play-circle"></i>
                                    Process Pending Videos
                                </button>
                            </form>
                        {% else %}
                            <button class="btn btn-secondary btn-lg w-100" disabled>
                                <i class="bi bi-check-circle"></i>
                                No Pending Videos
                            </button>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url_for('search') }}" class="btn btn-info btn-lg w-100 {% if not ai_available %}disabled{% endif %}">
                            <i class="bi bi-search"></i>
                            AI Visual Search
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url_for('videos') }}" class="btn btn-outline-primary btn-lg w-100">
                            <i class="bi bi-grid"></i>
                            Browse All Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Platform Statistics -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i> Platforms
                </h5>
            </div>
            <div class="card-body">
                {% if stats.get('platforms') %}
                    {% for platform, count in stats.platforms.items() %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-capitalize">{{ platform }}</span>
                            <span class="badge bg-primary">{{ count }}</span>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted mb-0">No videos indexed yet</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Videos -->
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> Recent Videos
                </h5>
                <a href="{{ url_for('videos') }}" class="btn btn-sm btn-outline-primary">
                    View All <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                {% if videos %}
                    <div class="row g-3">
                        {% for video in videos %}
                            <div class="col-md-3 col-sm-6">
                                <div class="card video-card h-100">
                                    <div class="position-relative">
                                        <img src="{{ url_for('serve_thumbnail', video_id=video.id) }}" 
                                             class="card-img-top video-thumbnail" 
                                             alt="{{ video.title }}"
                                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGVlMmU2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIFRodW1ibmFpbDwvdGV4dD48L3N2Zz4='">
                                        <span class="badge bg-primary position-absolute top-0 end-0 m-2">
                                            {{ video.platform|title }}
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="card-title text-truncate" title="{{ video.title }}">
                                            {{ video.title }}
                                        </h6>
                                        <p class="card-text small text-muted">
                                            Duration: {{ video.duration or 'Unknown' }}
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <a href="{{ url_for('video_detail', video_id=video.id) }}" 
                                               class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                            <small class="text-muted">
                                                {{ video.extraction_date[:10] if video.extraction_date else 'Unknown' }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-camera-video-off fs-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">No videos indexed yet</h5>
                        <p class="text-muted">Start by adding your first video URL</p>
                        <a href="{{ url_for('add_url') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Video URL
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh stats every 30 seconds -->
<script>
setInterval(function() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // Update stats cards
            document.querySelector('.bg-primary .h2').textContent = data.total_videos || 0;
            document.querySelector('.bg-success .h2').textContent = data.processed_videos || 0;
            document.querySelector('.bg-warning .h2').textContent = data.pending_videos || 0;
            document.querySelector('.bg-info .h2').textContent = data.recent_activity || 0;
        })
        .catch(error => console.log('Stats update failed:', error));
}, 30000);
</script>
{% endblock %}
