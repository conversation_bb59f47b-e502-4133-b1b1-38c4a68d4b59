{% extends "simple_base.html" %}

{% block title %}Processing Progress - Video Search Indexer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="text-gradient">
        <i class="fas fa-chart-line me-2"></i>Processing Progress
    </h1>
    <div>
        <form method="POST" action="{{ url_for('process_videos') }}" class="d-inline">
            <button type="submit" class="btn btn-success">
                <i class="fas fa-play me-1"></i>Process Videos
            </button>
        </form>
        <a href="{{ url_for('add_url') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Add URL
        </a>
    </div>
</div>

<!-- Progress Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-info mb-2">
                <i class="fas fa-list fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ url_stats.total_urls or 0 }}</h3>
            <small class="text-muted">Total URLs</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-success mb-2">
                <i class="fas fa-check-circle fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ url_stats.by_status.completed or 0 }}</h3>
            <small class="text-muted">Completed</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-warning mb-2">
                <i class="fas fa-clock fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ url_stats.by_status.pending or 0 }}</h3>
            <small class="text-muted">Pending</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-danger mb-2">
                <i class="fas fa-exclamation-circle fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ url_stats.by_status.failed or 0 }}</h3>
            <small class="text-muted">Failed</small>
        </div>
    </div>
</div>

<!-- Overall Progress -->
{% if url_stats.total_urls and url_stats.total_urls > 0 %}
    <div class="modern-card p-4 mb-4">
        <h4 class="mb-3">
            <i class="fas fa-chart-bar me-2"></i>Overall Progress
        </h4>
        {% set completed = url_stats.by_status.completed or 0 %}
        {% set pending = url_stats.by_status.pending or 0 %}
        {% set failed = url_stats.by_status.failed or 0 %}
        {% set total = url_stats.total_urls %}
        {% set completed_percent = (completed / total * 100) | round(1) %}
        {% set pending_percent = (pending / total * 100) | round(1) %}
        {% set failed_percent = (failed / total * 100) | round(1) %}

        <div class="progress mb-3" style="height: 20px;">
            <div class="progress-bar bg-success" role="progressbar"
                 style="width: {{ completed_percent }}%"
                 aria-valuenow="{{ completed }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                {% if completed_percent > 10 %}{{ completed_percent }}%{% endif %}
            </div>
            <div class="progress-bar bg-warning" role="progressbar"
                 style="width: {{ pending_percent }}%"
                 aria-valuenow="{{ pending }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                {% if pending_percent > 10 %}{{ pending_percent }}%{% endif %}
            </div>
            <div class="progress-bar bg-danger" role="progressbar"
                 style="width: {{ failed_percent }}%"
                 aria-valuenow="{{ failed }}" aria-valuemin="0" aria-valuemax="{{ total }}">
                {% if failed_percent > 10 %}{{ failed_percent }}%{% endif %}
            </div>
        </div>

        <div class="row text-center">
            <div class="col-md-4">
                <div class="text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    <strong>{{ completed }} Completed</strong>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-warning">
                    <i class="fas fa-clock me-1"></i>
                    <strong>{{ pending }} Pending</strong>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-danger">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <strong>{{ failed }} Failed</strong>
                </div>
            </div>
        </div>
    </div>
{% endif %}

<!-- Currently Processing -->
{% if processing_urls %}
    <div class="modern-card p-4 mb-4">
        <h4 class="mb-3">
            <i class="fas fa-spinner fa-spin me-2"></i>Currently Processing
        </h4>
        {% for url_entry in processing_urls %}
            <div class="mb-3 p-3 bg-light rounded">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ url_entry.platform.title() }}</h6>
                        <small class="text-muted">{{ url_entry.original_url[:60] }}{% if url_entry.original_url|length > 60 %}...{% endif %}</small>
                    </div>
                    <span class="badge bg-info">Processing</span>
                </div>

                <!-- Processing Steps Progress -->
                <div class="processing-steps">
                    <div class="row text-center small">
                        <div class="col-3">
                            <div class="step-indicator completed">
                                <i class="fas fa-check-circle text-success"></i>
                                <div>Metadata</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="step-indicator active">
                                <i class="fas fa-spinner fa-spin text-primary"></i>
                                <div>Screenshots</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="step-indicator">
                                <i class="fas fa-circle text-muted"></i>
                                <div>AI Analysis</div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="step-indicator">
                                <i class="fas fa-circle text-muted"></i>
                                <div>Storage</div>
                            </div>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}

<!-- Pending URLs -->
{% if pending_urls %}
    <div class="modern-card p-4 mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">
                <i class="fas fa-clock me-2"></i>Pending URLs ({{ pending_urls|length }})
            </h4>
            <form method="POST" action="{{ url_for('process_videos') }}" class="d-inline">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-play me-1"></i>Process Now
                </button>
            </form>
        </div>

        {% for url_entry in pending_urls[:8] %}
            <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-light rounded">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-1">
                        {% if url_entry.platform == 'youtube' %}
                            <i class="fab fa-youtube text-danger me-2"></i>
                        {% elif url_entry.platform == 'vimeo' %}
                            <i class="fab fa-vimeo text-primary me-2"></i>
                        {% else %}
                            <i class="fas fa-video text-secondary me-2"></i>
                        {% endif %}
                        <strong>{{ url_entry.platform.title() }}</strong>
                    </div>
                    <small class="text-muted d-block">{{ url_entry.original_url[:70] }}{% if url_entry.original_url|length > 70 %}...{% endif %}</small>
                    <small class="text-muted">Added: {{ url_entry.added_date[:19] }}</small>
                </div>
                <div class="text-end">
                    <span class="badge bg-warning">Pending</span>
                    {% if url_entry.processing_attempts > 0 %}
                        <br><small class="text-muted">{{ url_entry.processing_attempts }} attempts</small>
                    {% endif %}
                </div>
            </div>
        {% endfor %}

        {% if pending_urls|length > 8 %}
            <div class="text-center mt-3">
                <small class="text-muted">... and {{ pending_urls|length - 8 }} more pending URLs</small>
            </div>
        {% endif %}
    </div>
{% endif %}

<!-- Recently Processed -->
{% if recent_videos %}
    <div class="modern-card p-4">
        <h4 class="mb-3">
            <i class="fas fa-history me-2"></i>Recently Processed
        </h4>
        {% for video in recent_videos[:8] %}
            <div class="modern-card video-card-compact expandable mb-3" onclick="toggleExpand(this)">
                <!-- Compact View -->
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <div class="video-title">{{ video.title }}</div>
                        <div class="video-meta">
                            <span class="platform-badge">{{ video.platform }}</span>
                            {% if video.uploader %}
                                <span><i class="fas fa-user me-1"></i>{{ video.uploader }}</span>
                            {% endif %}
                            <span><i class="fas fa-images me-1"></i>{{ video.screenshot_count or 0 }} frames</span>
                            <span><i class="fas fa-clock me-1"></i>{{ video.extraction_date[:19] if video.extraction_date else 'Unknown' }}</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge bg-success">
                            <i class="fas fa-check me-1"></i>Completed
                        </span>
                        <i class="fas fa-chevron-down expand-icon text-muted"></i>
                    </div>
                </div>

                <!-- Expandable Content -->
                <div class="expandable-content">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-info-circle me-1"></i>Processing Details
                            </h6>
                            <div class="mb-3">
                                <div class="mb-1">
                                    <strong>Duration:</strong> {{ video.duration_seconds }}s
                                </div>
                                <div class="mb-1">
                                    <strong>Screenshots:</strong> {{ video.screenshot_count or 0 }} frames
                                </div>
                                <div class="mb-1">
                                    <strong>AI Analysis:</strong>
                                    {% if video.ai_tags %}
                                        <span class="badge bg-success">{{ video.ai_tags|length }} tags generated</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Not analyzed</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-chart-bar me-1"></i>Processing Stats
                            </h6>
                            <div class="processing-timeline">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>Metadata extracted</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>{{ video.screenshot_count or 0 }} screenshots captured</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>AI analysis completed</small>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>Data stored successfully</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between pt-3 border-top">
                        {% if video.url %}
                            <a href="{{ video.url }}" target="_blank" class="btn btn-sm btn-primary" onclick="event.stopPropagation()">
                                <i class="fas fa-external-link-alt me-1"></i>View Original
                            </a>
                        {% endif %}
                        <a href="{{ url_for('view_videos') }}" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation()">
                            <i class="fas fa-film me-1"></i>View All Videos
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="modern-card text-center py-5">
        <div class="text-muted">
            <i class="fas fa-history fa-3x mb-3"></i>
            <h4>No videos processed yet</h4>
            <p>Add some URLs and process them to see progress here!</p>
            <a href="{{ url_for('add_url') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Add Your First URL
            </a>
        </div>
    </div>
{% endif %}

<style>
.processing-steps .step-indicator {
    padding: 0.5rem 0;
}

.processing-timeline {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}
</style>

<script>
// Auto-refresh every 15 seconds if there are pending or processing videos
{% if pending_urls or processing_urls %}
    enableAutoRefresh(15000);
{% endif %}
</script>
{% endblock %}
