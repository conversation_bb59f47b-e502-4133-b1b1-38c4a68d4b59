<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Video Search Indexer{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        body {
            background-color: var(--light-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        /* Modern Card Design */
        .modern-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .modern-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .modern-card.expandable {
            cursor: pointer;
        }

        /* Compact Video Cards */
        .video-card-compact {
            padding: 1rem;
            margin-bottom: 0.75rem;
        }

        .video-card-compact .video-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .video-card-compact .video-meta {
            display: flex;
            gap: 1rem;
            align-items: center;
            color: var(--secondary-color);
            font-size: 0.875rem;
        }

        .video-card-compact .platform-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Expandable Content */
        .expandable-content {
            display: none;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
            margin-top: 1rem;
        }

        .expandable-content.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Tag Styles */
        .tag-clickable {
            background: #f1f5f9;
            color: var(--secondary-color);
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
            text-decoration: none;
            display: inline-block;
            margin: 0.125rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .tag-clickable:hover {
            background: var(--primary-color);
            color: white;
            text-decoration: none;
        }

        .ai-tag {
            background: linear-gradient(135deg, #8b5cf6, #a855f7);
            color: white;
        }

        .ai-tag:hover {
            background: linear-gradient(135deg, #7c3aed, #9333ea);
            color: white;
        }

        .theme-tag {
            background: var(--secondary-color);
            color: white;
            border: 1px solid rgba(100, 116, 139, 0.3);
        }

        .theme-tag:hover {
            background: #475569;
            color: white;
            transform: translateY(-1px);
        }

        .ai-summary {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* Modern Navigation */
        .navbar {
            background: white !important;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid #e2e8f0;
        }

        .navbar-brand {
            color: var(--dark-color) !important;
            font-weight: 700;
            font-size: 1.25rem;
        }

        .nav-link {
            color: var(--secondary-color) !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.25rem;
            transition: all 0.2s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--light-color);
            color: var(--primary-color) !important;
        }

        /* Modern Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(-1px);
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            padding: 0.75rem;
            transition: all 0.2s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-video me-2"></i>Video Search Indexer
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('add_url') }}">
                            <i class="fas fa-plus me-1"></i>Add URL
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_videos') }}">
                            <i class="fas fa-film me-1"></i>Videos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_sites') }}">
                            <i class="fas fa-globe me-1"></i>Sites
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_progress') }}">
                            <i class="fas fa-chart-line me-1"></i>Progress
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show modern-card" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Expandable cards functionality
        function toggleExpand(element) {
            const content = element.querySelector('.expandable-content');
            const icon = element.querySelector('.expand-icon');

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                if (icon) icon.classList.replace('fa-chevron-up', 'fa-chevron-down');
            } else {
                content.classList.add('show');
                if (icon) icon.classList.replace('fa-chevron-down', 'fa-chevron-up');
            }
        }

        // Site management toggle
        function toggleSite(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('.site-toggle-icon');

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                if (icon) icon.classList.replace('fa-chevron-up', 'fa-chevron-down');
            } else {
                content.classList.add('show');
                if (icon) icon.classList.replace('fa-chevron-down', 'fa-chevron-up');
            }
        }

        // Tag click handler for search
        function searchByTag(tag) {
            window.location.href = `{{ url_for('index') }}?q=${encodeURIComponent(tag)}`;
        }

        // Category click handler for search
        function searchByCategory(category) {
            window.location.href = `{{ url_for('index') }}?category=${encodeURIComponent(category)}`;
        }

        // Auto-refresh for progress page
        function enableAutoRefresh(interval = 30000) {
            setTimeout(() => location.reload(), interval);
        }
    </script>
</body>
</html>
