{% extends "base.html" %}

{% block title %}{{ video.title }} - Video Search Indexer{% endblock %}

{% block content %}
<div class="row">
    <!-- Video Information -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-play-circle"></i> Video Details
                </h5>
                <div>
                    <a href="{{ video.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-box-arrow-up-right"></i> Open Original
                    </a>
                    <a href="{{ url_for('videos') }}" class="btn btn-sm btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Videos
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Video Title -->
                <h3 class="mb-3">{{ video.title }}</h3>
                
                <!-- Video Thumbnail -->
                <div class="text-center mb-4">
                    <img src="{{ url_for('serve_thumbnail', video_id=video.id) }}" 
                         class="img-fluid rounded shadow" 
                         alt="{{ video.title }}"
                         style="max-height: 400px;"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGVlMmU2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIFRodW1ibmFpbDwvdGV4dD48L3N2Zz4='">
                </div>
                
                <!-- Video Description -->
                {% if video.description %}
                    <div class="mb-4">
                        <h6>Description</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0" style="white-space: pre-wrap;">{{ video.description }}</p>
                        </div>
                    </div>
                {% endif %}
                
                <!-- Video Metadata -->
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="bg-light p-3 rounded">
                            <h6 class="text-muted mb-2">
                                <i class="bi bi-clock"></i> Duration
                            </h6>
                            <p class="mb-0">{{ video.duration or 'Unknown' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="bg-light p-3 rounded">
                            <h6 class="text-muted mb-2">
                                <i class="bi bi-globe"></i> Platform
                            </h6>
                            <p class="mb-0 text-capitalize">{{ video.platform }}</p>
                        </div>
                    </div>
                    {% if video.view_count %}
                        <div class="col-md-6">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-muted mb-2">
                                    <i class="bi bi-eye"></i> Views
                                </h6>
                                <p class="mb-0">{{ "{:,}".format(video.view_count|int) if video.view_count else 'Unknown' }}</p>
                            </div>
                        </div>
                    {% endif %}
                    {% if video.like_count %}
                        <div class="col-md-6">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-muted mb-2">
                                    <i class="bi bi-heart"></i> Likes
                                </h6>
                                <p class="mb-0">{{ "{:,}".format(video.like_count|int) if video.like_count else 'Unknown' }}</p>
                            </div>
                        </div>
                    {% endif %}
                    {% if video.uploader %}
                        <div class="col-md-6">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-muted mb-2">
                                    <i class="bi bi-person"></i> Uploader
                                </h6>
                                <p class="mb-0">{{ video.uploader }}</p>
                            </div>
                        </div>
                    {% endif %}
                    {% if video.upload_date %}
                        <div class="col-md-6">
                            <div class="bg-light p-3 rounded">
                                <h6 class="text-muted mb-2">
                                    <i class="bi bi-calendar"></i> Upload Date
                                </h6>
                                <p class="mb-0">{{ video.upload_date }}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Video Statistics -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i> Processing Info
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Extraction Date</small>
                    <div>{{ video.extraction_date[:19] if video.extraction_date else 'Unknown' }}</div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Screenshots</small>
                    <div>{{ screenshots|length }} frames captured</div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">AI Analysis</small>
                    <div>
                        {% set ai_analysis_path = video.video_dir + '/ai_analysis.json' %}
                        {% if ai_analysis_path|file_exists %}
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle"></i> Completed
                            </span>
                        {% else %}
                            <span class="badge bg-secondary">
                                <i class="bi bi-clock"></i> Pending
                            </span>
                        {% endif %}
                    </div>
                </div>
                
                {% if video.tags %}
                    <div class="mb-3">
                        <small class="text-muted">Tags</small>
                        <div class="mt-1">
                            {% for tag in video.tags.split(',')[:5] %}
                                <span class="badge bg-primary me-1 mb-1">{{ tag.strip() }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if ai_available %}
                        <a href="{{ url_for('search') }}?q={{ video.title[:50] }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-search"></i> Find Similar Videos
                        </a>
                    {% endif %}
                    <a href="{{ video.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-play"></i> Watch Original
                    </a>
                    <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('{{ video.url }}')">
                        <i class="bi bi-clipboard"></i> Copy URL
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Screenshots Gallery -->
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-images"></i> Screenshots
                    <span class="badge bg-primary ms-2">{{ screenshots|length }}</span>
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="gridView" onclick="setView('grid')">
                        <i class="bi bi-grid"></i> Grid
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="listView" onclick="setView('list')">
                        <i class="bi bi-list"></i> List
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if screenshots %}
                    <div id="screenshotsContainer" class="row g-3">
                        {% for screenshot in screenshots %}
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6 screenshot-item">
                                <div class="card screenshot-card h-100">
                                    <img src="{{ screenshot.url }}" 
                                         class="card-img-top screenshot-thumbnail" 
                                         alt="Screenshot {{ loop.index }}"
                                         data-bs-toggle="modal" 
                                         data-bs-target="#screenshotModal"
                                         data-screenshot-url="{{ screenshot.url }}"
                                         data-screenshot-index="{{ loop.index }}"
                                         style="cursor: pointer; height: 120px; object-fit: cover;">
                                    <div class="card-body p-2">
                                        <small class="text-muted">Frame {{ loop.index }}</small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-camera-video-off fs-1 text-muted"></i>
                        <h6 class="mt-3 text-muted">No screenshots available</h6>
                        <p class="text-muted">Screenshots may still be processing or failed to extract.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Screenshot Modal -->
<div class="modal fade" id="screenshotModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Screenshot <span id="modalScreenshotIndex"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalScreenshotImage" src="" class="img-fluid" alt="Screenshot">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="downloadScreenshot()">
                    <i class="bi bi-download"></i> Download
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentScreenshotUrl = '';

// Screenshot modal handling
document.addEventListener('DOMContentLoaded', function() {
    const screenshotModal = document.getElementById('screenshotModal');
    
    screenshotModal.addEventListener('show.bs.modal', function(event) {
        const trigger = event.relatedTarget;
        const screenshotUrl = trigger.getAttribute('data-screenshot-url');
        const screenshotIndex = trigger.getAttribute('data-screenshot-index');
        
        currentScreenshotUrl = screenshotUrl;
        
        document.getElementById('modalScreenshotImage').src = screenshotUrl;
        document.getElementById('modalScreenshotIndex').textContent = screenshotIndex;
    });
});

// View switching
function setView(viewType) {
    const container = document.getElementById('screenshotsContainer');
    const gridBtn = document.getElementById('gridView');
    const listBtn = document.getElementById('listView');
    
    if (viewType === 'grid') {
        container.className = 'row g-3';
        container.querySelectorAll('.screenshot-item').forEach(item => {
            item.className = 'col-lg-2 col-md-3 col-sm-4 col-6 screenshot-item';
        });
        gridBtn.classList.add('active');
        listBtn.classList.remove('active');
    } else {
        container.className = 'row g-2';
        container.querySelectorAll('.screenshot-item').forEach(item => {
            item.className = 'col-12 screenshot-item';
        });
        listBtn.classList.add('active');
        gridBtn.classList.remove('active');
    }
    
    localStorage.setItem('screenshotView', viewType);
}

// Load saved view preference
document.addEventListener('DOMContentLoaded', function() {
    const savedView = localStorage.getItem('screenshotView') || 'grid';
    setView(savedView);
});

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check"></i> Copied!';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}

// Download screenshot
function downloadScreenshot() {
    if (currentScreenshotUrl) {
        const link = document.createElement('a');
        link.href = currentScreenshotUrl;
        link.download = 'screenshot.jpg';
        link.click();
    }
}
</script>
{% endblock %}
