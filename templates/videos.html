{% extends "base.html" %}

{% block title %}Videos - Video Search Indexer{% endblock %}

{% block content %}
<div class="row">
    <!-- Page Header -->
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="h2 mb-0">
                <i class="bi bi-collection-play"></i> Video Gallery
            </h1>
            <a href="{{ url_for('add_url') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Add Video
            </a>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="{{ url_for('videos') }}" class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">Search Videos</label>
                        <input type="text" class="form-control" id="search" name="q" 
                               placeholder="Search by title, description, or tags..." 
                               value="{{ search_query }}">
                    </div>
                    <div class="col-md-4">
                        <label for="platform" class="form-label">Platform</label>
                        <select class="form-select" id="platform" name="platform">
                            <option value="">All Platforms</option>
                            <option value="youtube" {% if platform == 'youtube' %}selected{% endif %}>YouTube</option>
                            <option value="vimeo" {% if platform == 'vimeo' %}selected{% endif %}>Vimeo</option>
                            <option value="dailymotion" {% if platform == 'dailymotion' %}selected{% endif %}>Dailymotion</option>
                            <option value="other" {% if platform == 'other' %}selected{% endif %}>Other</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i> Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Results Info -->
    {% if search_query or platform %}
        <div class="col-12 mb-3">
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                Showing results for:
                {% if search_query %}
                    <strong>"{{ search_query }}"</strong>
                {% endif %}
                {% if platform %}
                    {% if search_query %}and{% endif %}
                    <strong>{{ platform|title }}</strong> platform
                {% endif %}
                <a href="{{ url_for('videos') }}" class="btn btn-sm btn-outline-primary ms-2">
                    <i class="bi bi-x"></i> Clear Filters
                </a>
            </div>
        </div>
    {% endif %}
    
    <!-- Video Grid -->
    <div class="col-12">
        {% if videos %}
            <div class="row g-4">
                {% for video in videos %}
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <div class="card video-card h-100 shadow-sm">
                            <div class="position-relative">
                                <img src="{{ url_for('serve_thumbnail', video_id=video.id) }}" 
                                     class="card-img-top video-thumbnail" 
                                     alt="{{ video.title }}"
                                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGVlMmU2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIFRodW1ibmFpbDwvdGV4dD48L3N2Zz4='">
                                
                                <!-- Platform Badge -->
                                <span class="badge bg-primary position-absolute top-0 end-0 m-2">
                                    {{ video.platform|title }}
                                </span>
                                
                                <!-- Duration Badge -->
                                {% if video.duration %}
                                    <span class="badge bg-dark position-absolute bottom-0 end-0 m-2">
                                        {{ video.duration }}
                                    </span>
                                {% endif %}
                                
                                <!-- Play Overlay -->
                                <div class="video-overlay">
                                    <a href="{{ url_for('video_detail', video_id=video.id) }}" 
                                       class="btn btn-light btn-lg rounded-circle">
                                        <i class="bi bi-play-fill"></i>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title text-truncate" title="{{ video.title }}">
                                    {{ video.title }}
                                </h6>
                                
                                {% if video.description %}
                                    <p class="card-text small text-muted flex-grow-1" 
                                       style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                                        {{ video.description }}
                                    </p>
                                {% endif %}
                                
                                <!-- Video Stats -->
                                <div class="mt-auto">
                                    <div class="row text-center small text-muted mb-2">
                                        {% if video.view_count %}
                                            <div class="col">
                                                <i class="bi bi-eye"></i>
                                                {{ video.view_count|int|filesizeformat if video.view_count|int > 1000 else video.view_count }}
                                            </div>
                                        {% endif %}
                                        {% if video.like_count %}
                                            <div class="col">
                                                <i class="bi bi-heart"></i>
                                                {{ video.like_count|int|filesizeformat if video.like_count|int > 1000 else video.like_count }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <!-- Action Buttons -->
                                    <div class="d-flex justify-content-between align-items-center">
                                        <a href="{{ url_for('video_detail', video_id=video.id) }}" 
                                           class="btn btn-sm btn-primary">
                                            <i class="bi bi-eye"></i> View Details
                                        </a>
                                        <a href="{{ video.url }}" target="_blank" 
                                           class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-box-arrow-up-right"></i>
                                        </a>
                                    </div>
                                    
                                    <!-- Date -->
                                    <div class="text-center mt-2">
                                        <small class="text-muted">
                                            Added: {{ video.extraction_date[:10] if video.extraction_date else 'Unknown' }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if total > per_page %}
                <nav aria-label="Video pagination" class="mt-5">
                    <ul class="pagination justify-content-center">
                        {% set total_pages = (total / per_page)|round(0, 'ceil')|int %}
                        
                        <!-- Previous Page -->
                        {% if page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('videos', page=page-1, q=search_query, platform=platform) }}">
                                    <i class="bi bi-chevron-left"></i> Previous
                                </a>
                            </li>
                        {% endif %}
                        
                        <!-- Page Numbers -->
                        {% for p in range(1, total_pages + 1) %}
                            {% if p == page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ p }}</span>
                                </li>
                            {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('videos', page=p, q=search_query, platform=platform) }}">{{ p }}</a>
                                </li>
                            {% elif p == 4 and page > 6 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% elif p == total_pages - 3 and page < total_pages - 5 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        <!-- Next Page -->
                        {% if page < total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('videos', page=page+1, q=search_query, platform=platform) }}">
                                    Next <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
            
        {% else %}
            <!-- No Videos Found -->
            <div class="text-center py-5">
                <i class="bi bi-camera-video-off fs-1 text-muted"></i>
                <h5 class="mt-3 text-muted">
                    {% if search_query or platform %}
                        No videos found matching your criteria
                    {% else %}
                        No videos indexed yet
                    {% endif %}
                </h5>
                <p class="text-muted">
                    {% if search_query or platform %}
                        Try adjusting your search terms or filters
                    {% else %}
                        Start by adding your first video URL
                    {% endif %}
                </p>
                <div class="mt-3">
                    {% if search_query or platform %}
                        <a href="{{ url_for('videos') }}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-arrow-left"></i> Show All Videos
                        </a>
                    {% endif %}
                    <a href="{{ url_for('add_url') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Video URL
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
