{% extends "simple_base.html" %}

{% block title %}Manage Sites - Video Search Indexer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="text-gradient">
        <i class="fas fa-globe me-2"></i>Manage Sites
    </h1>
    <a href="{{ url_for('add_url') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Add URL
    </a>
</div>

{% if sites %}
    {% for platform, site_data in sites.items() %}
        <div class="modern-card mb-3">
            <!-- Site Header (Clickable) -->
            <div class="site-header p-3 d-flex justify-content-between align-items-center" onclick="toggleSite(this)">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        {% if platform == 'youtube' %}
                            <i class="fab fa-youtube text-danger fa-2x"></i>
                        {% elif platform == 'vimeo' %}
                            <i class="fab fa-vimeo text-primary fa-2x"></i>
                        {% elif platform == 'dailymotion' %}
                            <i class="fab fa-dailymotion text-info fa-2x"></i>
                        {% else %}
                            <i class="fas fa-video text-secondary fa-2x"></i>
                        {% endif %}
                    </div>
                    <div>
                        <h5 class="mb-1">{{ platform.title() }}</h5>
                        <small class="text-muted">
                            <i class="fas fa-video me-1"></i>{{ site_data.count }} videos
                            {% if site_data.total_duration %}
                                • <i class="fas fa-clock me-1"></i>{{ site_data.total_duration }} total duration
                            {% endif %}
                        </small>
                    </div>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <div class="text-end">
                        <div class="small text-muted">Last updated</div>
                        <div class="small">{{ site_data.last_updated or 'Unknown' }}</div>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-danger"
                                onclick="event.stopPropagation(); deleteSite('{{ platform }}')"
                                title="Delete all videos from this site">
                            <i class="fas fa-trash"></i>
                        </button>
                        <i class="fas fa-chevron-down site-toggle-icon text-muted"></i>
                    </div>
                </div>
            </div>

            <!-- Site Content (Expandable) -->
            <div class="site-content">
                <div class="border-top p-3">
                    {% if site_data.videos %}
                        <!-- Site Statistics -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="text-primary">
                                        <i class="fas fa-video"></i>
                                    </div>
                                    <div class="fw-bold">{{ site_data.count }}</div>
                                    <small class="text-muted">Videos</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="text-success">
                                        <i class="fas fa-images"></i>
                                    </div>
                                    <div class="fw-bold">{{ site_data.total_screenshots or 0 }}</div>
                                    <small class="text-muted">Screenshots</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="text-info">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="fw-bold">{{ "{:,}".format(site_data.total_views or 0) }}</div>
                                    <small class="text-muted">Total Views</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="text-warning">
                                        <i class="fas fa-tags"></i>
                                    </div>
                                    <div class="fw-bold">{{ site_data.unique_tags or 0 }}</div>
                                    <small class="text-muted">Unique Tags</small>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Videos Preview -->
                        <h6 class="text-muted mb-2">
                            <i class="fas fa-clock me-1"></i>Recent Videos
                        </h6>
                        <div class="row">
                            {% for video in site_data.videos[:4] %}
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center p-2 bg-light rounded">
                                        <div class="flex-grow-1">
                                            <div class="fw-bold small">{{ video.title[:40] }}{% if video.title|length > 40 %}...{% endif %}</div>
                                            <div class="text-muted small">
                                                {% if video.uploader %}{{ video.uploader }} • {% endif %}
                                                {% if video.duration_seconds %}{{ video.duration_seconds }}s{% endif %}
                                            </div>
                                        </div>
                                        <div class="ms-2">
                                            <a href="{{ video.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Actions -->
                        <div class="d-flex justify-content-between mt-3 pt-3 border-top">
                            <a href="{{ url_for('search', category=platform) }}" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i>View All {{ site_data.count }} Videos
                            </a>
                            <button class="btn btn-outline-danger" onclick="deleteSite('{{ platform }}')">
                                <i class="fas fa-trash me-1"></i>Delete All Videos from {{ platform.title() }}
                            </button>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <div class="text-muted">
                                <i class="fas fa-video fa-2x mb-2"></i>
                                <p>No videos found for this platform.</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="modern-card text-center py-5">
        <div class="text-muted">
            <i class="fas fa-globe fa-3x mb-3"></i>
            <h4>No sites indexed yet</h4>
            <p>Add some video URLs to get started!</p>
            <a href="{{ url_for('add_url') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Add Your First URL
            </a>
        </div>
    </div>
{% endif %}

<script>
function deleteSite(platform) {
    if (confirm(`Are you sure you want to delete ALL videos from ${platform}? This action cannot be undone.`)) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ url_for("delete_site") }}';

        const platformInput = document.createElement('input');
        platformInput.type = 'hidden';
        platformInput.name = 'platform';
        platformInput.value = platform;

        form.appendChild(platformInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
