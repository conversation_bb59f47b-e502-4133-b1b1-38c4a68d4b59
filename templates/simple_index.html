{% extends "simple_base.html" %}

{% block title %}Home - Video Search Indexer{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="text-center mb-5">
    <h1 class="text-gradient display-4 mb-3">
        <i class="fas fa-video me-3"></i>Video Search Indexer
    </h1>
    <p class="lead text-muted">Manage and search your indexed video content locally with AI-powered analysis</p>
</div>

<!-- Quick Stats -->
<div class="row mb-5">
    <div class="col-md-3">
        <div class="modern-card text-center p-4">
            <div class="text-primary mb-3">
                <i class="fas fa-video fa-3x"></i>
            </div>
            <h3 class="mb-1">{{ total_videos }}</h3>
            <small class="text-muted">Total Videos</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-4">
            <div class="text-success mb-3">
                <i class="fas fa-folder fa-3x"></i>
            </div>
            <h3 class="mb-1">{{ categories|length }}</h3>
            <small class="text-muted">Categories</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-4">
            <div class="text-info mb-3">
                <i class="fas fa-images fa-3x"></i>
            </div>
            <h3 class="mb-1">{{ total_screenshots or 0 }}</h3>
            <small class="text-muted">Screenshots</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-4">
            <div class="text-warning mb-3">
                <i class="fas fa-robot fa-3x"></i>
            </div>
            <h3 class="mb-1">{{ ai_analysis_count or 0 }}</h3>
            <small class="text-muted">AI Analyzed</small>
        </div>
    </div>
</div>

<!-- Search Section -->
<div class="modern-card p-4 mb-4">
    <div class="row">
        <div class="col-md-8">
            <h4 class="mb-3">
                <i class="fas fa-search me-2"></i>Search Videos
            </h4>
            <form method="GET" id="searchForm">
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" class="form-control form-control-lg" name="q"
                               value="{{ query or '' }}" placeholder="Search for videos, tags, or content...">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select form-select-lg" name="category">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                                <option value="{{ category.name }}"
                                        {% if category.name == selected_category %}selected{% endif %}>
                                    {{ category.name }} ({{ category.count }})
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-md-4">
            <h6 class="text-muted mb-2">Quick Actions</h6>
            <div class="d-grid gap-2">
                <a href="{{ url_for('add_url') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Add URL
                </a>
                <a href="{{ url_for('view_sites') }}" class="btn btn-outline-primary">
                    <i class="fas fa-globe me-1"></i>Manage Sites
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
{% if query or selected_category %}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>
            <i class="fas fa-search-plus me-2"></i>Search Results
            {% if query %}for "{{ query }}"{% endif %}
            {% if selected_category %}in {{ selected_category }}{% endif %}
        </h4>
        <span class="badge bg-secondary fs-6">{{ videos|length }} videos found</span>
    </div>

    {% if videos %}
        <div class="mb-4">
            {% for video in videos[:5] %}
                <div class="modern-card video-card-compact expandable mb-3" onclick="toggleExpand(this)">
                    <!-- Compact View -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <div class="video-title">{{ video.title }}</div>
                            <div class="video-meta">
                                <span class="platform-badge">{{ video.platform }}</span>
                                {% if video.uploader %}
                                    <span><i class="fas fa-user me-1"></i>{{ video.uploader }}</span>
                                {% endif %}
                                {% if video.duration_seconds %}
                                    <span><i class="fas fa-clock me-1"></i>{{ video.duration_seconds }}s</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-success">{{ video.screenshot_count or 0 }} frames</span>
                            <i class="fas fa-chevron-down expand-icon text-muted"></i>
                        </div>
                    </div>

                    <!-- Expandable Content -->
                    <div class="expandable-content">
                        <div class="row">
                            <div class="col-md-6">
                                {% if video.description %}
                                    <h6 class="text-muted mb-2">
                                        <i class="fas fa-align-left me-1"></i>Description
                                    </h6>
                                    <p class="text-muted small mb-3">
                                        {{ video.description[:200] }}{% if video.description|length > 200 %}...{% endif %}
                                    </p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <!-- Tags -->
                                {% if video.tags %}
                                    <h6 class="text-muted mb-2">
                                        <i class="fas fa-tags me-1"></i>Tags
                                    </h6>
                                    <div class="mb-3">
                                        {% for tag in video.tags[:6] %}
                                            <span class="tag-clickable" onclick="event.stopPropagation(); searchByTag('{{ tag }}')">
                                                {{ tag }}
                                            </span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="d-flex justify-content-between pt-3 border-top">
                            {% if video.url %}
                                <a href="{{ video.url }}" target="_blank" class="btn btn-sm btn-primary" onclick="event.stopPropagation()">
                                    <i class="fas fa-external-link-alt me-1"></i>View Original
                                </a>
                            {% endif %}
                            <a href="{{ url_for('view_videos') }}" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation()">
                                <i class="fas fa-film me-1"></i>View All Videos
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}

            {% if videos|length > 5 %}
                <div class="text-center">
                    <a href="{{ url_for('view_videos') }}" class="btn btn-outline-primary">
                        <i class="fas fa-film me-1"></i>View All {{ videos|length }} Results
                    </a>
                </div>
            {% endif %}
        </div>
    {% else %}
        <div class="modern-card text-center py-5">
            <div class="text-muted">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h4>No videos found</h4>
                <p>Try adjusting your search terms or category filter.</p>
            </div>
        </div>
    {% endif %}
{% endif %}

<!-- Categories -->
<div class="modern-card p-4">
    <h4 class="mb-3">
        <i class="fas fa-folder me-2"></i>Browse Categories
    </h4>
    {% if categories %}
        <div class="row">
            {% for category in categories %}
                <div class="col-md-4 mb-2">
                    <a href="javascript:void(0)" onclick="searchByCategory('{{ category.name }}')"
                       class="text-decoration-none d-block p-2 rounded hover-bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="tag-clickable">{{ category.name }}</span>
                            <small class="text-muted">{{ category.count }} videos</small>
                        </div>
                    </a>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-3">
            <div class="text-muted">
                <i class="fas fa-folder-open fa-2x mb-2"></i>
                <p>No categories yet. Add some videos to get started!</p>
                <a href="{{ url_for('add_url') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Add Your First URL
                </a>
            </div>
        </div>
    {% endif %}
</div>

<script>
function searchByCategory(category) {
    document.querySelector('select[name="category"]').value = category;
    document.getElementById('searchForm').submit();
}
</script>
{% endblock %}
