{% extends "simple_base.html" %}

{% block title %}Add Video URL - Video Search Indexer{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">➕ Add Video URL</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="url" class="form-label">Video URL</label>
                        <input type="url" class="form-control" id="url" name="url" 
                               placeholder="https://youtube.com/watch?v=..." required>
                        <div class="form-text">
                            Supported platforms: YouTube, Vimeo, and other video sites
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">Add Video</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">📋 Instructions</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Add URL:</strong> Paste the video URL above and click "Add Video"</li>
                    <li><strong>Processing:</strong> The video will be queued for processing</li>
                    <li><strong>Indexing:</strong> Screenshots and metadata will be extracted automatically</li>
                    <li><strong>Search:</strong> Once processed, you can search for the video content</li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <strong>Note:</strong> Videos are processed in the background. Use the "Process Videos" button on the home page to start processing queued videos.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
