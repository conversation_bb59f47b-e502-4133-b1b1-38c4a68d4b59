{% extends "simple_base.html" %}

{% block title %}All Videos - Video Search Indexer{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="text-gradient">
        <i class="fas fa-film me-2"></i>All Videos
    </h1>
    <div>
        <a href="{{ url_for('add_url') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Add URL
        </a>
        <a href="{{ url_for('view_progress') }}" class="btn btn-outline-primary">
            <i class="fas fa-chart-line me-1"></i>Progress
        </a>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-primary mb-2">
                <i class="fas fa-video fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ videos|length }}</h3>
            <small class="text-muted">Total Videos</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-success mb-2">
                <i class="fas fa-check-circle fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ url_stats.by_status.completed or 0 }}</h3>
            <small class="text-muted">Completed</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-warning mb-2">
                <i class="fas fa-clock fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ url_stats.by_status.pending or 0 }}</h3>
            <small class="text-muted">Pending</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="modern-card text-center p-3">
            <div class="text-danger mb-2">
                <i class="fas fa-exclamation-circle fa-2x"></i>
            </div>
            <h3 class="mb-1">{{ url_stats.by_status.failed or 0 }}</h3>
            <small class="text-muted">Failed</small>
        </div>
    </div>
</div>

{% if videos %}
    <!-- Videos List -->
    <div class="mb-4">
        {% for video in videos %}
            <div class="modern-card video-card-compact expandable" onclick="toggleExpand(this)">
                <!-- Compact View -->
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <div class="video-title">{{ video.title }}</div>
                        <div class="video-meta">
                            <span class="platform-badge">{{ video.platform }}</span>
                            {% if video.uploader %}
                                <span><i class="fas fa-user me-1"></i>{{ video.uploader }}</span>
                            {% endif %}
                            {% if video.duration_seconds %}
                                <span><i class="fas fa-clock me-1"></i>{{ video.duration_seconds }}s</span>
                            {% endif %}
                            {% if video.view_count %}
                                <span><i class="fas fa-eye me-1"></i>{{ "{:,}".format(video.view_count|int) }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge bg-success">{{ video.screenshot_count or 0 }} frames</span>
                        <i class="fas fa-chevron-down expand-icon text-muted"></i>
                    </div>
                </div>

                <!-- Expandable Content -->
                <div class="expandable-content">
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Video Details -->
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-info-circle me-1"></i>Video Details
                            </h6>
                            <div class="mb-3">
                                {% if video.upload_date %}
                                    <div class="mb-1">
                                        <strong>Uploaded:</strong> {{ video.upload_date }}
                                    </div>
                                {% endif %}
                                {% if video.extraction_date %}
                                    <div class="mb-1">
                                        <strong>Processed:</strong> {{ video.extraction_date[:19] }}
                                    </div>
                                {% endif %}
                                <div class="mb-1">
                                    <strong>Status:</strong>
                                    <span class="badge bg-success">{{ video.status or 'completed' }}</span>
                                </div>
                            </div>

                            <!-- Description -->
                            {% if video.description %}
                                <h6 class="text-muted mb-2">
                                    <i class="fas fa-align-left me-1"></i>Description
                                </h6>
                                <p class="text-muted small mb-3">
                                    {{ video.description[:300] }}{% if video.description|length > 300 %}...{% endif %}
                                </p>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <!-- Tags -->
                            {% if video.tags %}
                                <h6 class="text-muted mb-2">
                                    <i class="fas fa-tags me-1"></i>Tags
                                </h6>
                                <div class="mb-3">
                                    {% for tag in video.tags[:12] %}
                                        <span class="tag-clickable" onclick="event.stopPropagation(); searchByTag('{{ tag }}')">
                                            {{ tag }}
                                        </span>
                                    {% endfor %}
                                    {% if video.tags|length > 12 %}
                                        <span class="text-muted small">+{{ video.tags|length - 12 }} more</span>
                                    {% endif %}
                                </div>
                            {% endif %}

                            <!-- Categories -->
                            {% if video.categories %}
                                <h6 class="text-muted mb-2">
                                    <i class="fas fa-folder me-1"></i>Categories
                                </h6>
                                <div class="mb-3">
                                    {% for category in video.categories %}
                                        <span class="tag-clickable" onclick="event.stopPropagation(); searchByTag('{{ category }}')">
                                            {{ category }}
                                        </span>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Enhanced AI Analysis (if available) -->
                            {% if video.ai_tags or video.video_summary %}
                                <h6 class="text-muted mb-2">
                                    <i class="fas fa-robot me-1"></i>AI Analysis
                                </h6>

                                <!-- Video Summary -->
                                {% if video.video_summary %}
                                    <div class="mb-3">
                                        <div class="ai-summary p-2 rounded" style="background-color: var(--light-bg); border-left: 3px solid var(--primary-color);">
                                            <small class="text-muted d-block mb-1">
                                                <i class="fas fa-eye me-1"></i>AI Summary
                                                {% if video.video_description_coverage %}
                                                    ({{ video.video_description_coverage }} frames analyzed)
                                                {% endif %}
                                            </small>
                                            <div class="text-dark">{{ video.video_summary }}</div>
                                        </div>
                                    </div>
                                {% endif %}

                                <!-- Key Themes -->
                                {% if video.video_key_themes and video.video_key_themes|length > 0 %}
                                    <div class="mb-2">
                                        <small class="text-muted d-block mb-1">
                                            <i class="fas fa-lightbulb me-1"></i>Key Themes
                                        </small>
                                        <div>
                                            {% for theme in video.video_key_themes[:6] %}
                                                <span class="tag-clickable theme-tag" onclick="event.stopPropagation(); searchByTag('{{ theme }}')"
                                                      style="background-color: var(--secondary-color); color: white;">
                                                    {{ theme }}
                                                </span>
                                            {% endfor %}
                                            {% if video.video_key_themes|length > 6 %}
                                                <span class="text-muted small">+{{ video.video_key_themes|length - 6 }} more</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endif %}

                                <!-- AI Tags -->
                                {% if video.ai_tags %}
                                    <div class="mb-3">
                                        <small class="text-muted d-block mb-1">
                                            <i class="fas fa-tags me-1"></i>AI Tags
                                        </small>
                                        <div>
                                            {% for ai_tag in video.ai_tags[:8] %}
                                                <span class="tag-clickable ai-tag" onclick="event.stopPropagation(); searchByTag('{{ ai_tag }}')">
                                                    {{ ai_tag }}
                                                </span>
                                            {% endfor %}
                                            {% if video.ai_tags|length > 8 %}
                                                <span class="text-muted small">+{{ video.ai_tags|length - 8 }} more</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="d-flex justify-content-between pt-3 border-top">
                        {% if video.url %}
                            <a href="{{ video.url }}" target="_blank" class="btn btn-sm btn-primary" onclick="event.stopPropagation()">
                                <i class="fas fa-external-link-alt me-1"></i>View Original
                            </a>
                        {% endif %}
                        <form method="POST" action="{{ url_for('delete_video', video_id=video.id) }}"
                              onsubmit="event.stopPropagation(); return confirm('Are you sure you want to delete this video and all its data?')" class="d-inline">
                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <h4 class="text-muted">No videos processed yet</h4>
        <p class="text-muted">Add some video URLs and process them to see them here!</p>
        <a href="{{ url_for('add_url') }}" class="btn btn-primary">➕ Add Your First Video</a>
    </div>
{% endif %}
{% endblock %}
