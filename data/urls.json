{"https://www.youtube.com/watch?v=dQw4w9WgXcQ": {"original_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "normalized_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-27T20:06:47.757463", "metadata": {}, "processing_attempts": 2, "last_error": null}, "https://www.youtube.com/watch?v=zLQLhARAYEs": {"original_url": "https://www.youtube.com/watch?v=zLQLhARAYEs", "normalized_url": "https://www.youtube.com/watch?v=zLQLhARAYEs", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-27T21:16:08.245961", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": null}, "https://www.youtube.com/watch?v=jNQXAC9IVRw": {"original_url": "https://www.youtube.com/watch?v=jNQXAC9IVRw", "normalized_url": "https://www.youtube.com/watch?v=jNQXAC9IVRw", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-27T21:24:35.159760", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": null}, "https://www.youtube.com/watch?v=9bZkp7q19f0": {"original_url": "https://www.youtube.com/watch?v=9bZkp7q19f0", "normalized_url": "https://www.youtube.com/watch?v=9bZkp7q19f0", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-27T21:27:54.461210", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": null}, "https://www.youtube.com/watch?v=kJQP7kiw5Fk": {"original_url": "https://www.youtube.com/watch?v=kJQP7kiw5Fk", "normalized_url": "https://www.youtube.com/watch?v=kJQP7kiw5Fk", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-27T21:29:27.772655", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": null}, "https://www.youtube.com/watch?v=I14R3JnNPLY": {"original_url": "https://www.youtube.com/watch?v=I14R3JnNPLY", "normalized_url": "https://www.youtube.com/watch?v=I14R3JnNPLY", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-27T22:32:54.217491", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": null}, "https://www.youtube.com/watch?v=JYDoeolagmU": {"original_url": "https://www.youtube.com/watch?v=JYDoeolagmU&pp=0gcJCccJAYcqIYzv", "normalized_url": "https://www.youtube.com/watch?v=JYDoeolagmU", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-27T23:41:15.394685", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": null}, "https://www.xvideos.com/video.ubhotuk9e37/": {"original_url": "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie", "normalized_url": "https://www.xvideos.com/video.ubhotuk9e37/", "domain": "xvideos", "platform": "xvideos", "status": "failed", "added_date": "2025-07-28T00:36:53.986212", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": "Metadata extraction failed: Failed to extract metadata with all methods"}, "https://www.xvideos.com/video70986409/hot_blonde_gets_fucked": {"original_url": "https://www.xvideos.com/video70986409/hot_blonde_gets_fucked", "normalized_url": "https://www.xvideos.com/video70986409/hot_blonde_gets_fucked", "domain": "xvideos", "platform": "xvideos", "status": "processing", "added_date": "2025-07-28T00:41:57.202198", "metadata": {"extract_screenshots": true}, "processing_attempts": 11, "last_error": null}, "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie": {"original_url": "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie", "normalized_url": "https://www.xvideos.com/video.ubhotuk9e37/white_girl_creampie", "domain": "xvideos", "platform": "xvideos", "status": "processing", "added_date": "2025-07-28T02:28:57.059158", "metadata": {"extract_screenshots": true}, "processing_attempts": 1, "last_error": null}, "https://www.youtube.com/watch?v=hM-GDQP4Kco": {"original_url": "https://www.youtube.com/watch?v=hM-GDQP4Kco", "normalized_url": "https://www.youtube.com/watch?v=hM-GDQP4Kco", "domain": "youtube", "platform": "youtube", "status": "completed", "added_date": "2025-07-28T03:10:04.219839", "metadata": {"extract_screenshots": true}, "processing_attempts": 2, "last_error": null}}