[{"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00008.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00009.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00010.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00011.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00012.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00013.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00020.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00021.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00022.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00023.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00024.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00025.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00014.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00015.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00016.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00017.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00018.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00019.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00001.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00002.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00003.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00004.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00005.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00006.jpg", "error": "name 'config' is not defined", "success": false}, {"image_path": "data/youtube/index/<PERSON>_<PERSON>_just_got_cancelled_hM-GDQP4Kco/screenshots/frame_00007.jpg", "error": "name 'config' is not defined", "success": false}]