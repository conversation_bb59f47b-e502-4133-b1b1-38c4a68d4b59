#!/usr/bin/env python3
"""
Debug script to test AI analyzer initialization
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_ai_analyzer():
    """Test AI analyzer initialization."""
    
    print("=== Testing AI Analyzer Initialization ===")
    
    try:
        print("1. Importing CLIPAnalyzer...")
        from ai_analyzer import CLIPAnalyzer
        print("✅ CLIPAnalyzer imported successfully")
        
        print("2. Creating CLIPAnalyzer instance...")
        analyzer = CLIPAnalyzer()
        print("✅ CLIPAnalyzer instance created")
        
        print("3. Checking if analyzer is available...")
        is_available = analyzer.is_available()
        print(f"   Available: {is_available}")
        
        if is_available:
            print("4. Testing BLIP captioner...")
            captioner_available = analyzer.captioner.is_available()
            print(f"   BLIP Captioner available: {captioner_available}")
            
            print("5. Testing with a sample image...")
            # Find a sample screenshot
            sample_image = None
            for video_dir in Path('data/youtube/index').iterdir():
                if video_dir.is_dir():
                    screenshots_dir = video_dir / 'screenshots'
                    if screenshots_dir.exists():
                        for img_file in screenshots_dir.glob('frame_*.jpg'):
                            sample_image = img_file
                            break
                    if sample_image:
                        break
            
            if sample_image:
                print(f"   Using sample image: {sample_image}")
                
                # Test CLIP analysis
                result = analyzer.analyze_image(sample_image)
                if result.get('success'):
                    print("   ✅ CLIP analysis successful")
                    print(f"   Embedding shape: {len(result.get('embedding', []))}")
                else:
                    print(f"   ❌ CLIP analysis failed: {result.get('error')}")
                
                # Test BLIP captioning if available
                if captioner_available:
                    try:
                        caption = analyzer.captioner.generate_caption(sample_image)
                        print(f"   ✅ BLIP caption: {caption}")
                    except Exception as e:
                        print(f"   ❌ BLIP captioning failed: {e}")
                else:
                    print("   ⚠️  BLIP captioner not available")
            else:
                print("   ⚠️  No sample images found")
        else:
            print("❌ AI analyzer not available")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_ai_analyzer()
